package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"time"
)

func main() {
	fmt.Println("🎥 Simple FFmpeg Camera Test")
	fmt.Println("============================")
	
	// 检查FFmpeg是否安装
	if err := checkFFmpeg(); err != nil {
		log.Fatalf("[ERROR] %v", err)
	}
	
	// 测试1: 列出可用设备
	fmt.Println("\n📹 Step 1: Listing available cameras...")
	if err := listCameras(); err != nil {
		log.Printf("[ERROR] Failed to list cameras: %v", err)
	}
	
	// 测试2: 捕获10秒视频并保存为文件
	fmt.Println("\n🎬 Step 2: Capturing 10 seconds of video...")
	if err := captureVideo(); err != nil {
		log.Printf("[ERROR] Failed to capture video: %v", err)
	} else {
		fmt.Println("✅ Video capture completed!")
		fmt.Println("📁 Check 'test_output.mp4' file")
	}
	
	// 测试3: 捕获单帧图片
	fmt.Println("\n📸 Step 3: Capturing a single frame...")
	if err := captureFrame(); err != nil {
		log.Printf("[ERROR] Failed to capture frame: %v", err)
	} else {
		fmt.Println("✅ Frame capture completed!")
		fmt.Println("📁 Check 'test_frame.jpg' file")
	}
	
	fmt.Println("\n🎉 FFmpeg camera test completed!")
	fmt.Println("If you see the output files, your camera is working with FFmpeg.")
}

func checkFFmpeg() error {
	fmt.Println("[INFO] 🔍 Checking FFmpeg installation...")
	
	if _, err := exec.LookPath("ffmpeg"); err != nil {
		return fmt.Errorf("FFmpeg not found. Please install FFmpeg first:\n" +
			"  macOS: brew install ffmpeg\n" +
			"  Ubuntu: sudo apt install ffmpeg\n" +
			"  Windows: Download from https://ffmpeg.org/")
	}
	
	fmt.Println("[INFO] ✅ FFmpeg found")
	return nil
}

func listCameras() error {
	fmt.Println("[INFO] Detecting cameras...")
	
	// macOS: 使用 avfoundation
	cmd := exec.Command("ffmpeg", "-f", "avfoundation", "-list_devices", "true", "-i", "")
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		// 尝试 Linux v4l2
		fmt.Println("[INFO] Trying Linux v4l2...")
		cmd = exec.Command("ffmpeg", "-f", "v4l2", "-list_devices", "true", "-i", "")
		output, err = cmd.CombinedOutput()
		
		if err != nil {
			// 尝试 Windows DirectShow
			fmt.Println("[INFO] Trying Windows DirectShow...")
			cmd = exec.Command("ffmpeg", "-f", "dshow", "-list_devices", "true", "-i", "dummy")
			output, err = cmd.CombinedOutput()
		}
	}
	
	fmt.Printf("📋 Device list output:\n%s\n", string(output))
	return nil
}

func captureVideo() error {
	fmt.Println("[INFO] Starting video capture...")
	
	// 删除旧文件
	os.Remove("test_output.mp4")
	
	// 构建FFmpeg命令 - 尝试不同的输入格式
	var cmd *exec.Cmd
	
	// macOS
	cmd = exec.Command("ffmpeg",
		"-f", "avfoundation",
		"-framerate", "15",
		"-video_size", "640x480",
		"-i", "0", // 默认摄像头
		"-t", "10", // 录制10秒
		"-c:v", "libx264",
		"-preset", "ultrafast",
		"-y", // 覆盖输出文件
		"test_output.mp4")
	
	fmt.Println("[DEBUG] Running: ffmpeg -f avfoundation -framerate 15 -video_size 640x480 -i 0 -t 10 -c:v libx264 -preset ultrafast -y test_output.mp4")
	
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		fmt.Printf("[DEBUG] macOS attempt failed: %v\n", err)
		fmt.Printf("[DEBUG] Output: %s\n", string(output))
		
		// 尝试 Linux
		fmt.Println("[INFO] Trying Linux v4l2...")
		cmd = exec.Command("ffmpeg",
			"-f", "v4l2",
			"-framerate", "15",
			"-video_size", "640x480",
			"-i", "/dev/video0",
			"-t", "10",
			"-c:v", "libx264",
			"-preset", "ultrafast",
			"-y",
			"test_output.mp4")
		
		output, err = cmd.CombinedOutput()
		
		if err != nil {
			fmt.Printf("[DEBUG] Linux attempt failed: %v\n", err)
			fmt.Printf("[DEBUG] Output: %s\n", string(output))
			return fmt.Errorf("failed to capture video on both macOS and Linux")
		}
	}
	
	fmt.Printf("[DEBUG] FFmpeg output: %s\n", string(output))
	
	// 检查文件是否创建
	if _, err := os.Stat("test_output.mp4"); os.IsNotExist(err) {
		return fmt.Errorf("output file was not created")
	}
	
	return nil
}

func captureFrame() error {
	fmt.Println("[INFO] Capturing single frame...")
	
	// 删除旧文件
	os.Remove("test_frame.jpg")
	
	var cmd *exec.Cmd
	
	// macOS
	cmd = exec.Command("ffmpeg",
		"-f", "avfoundation",
		"-video_size", "640x480",
		"-i", "0",
		"-frames:v", "1", // 只捕获一帧
		"-y",
		"test_frame.jpg")
	
	fmt.Println("[DEBUG] Running: ffmpeg -f avfoundation -video_size 640x480 -i 0 -frames:v 1 -y test_frame.jpg")
	
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		fmt.Printf("[DEBUG] macOS attempt failed: %v\n", err)
		fmt.Printf("[DEBUG] Output: %s\n", string(output))
		
		// 尝试 Linux
		fmt.Println("[INFO] Trying Linux v4l2...")
		cmd = exec.Command("ffmpeg",
			"-f", "v4l2",
			"-video_size", "640x480",
			"-i", "/dev/video0",
			"-frames:v", "1",
			"-y",
			"test_frame.jpg")
		
		output, err = cmd.CombinedOutput()
		
		if err != nil {
			fmt.Printf("[DEBUG] Linux attempt failed: %v\n", err)
			fmt.Printf("[DEBUG] Output: %s\n", string(output))
			return fmt.Errorf("failed to capture frame on both macOS and Linux")
		}
	}
	
	fmt.Printf("[DEBUG] FFmpeg output: %s\n", string(output))
	
	// 检查文件是否创建
	if _, err := os.Stat("test_frame.jpg"); os.IsNotExist(err) {
		return fmt.Errorf("output file was not created")
	}
	
	return nil
}
