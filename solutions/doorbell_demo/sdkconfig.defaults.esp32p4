# Flash setting
CONFIG_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y

CONFIG_SPIRAM_SPEED_200M=y

# If you use serial JTAG turn on this option
#CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG=y

# Use camera SC2336
CONFIG_CAMERA_SC2336=y
CONFIG_CAMERA_SC2336_MIPI_RAW10_1920x1080_25FPS_2_LANE=y

# Enable ISP pipelines
CONFIG_ESP_VIDEO_ENABLE_ISP_PIPELINE_CONTROLLER=y

# Set Slave target type
CONFIG_IDF_SLAVE_TARGET="esp32c6"
CONFIG_SLAVE_IDF_TARGET_ESP32C6=y

# Enable following configuration if support C5 Slave, make sure GPIO matched
# CONFIG_SLAVE_IDF_TARGET_ESP32C5=y
# CONFIG_ESP_HOSTED_SPI_HD_HOST_INTERFACE=y
# CONFIG_ESP_SPI_HD_GPIO_CS=4
# CONFIG_ESP_SPI_HD_GPIO_CLK=5
# CONFIG_ESP_SPI_HD_GPIO_D0=20
# CONFIG_ESP_SPI_HD_GPIO_D1=21
# CONFIG_ESP_SPI_HD_GPIO_D2=22
# CONFIG_ESP_SPI_HD_GPIO_D3=23
# CONFIG_ESP_SPI_HD_GPIO_DATA_READY=32
# CONFIG_ESP_SPI_HD_GPIO_RESET_SLAVE=33
# CONFIG_ESP_SPI_HD_FREQ_ESP32XX=40
# CONFIG_ESP_HOSTED_SPI_HD_PRIV_INTERFACE_4_DATA_LINES=y

CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=16
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=64
CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM=64
CONFIG_ESP_WIFI_AMPDU_TX_ENABLED=y
CONFIG_ESP_WIFI_TX_BA_WIN=32
CONFIG_ESP_WIFI_AMPDU_RX_ENABLED=y
CONFIG_ESP_WIFI_RX_BA_WIN=32

CONFIG_LWIP_TCP_SND_BUF_DEFAULT=65534
CONFIG_LWIP_TCP_WND_DEFAULT=65534
CONFIG_LWIP_TCP_RECVMBOX_SIZE=64
CONFIG_LWIP_UDP_RECVMBOX_SIZE=64
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=64

CONFIG_LWIP_TCP_SACK_OUT=y