/*
 * 简化的EK79007测试程序
 * 避免复杂操作，专注于基本显示功能
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "codec_init.h"
#include "esp_lcd_panel_ops.h"

static const char *TAG = "SIMPLE_EK79007_TEST";

// 根据官方文档的GPIO配置
#define RST_LCD_GPIO    (27)    // 复位引脚
#define PWM_LCD_GPIO    (26)    // 背光控制引脚

// 简单的GPIO控制测试
void simple_gpio_test(void)
{
    ESP_LOGI(TAG, "=== 简单GPIO控制测试 ===");

    // 1. 配置复位引脚 GPIO27
    ESP_LOGI(TAG, "配置复位引脚 GPIO %d", RST_LCD_GPIO);
    gpio_config_t rst_config = {
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << RST_LCD_GPIO),
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    esp_err_t ret = gpio_config(&rst_config);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ 复位引脚配置成功");
    } else {
        ESP_LOGE(TAG, "❌ 复位引脚配置失败: %s", esp_err_to_name(ret));
        return;
    }

    // 2. 配置背光引脚 GPIO26
    ESP_LOGI(TAG, "配置背光引脚 GPIO %d", PWM_LCD_GPIO);
    gpio_config_t pwm_config = {
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << PWM_LCD_GPIO),
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    ret = gpio_config(&pwm_config);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ 背光引脚配置成功");
    } else {
        ESP_LOGE(TAG, "❌ 背光引脚配置失败: %s", esp_err_to_name(ret));
        return;
    }

    // 3. 复位序列
    ESP_LOGI(TAG, "执行LCD复位序列...");
    gpio_set_level(RST_LCD_GPIO, 0);  // 拉低复位
    vTaskDelay(pdMS_TO_TICKS(100));
    gpio_set_level(RST_LCD_GPIO, 1);  // 释放复位
    vTaskDelay(pdMS_TO_TICKS(100));
    ESP_LOGI(TAG, "✅ LCD复位序列完成");

    // 4. 开启背光
    ESP_LOGI(TAG, "开启LCD背光...");
    gpio_set_level(PWM_LCD_GPIO, 1);  // 开启背光
    ESP_LOGI(TAG, "✅ LCD背光已开启");

    // 5. 测试背光开关
    ESP_LOGI(TAG, "测试背光开关...");
    for (int i = 0; i < 3; i++) {
        ESP_LOGI(TAG, "关闭背光");
        gpio_set_level(PWM_LCD_GPIO, 0);
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        ESP_LOGI(TAG, "开启背光");
        gpio_set_level(PWM_LCD_GPIO, 1);
        vTaskDelay(pdMS_TO_TICKS(1000));
    }

    ESP_LOGI(TAG, "=== GPIO控制测试完成 ===");
}

// 使用现有的LCD句柄进行简单测试
void simple_lcd_draw_test(void)
{
    ESP_LOGI(TAG, "=== 简单LCD绘制测试 ===");

    // 获取现有的LCD句柄
    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    if (lcd_handle == NULL) {
        ESP_LOGE(TAG, "无法获取LCD句柄，请先初始化LCD");
        return;
    }

    ESP_LOGI(TAG, "LCD句柄获取成功: %p", lcd_handle);

    // 分配小的测试缓冲区
    const int width = 100;
    const int height = 100;
    const int buffer_size = width * height * 2; // RGB565
    
    uint16_t *buffer = (uint16_t *)malloc(buffer_size);
    if (buffer == NULL) {
        ESP_LOGE(TAG, "内存分配失败");
        return;
    }

    ESP_LOGI(TAG, "缓冲区分配成功: %d bytes", buffer_size);

    // 测试不同颜色
    uint16_t colors[] = {
        0xF800, // 红色
        0x07E0, // 绿色
        0x001F, // 蓝色
        0xFFFF, // 白色
    };
    
    const char* color_names[] = {
        "红色", "绿色", "蓝色", "白色"
    };

    for (int c = 0; c < 4; c++) {
        // 填充颜色
        for (int i = 0; i < width * height; i++) {
            buffer[i] = colors[c];
        }

        int x = c * 200;
        int y = 200;

        ESP_LOGI(TAG, "绘制%s方块 (%d,%d)", color_names[c], x, y);

        esp_err_t ret = esp_lcd_panel_draw_bitmap(lcd_handle, x, y, x + width, y + height, buffer);
        
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "✅ %s方块绘制成功", color_names[c]);
        } else {
            ESP_LOGE(TAG, "❌ %s方块绘制失败: %s", color_names[c], esp_err_to_name(ret));
        }

        vTaskDelay(pdMS_TO_TICKS(1000));
    }

    free(buffer);
    ESP_LOGI(TAG, "=== LCD绘制测试完成 ===");
}

// 简单EK79007测试任务
static void simple_ek79007_test_task(void *param)
{
    ESP_LOGI(TAG, "=== 简化EK79007测试开始 ===");

    // 1. 先测试GPIO控制
    simple_gpio_test();

    // 等待5秒观察背光效果
    ESP_LOGI(TAG, "等待5秒观察背光效果...");
    vTaskDelay(pdMS_TO_TICKS(5000));

    // 2. 再测试LCD绘制（使用现有的LCD初始化）
    simple_lcd_draw_test();

    ESP_LOGI(TAG, "=== 简化EK79007测试完成 ===");
    ESP_LOGI(TAG, "如果看到背光开关和彩色方块，说明配置正确！");
    
    vTaskDelete(NULL);
}

// 启动简化EK79007测试
void start_simple_ek79007_test(void)
{
    xTaskCreate(simple_ek79007_test_task, "simple_ek79007_test", 4096, NULL, 5, NULL);
}
