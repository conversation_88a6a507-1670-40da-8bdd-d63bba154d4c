/* Call UI Implementation
 * 
 * 接听界面实现 - 显示来电信息和接听/拒绝按钮
 */

#include "call_ui.h"
#include "ui_manager.h"
#include "esp_log.h"
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "CALL_UI";

// 接听界面状态
typedef struct {
    bool initialized;
    esp_lcd_panel_handle_t lcd_handle;
    int screen_width;
    int screen_height;
    uint16_t bg_color;
    uint16_t text_color;
    uint16_t accept_color;
    uint16_t reject_color;
} call_ui_t;

static call_ui_t s_call_ui = {0};

// 简单的字体数据 (8x16像素，ASCII字符)
static const uint8_t font_8x16[][16] = {
    // 'A'
    {0x18, 0x3C, 0x66, 0x66, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66},
    // 'C'
    {0x3C, 0x66, 0x66, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x66, 0x66, 0x66, 0x3C},
    // 'E'
    {0x7E, 0x60, 0x60, 0x60, 0x60, 0x60, 0x7C, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x7E},
    // 'I'
    {0x3C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x3C},
    // 'L'
    {0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x7E},
    // 'N'
    {0x66, 0x66, 0x76, 0x76, 0x6E, 0x6E, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66},
    // 'O'
    {0x3C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C},
    // 'R'
    {0x7C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x7C, 0x78, 0x6C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66},
    // 'T'
    {0x7E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18},
    // ' '
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
};

// 获取字符对应的字体索引
static int get_font_index(char c)
{
    switch (c) {
        case 'A': case 'a': return 0;
        case 'C': case 'c': return 1;
        case 'E': case 'e': return 2;
        case 'I': case 'i': return 3;
        case 'L': case 'l': return 4;
        case 'N': case 'n': return 5;
        case 'O': case 'o': return 6;
        case 'R': case 'r': return 7;
        case 'T': case 't': return 8;
        default: return 9; // 空格
    }
}

// 使用全局安全绘制函数
static esp_err_t safe_lcd_draw_bitmap(int x_start, int y_start, int x_end, int y_end, const void *color_data)
{
    return ui_safe_lcd_draw_bitmap(x_start, y_start, x_end, y_end, color_data);
}

// 在指定位置绘制字符
static void draw_char(int x, int y, char c, uint16_t color)
{
    if (!s_call_ui.initialized) {
        return;
    }

    int font_idx = get_font_index(c);
    const uint8_t *font_data = font_8x16[font_idx];

    // 创建字符缓冲区
    uint16_t char_buffer[8 * 16];

    for (int row = 0; row < 16; row++) {
        uint8_t line = font_data[row];
        for (int col = 0; col < 8; col++) {
            if (line & (0x80 >> col)) {
                char_buffer[row * 8 + col] = color;
            } else {
                char_buffer[row * 8 + col] = s_call_ui.bg_color;
            }
        }
    }

    // 安全绘制到LCD，如果失败就跳过
    esp_err_t ret = safe_lcd_draw_bitmap(x, y, x + 8, y + 16, char_buffer);
    if (ret != ESP_OK) {
        // 绘制失败，添加延时
        vTaskDelay(pdMS_TO_TICKS(20));
    }
}

// 在指定位置绘制字符串
static void draw_string(int x, int y, const char *str, uint16_t color)
{
    if (!s_call_ui.initialized || str == NULL) {
        return;
    }

    int pos_x = x;
    for (int i = 0; str[i] != '\0'; i++) {
        draw_char(pos_x, y, str[i], color);
        pos_x += 8; // 字符宽度
        // 在字符之间添加小延时，避免LCD过载
        vTaskDelay(pdMS_TO_TICKS(5));
    }
}

// 绘制矩形
static void draw_rect(int x, int y, int width, int height, uint16_t color)
{
    if (!s_call_ui.initialized) {
        return;
    }
    
    uint16_t *buffer = (uint16_t *)malloc(width * height * sizeof(uint16_t));
    if (buffer == NULL) {
        ESP_LOGE(TAG, "内存分配失败");
        return;
    }
    
    // 填充颜色
    for (int i = 0; i < width * height; i++) {
        buffer[i] = color;
    }
    
    // 安全绘制到LCD
    safe_lcd_draw_bitmap(x, y, x + width, y + height, buffer);
    
    free(buffer);
}

// 清除屏幕
static void clear_screen(void)
{
    if (!s_call_ui.initialized) {
        return;
    }
    
    // 创建背景色缓冲区
    const int buffer_size = 100 * 100; // 分块清除
    uint16_t *buffer = (uint16_t *)malloc(buffer_size * sizeof(uint16_t));
    if (buffer == NULL) {
        ESP_LOGE(TAG, "内存分配失败");
        return;
    }
    
    // 填充背景色
    for (int i = 0; i < buffer_size; i++) {
        buffer[i] = s_call_ui.bg_color;
    }
    
    // 分块清除屏幕
    for (int y = 0; y < s_call_ui.screen_height; y += 100) {
        for (int x = 0; x < s_call_ui.screen_width; x += 100) {
            int w = (x + 100 > s_call_ui.screen_width) ? (s_call_ui.screen_width - x) : 100;
            int h = (y + 100 > s_call_ui.screen_height) ? (s_call_ui.screen_height - y) : 100;
            safe_lcd_draw_bitmap(x, y, x + w, y + h, buffer);
            // 添加小延时，避免LCD过载
            vTaskDelay(pdMS_TO_TICKS(10));
        }
    }
    
    free(buffer);
}

esp_err_t call_ui_init(const call_ui_config_t *config)
{
    if (config == NULL || config->lcd_handle == NULL) {
        ESP_LOGE(TAG, "无效的配置参数");
        return ESP_ERR_INVALID_ARG;
    }
    
    if (s_call_ui.initialized) {
        ESP_LOGW(TAG, "接听界面已经初始化");
        return ESP_OK;
    }
    
    // 初始化接听界面状态
    memset(&s_call_ui, 0, sizeof(call_ui_t));
    s_call_ui.lcd_handle = config->lcd_handle;
    s_call_ui.screen_width = config->screen_width;
    s_call_ui.screen_height = config->screen_height;
    s_call_ui.bg_color = config->bg_color;
    s_call_ui.text_color = config->text_color;
    s_call_ui.accept_color = config->accept_color;
    s_call_ui.reject_color = config->reject_color;
    s_call_ui.initialized = true;
    
    ESP_LOGI(TAG, "接听界面初始化成功 (分辨率: %dx%d)", config->screen_width, config->screen_height);
    
    return ESP_OK;
}

void call_ui_show_incoming(const char *caller_info)
{
    if (!s_call_ui.initialized) {
        ESP_LOGW(TAG, "接听界面未初始化");
        return;
    }

    ESP_LOGI(TAG, "显示来电界面: %s", caller_info ? caller_info : "Unknown");

    // 清除屏幕
    clear_screen();

    // 显示"来电"标题
    const char *title = "INCOMING CALL";
    int title_x = (s_call_ui.screen_width - strlen(title) * 8) / 2;
    int title_y = 100;
    draw_string(title_x, title_y, title, s_call_ui.text_color);

    // 显示来电者信息
    if (caller_info != NULL) {
        int caller_x = (s_call_ui.screen_width - strlen(caller_info) * 8) / 2;
        int caller_y = 200;
        draw_string(caller_x, caller_y, caller_info, s_call_ui.text_color);
    }

    // 绘制接听按钮 (绿色)
    int button_width = 120;
    int button_height = 60;
    int accept_x = s_call_ui.screen_width / 4 - button_width / 2;
    int button_y = 350;
    draw_rect(accept_x, button_y, button_width, button_height, s_call_ui.accept_color);

    // 接听按钮文字
    const char *accept_text = "ACCEPT";
    int accept_text_x = accept_x + (button_width - strlen(accept_text) * 8) / 2;
    int accept_text_y = button_y + (button_height - 16) / 2;
    draw_string(accept_text_x, accept_text_y, accept_text, s_call_ui.text_color);

    // 绘制拒绝按钮 (红色)
    int reject_x = s_call_ui.screen_width * 3 / 4 - button_width / 2;
    draw_rect(reject_x, button_y, button_width, button_height, s_call_ui.reject_color);

    // 拒绝按钮文字
    const char *reject_text = "REJECT";
    int reject_text_x = reject_x + (button_width - strlen(reject_text) * 8) / 2;
    int reject_text_y = button_y + (button_height - 16) / 2;
    draw_string(reject_text_x, reject_text_y, reject_text, s_call_ui.text_color);

    // 显示操作提示
    const char *hint = "Press button to answer";
    int hint_x = (s_call_ui.screen_width - strlen(hint) * 8) / 2;
    int hint_y = 450;
    draw_string(hint_x, hint_y, hint, s_call_ui.text_color);
}

void call_ui_show_in_call(void)
{
    if (!s_call_ui.initialized) {
        ESP_LOGW(TAG, "接听界面未初始化");
        return;
    }

    ESP_LOGI(TAG, "显示通话中界面");

    // 清除屏幕
    clear_screen();

    // 显示"通话中"标题
    const char *title = "IN CALL";
    int title_x = (s_call_ui.screen_width - strlen(title) * 8) / 2;
    int title_y = 200;
    draw_string(title_x, title_y, title, s_call_ui.text_color);

    // 显示挂断提示
    const char *hint = "Press button to hang up";
    int hint_x = (s_call_ui.screen_width - strlen(hint) * 8) / 2;
    int hint_y = 350;
    draw_string(hint_x, hint_y, hint, s_call_ui.text_color);
}

void call_ui_show_call_ended(void)
{
    if (!s_call_ui.initialized) {
        ESP_LOGW(TAG, "接听界面未初始化");
        return;
    }

    ESP_LOGI(TAG, "显示通话结束界面");

    // 清除屏幕
    clear_screen();

    // 显示"通话结束"标题
    const char *title = "CALL ENDED";
    int title_x = (s_call_ui.screen_width - strlen(title) * 8) / 2;
    int title_y = 250;
    draw_string(title_x, title_y, title, s_call_ui.text_color);
}

void call_ui_clear(void)
{
    if (!s_call_ui.initialized) {
        return;
    }
    
    ESP_LOGI(TAG, "清除接听界面");
    clear_screen();
}

bool call_ui_is_initialized(void)
{
    return s_call_ui.initialized;
}
