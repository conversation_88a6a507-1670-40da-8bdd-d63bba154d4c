/*
 * 多GPIO背光测试程序头文件
 */

#ifndef MULTI_GPIO_TEST_H
#define MULTI_GPIO_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 启动多GPIO背光测试
 * 
 * 这个函数会自动测试多个可能的背光GPIO引脚：
 * - GPIO 22 (官方demo使用)
 * - GPIO 26 (用户发现)
 * - GPIO 45-52, 54 (其他可能的引脚)
 * 
 * 每个GPIO测试时会：
 * 1. 初始化EK79007面板
 * 2. 绘制红色全屏
 * 3. 等待10秒观察效果
 * 4. 清理资源，测试下一个GPIO
 * 
 * 如果某个GPIO测试时屏幕显示红色，说明找到了正确的背光引脚。
 */
void start_multi_gpio_test(void);

#ifdef __cplusplus
}
#endif

#endif // MULTI_GPIO_TEST_H
