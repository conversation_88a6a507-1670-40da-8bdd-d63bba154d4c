/*
 * 简化的LCD测试程序
 * 直接使用现有的media_sys中的LCD渲染器
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "media_sys.h"

static const char *TAG = "SIMPLE_LCD_TEST";

// RGB565颜色定义
#define RGB565_RED      0xF800
#define RGB565_GREEN    0x07E0
#define RGB565_BLUE     0x001F
#define RGB565_WHITE    0xFFFF
#define RGB565_BLACK    0x0000

// 生成纯色图案
static void generate_solid_color(uint16_t *buffer, int width, int height, uint16_t color)
{
    for (int i = 0; i < width * height; i++) {
        buffer[i] = color;
    }
}

// 生成条纹图案
static void generate_stripes(uint16_t *buffer, int width, int height)
{
    uint16_t colors[] = {RGB565_RED, RGB565_GREEN, RGB565_BLUE, RGB565_WHITE};
    int stripe_height = height / 4;
    
    for (int y = 0; y < height; y++) {
        uint16_t color = colors[y / stripe_height];
        if (y / stripe_height >= 4) color = RGB565_BLACK;
        
        for (int x = 0; x < width; x++) {
            buffer[y * width + x] = color;
        }
    }
}

// 简单的LCD测试
void simple_lcd_test(void)
{
    ESP_LOGI(TAG, "=== 简单LCD测试开始 ===");

    // 检查media_sys是否已初始化
    if (!is_media_sys_ready()) {
        ESP_LOGE(TAG, "Media system not ready, please call media_sys_buildup() first");
        return;
    }

    // 分配图像缓冲区
    const int width = 1024;
    const int height = 600;
    const int buffer_size = width * height * 2; // RGB565 = 2 bytes per pixel
    
    uint16_t *image_buffer = (uint16_t *)malloc(buffer_size);
    if (image_buffer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate image buffer");
        return;
    }

    ESP_LOGI(TAG, "图像缓冲区分配成功: %d bytes", buffer_size);

    // 测试不同颜色
    const char* test_names[] = {"红色", "绿色", "蓝色", "白色", "条纹"};
    uint16_t test_colors[] = {RGB565_RED, RGB565_GREEN, RGB565_BLUE, RGB565_WHITE, 0};

    for (int i = 0; i < 5; i++) {
        ESP_LOGI(TAG, "测试图案 %d: %s", i, test_names[i]);

        // 生成测试图案
        if (i == 4) {
            generate_stripes(image_buffer, width, height);
        } else {
            generate_solid_color(image_buffer, width, height, test_colors[i]);
        }

        // 尝试直接发送到播放器
        int ret = test_send_video_frame((uint8_t*)image_buffer, buffer_size);
        if (ret == 0) {
            ESP_LOGI(TAG, "视频帧发送成功");
        } else {
            ESP_LOGE(TAG, "视频帧发送失败: %d", ret);
        }

        // 等待3秒
        vTaskDelay(pdMS_TO_TICKS(3000));
    }

    // 清理资源
    free(image_buffer);
    ESP_LOGI(TAG, "=== 简单LCD测试完成 ===");
}

// 异步启动LCD测试
void start_simple_lcd_test(void)
{
    // 创建测试任务
    xTaskCreate([](void *param) {
        simple_lcd_test();
        vTaskDelete(NULL);
    }, "simple_lcd_test", 4096, NULL, 5, NULL);
}
