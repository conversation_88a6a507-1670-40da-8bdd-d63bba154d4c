// VideoCall Demo UI for ESP32
// 视频通话演示界面

import { VerticalBox, HorizontalBox, Button } from "std-widgets.slint";

// 主应用窗口
export component VideoCallWindow inherits Window {
    // 窗口属性
    width: 1024px;
    height: 600px;
    title: "ESP32 Video Call";

    // 状态属性
    in-out property <string> current-time: "00:00";
    in-out property <string> current-date: "2024-01-01";
    in-out property <string> caller-info: "Unknown Caller";
    in-out property <int> ui-state: 0; // 0: screensaver, 1: incoming, 2: in-call, 3: ended

    // 回调函数
    callback accept-call();
    callback reject-call();
    callback hang-up();

    // 主布局
    Rectangle {
        background: #000000;

        // 屏保界面
        if ui-state == 0: VerticalBox {
            alignment: center;
            spacing: 20px;

            // 标题
            Text {
                text: "ESP32 Video Call";
                font-size: 32px;
                color: #ffffff;
                horizontal-alignment: center;
            }

            // 时间显示
            Text {
                text: current-time;
                font-size: 48px;
                color: #00ff00;
                horizontal-alignment: center;
                font-weight: 700;
            }

            // 日期显示
            Text {
                text: current-date;
                font-size: 24px;
                color: #ffffff;
                horizontal-alignment: center;
            }

            // 状态指示器
            Rectangle {
                width: 200px;
                height: 100px;
                background: #004400;
                border-radius: 10px;

                Text {
                    text: "Ready";
                    color: #ffffff;
                    font-size: 20px;
                    horizontal-alignment: center;
                    vertical-alignment: center;
                }
            }
        }

        // 来电界面
        if ui-state == 1: Rectangle {
            background: #001144;

            VerticalBox {
                alignment: center;
                spacing: 30px;

                // 来电标题
                Text {
                    text: "Incoming Call";
                    font-size: 36px;
                    color: #ffffff;
                    horizontal-alignment: center;
                    font-weight: 700;
                }

                // 来电者信息
                Text {
                    text: caller-info;
                    font-size: 28px;
                    color: #ffffff;
                    horizontal-alignment: center;
                }

                // 按钮区域
                HorizontalBox {
                    spacing: 100px;
                    alignment: center;

                    // 接听按钮
                    Button {
                        text: "Accept";
                        width: 150px;
                        height: 80px;
                        clicked => { accept-call(); }
                    }

                    // 拒绝按钮
                    Button {
                        text: "Reject";
                        width: 150px;
                        height: 80px;
                        clicked => { reject-call(); }
                    }
                }

                // 操作提示
                Text {
                    text: "Press button to answer";
                    font-size: 18px;
                    color: #aaaaaa;
                    horizontal-alignment: center;
                }
            }
        }

        // 通话中界面
        if ui-state == 2: Rectangle {
            background: #002200;

            VerticalBox {
                alignment: center;
                spacing: 40px;

                // 通话中标题
                Text {
                    text: "In Call";
                    font-size: 40px;
                    color: #ffffff;
                    horizontal-alignment: center;
                    font-weight: 700;
                }

                // 通话状态指示器
                Rectangle {
                    width: 400px;
                    height: 200px;
                    background: #004400;
                    border-radius: 20px;

                    VerticalBox {
                        alignment: center;
                        spacing: 10px;

                        Text {
                            text: "Connected";
                            color: #00ff00;
                            font-size: 28px;
                            horizontal-alignment: center;
                            font-weight: 700;
                        }

                        Text {
                            text: "Video Call Active";
                            color: #ffffff;
                            font-size: 18px;
                            horizontal-alignment: center;
                        }
                    }
                }

                // 挂断按钮
                Button {
                    text: "Hang Up";
                    width: 200px;
                    height: 80px;
                    clicked => { hang-up(); }
                }

                // 操作提示
                Text {
                    text: "Press button to hang up";
                    font-size: 18px;
                    color: #aaaaaa;
                    horizontal-alignment: center;
                }
            }
        }

        // 通话结束界面
        if ui-state == 3: Rectangle {
            background: #440000;

            VerticalBox {
                alignment: center;
                spacing: 30px;

                // 通话结束标题
                Text {
                    text: "Call Ended";
                    font-size: 36px;
                    color: #ffffff;
                    horizontal-alignment: center;
                    font-weight: 700;
                }

                // 状态指示器
                Rectangle {
                    width: 300px;
                    height: 150px;
                    background: #660000;
                    border-radius: 15px;

                    Text {
                        text: "Disconnected";
                        color: #ff6666;
                        font-size: 24px;
                        horizontal-alignment: center;
                        vertical-alignment: center;
                        font-weight: 700;
                    }
                }
            }
        }
    }
}


