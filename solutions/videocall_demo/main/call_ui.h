/* Call UI for Video Call Demo
 * 
 * 接听界面 - 显示来电信息和接听/拒绝按钮
 */

#pragma once

#include "esp_lcd_panel_ops.h"
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 接听界面配置结构
 */
typedef struct {
    esp_lcd_panel_handle_t lcd_handle;  // LCD句柄
    int screen_width;                   // 屏幕宽度
    int screen_height;                  // 屏幕高度
    uint16_t bg_color;                  // 背景颜色 (RGB565)
    uint16_t text_color;                // 文字颜色 (RGB565)
    uint16_t accept_color;              // 接听按钮颜色 (RGB565)
    uint16_t reject_color;              // 拒绝按钮颜色 (RGB565)
} call_ui_config_t;

/**
 * @brief 初始化接听界面
 * 
 * @param config 接听界面配置
 * @return 
 *      - ESP_OK: 成功
 *      - ESP_FAIL: 失败
 */
esp_err_t call_ui_init(const call_ui_config_t *config);

/**
 * @brief 显示来电界面
 * 
 * @param caller_info 来电者信息
 */
void call_ui_show_incoming(const char *caller_info);

/**
 * @brief 显示通话中界面
 */
void call_ui_show_in_call(void);

/**
 * @brief 显示通话结束界面
 */
void call_ui_show_call_ended(void);

/**
 * @brief 清除接听界面
 */
void call_ui_clear(void);

/**
 * @brief 接听界面是否已初始化
 * 
 * @return true: 已初始化, false: 未初始化
 */
bool call_ui_is_initialized(void);

#ifdef __cplusplus
}
#endif
