/*
 * LCD详细诊断程序
 * 逐步检查LCD初始化和绘制的每个环节
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "codec_init.h"
#include "esp_lcd_panel_ops.h"

static const char *TAG = "LCD_DIAGNOSTIC";

// GPIO配置
#define RST_LCD_GPIO    (27)    // 复位引脚
#define PWM_LCD_GPIO    (26)    // 背光控制引脚

// 详细的LCD状态检查
void detailed_lcd_diagnostic(void)
{
    ESP_LOGI(TAG, "=== LCD详细诊断开始 ===");

    // 1. 检查LCD句柄
    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    ESP_LOGI(TAG, "1. LCD句柄检查:");
    if (lcd_handle == NULL) {
        ESP_LOGE(TAG, "   ❌ LCD句柄为NULL，LCD未初始化");
        ESP_LOGI(TAG, "   💡 请先运行系统初始化");
        return;
    } else {
        ESP_LOGI(TAG, "   ✅ LCD句柄有效: %p", lcd_handle);
    }

    // 2. 检查GPIO状态
    ESP_LOGI(TAG, "2. GPIO状态检查:");
    
    // 检查复位引脚
    int rst_level = gpio_get_level(RST_LCD_GPIO);
    ESP_LOGI(TAG, "   RST_LCD (GPIO %d): %s", RST_LCD_GPIO, rst_level ? "HIGH" : "LOW");
    
    // 检查背光引脚
    int pwm_level = gpio_get_level(PWM_LCD_GPIO);
    ESP_LOGI(TAG, "   PWM_LCD (GPIO %d): %s", PWM_LCD_GPIO, pwm_level ? "HIGH (背光开)" : "LOW (背光关)");

    // 3. 测试LCD面板基本功能
    ESP_LOGI(TAG, "3. LCD面板功能测试:");
    
    // 测试显示开关（可能不支持）
    esp_err_t ret = esp_lcd_panel_disp_on_off(lcd_handle, false);
    ESP_LOGI(TAG, "   显示关闭: %s", esp_err_to_name(ret));
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    ret = esp_lcd_panel_disp_on_off(lcd_handle, true);
    ESP_LOGI(TAG, "   显示开启: %s", esp_err_to_name(ret));

    // 测试颜色反转
    ret = esp_lcd_panel_invert_color(lcd_handle, true);
    ESP_LOGI(TAG, "   颜色反转开启: %s", esp_err_to_name(ret));
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    ret = esp_lcd_panel_invert_color(lcd_handle, false);
    ESP_LOGI(TAG, "   颜色反转关闭: %s", esp_err_to_name(ret));

    // 4. 内存分配测试
    ESP_LOGI(TAG, "4. 内存分配测试:");
    
    const int test_width = 50;
    const int test_height = 50;
    const int buffer_size = test_width * test_height * 2;
    
    uint16_t *buffer = (uint16_t *)malloc(buffer_size);
    if (buffer == NULL) {
        ESP_LOGE(TAG, "   ❌ 内存分配失败: %d bytes", buffer_size);
        return;
    } else {
        ESP_LOGI(TAG, "   ✅ 内存分配成功: %d bytes", buffer_size);
    }

    // 5. 绘制API测试
    ESP_LOGI(TAG, "5. 绘制API详细测试:");
    
    // 填充红色
    for (int i = 0; i < test_width * test_height; i++) {
        buffer[i] = 0xF800; // 红色
    }

    // 测试不同位置的绘制
    struct {
        int x, y;
        const char* desc;
    } test_positions[] = {
        {0, 0, "左上角"},
        {500, 300, "中心"},
        {974, 550, "右下角"},
        {100, 100, "左上区域"},
    };

    for (int i = 0; i < 4; i++) {
        int x = test_positions[i].x;
        int y = test_positions[i].y;
        
        ESP_LOGI(TAG, "   测试绘制: %s (%d,%d)", test_positions[i].desc, x, y);
        
        ret = esp_lcd_panel_draw_bitmap(lcd_handle, x, y, x + test_width, y + test_height, buffer);
        
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "     ✅ 绘制API成功");
        } else {
            ESP_LOGE(TAG, "     ❌ 绘制API失败: %s (0x%x)", esp_err_to_name(ret), ret);
        }
        
        vTaskDelay(pdMS_TO_TICKS(1000));
    }

    // 6. 大缓冲区测试
    ESP_LOGI(TAG, "6. 大缓冲区测试:");
    
    const int large_width = 200;
    const int large_height = 200;
    const int large_buffer_size = large_width * large_height * 2;
    
    uint16_t *large_buffer = (uint16_t *)malloc(large_buffer_size);
    if (large_buffer == NULL) {
        ESP_LOGE(TAG, "   ❌ 大缓冲区分配失败: %d bytes", large_buffer_size);
    } else {
        ESP_LOGI(TAG, "   ✅ 大缓冲区分配成功: %d bytes", large_buffer_size);
        
        // 填充绿色
        for (int i = 0; i < large_width * large_height; i++) {
            large_buffer[i] = 0x07E0; // 绿色
        }
        
        ESP_LOGI(TAG, "   测试大块绘制 200x200...");
        ret = esp_lcd_panel_draw_bitmap(lcd_handle, 400, 200, 400 + large_width, 200 + large_height, large_buffer);
        
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "     ✅ 大块绘制API成功");
        } else {
            ESP_LOGE(TAG, "     ❌ 大块绘制API失败: %s", esp_err_to_name(ret));
        }
        
        free(large_buffer);
    }

    // 7. 全屏测试
    ESP_LOGI(TAG, "7. 全屏绘制测试:");
    
    const int full_buffer_size = 1024 * 600 * 2;
    ESP_LOGI(TAG, "   尝试分配全屏缓冲区: %d bytes", full_buffer_size);
    
    uint16_t *full_buffer = (uint16_t *)malloc(full_buffer_size);
    if (full_buffer == NULL) {
        ESP_LOGE(TAG, "   ❌ 全屏缓冲区分配失败");
        ESP_LOGI(TAG, "   💡 内存不足，跳过全屏测试");
    } else {
        ESP_LOGI(TAG, "   ✅ 全屏缓冲区分配成功");
        
        // 填充蓝色
        for (int i = 0; i < 1024 * 600; i++) {
            full_buffer[i] = 0x001F; // 蓝色
        }
        
        ESP_LOGI(TAG, "   测试全屏绘制 1024x600...");
        ret = esp_lcd_panel_draw_bitmap(lcd_handle, 0, 0, 1024, 600, full_buffer);
        
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "     ✅ 全屏绘制API成功");
        } else {
            ESP_LOGE(TAG, "     ❌ 全屏绘制API失败: %s", esp_err_to_name(ret));
        }
        
        free(full_buffer);
    }

    free(buffer);

    // 8. 总结
    ESP_LOGI(TAG, "8. 诊断总结:");
    ESP_LOGI(TAG, "   - 如果所有绘制API都返回成功，但屏幕无显示，可能是:");
    ESP_LOGI(TAG, "     1. LCD面板未正确初始化");
    ESP_LOGI(TAG, "     2. MIPI DSI通信问题");
    ESP_LOGI(TAG, "     3. 颜色格式不匹配");
    ESP_LOGI(TAG, "     4. 显示时序问题");
    ESP_LOGI(TAG, "   - 如果背光能正常开关，说明GPIO控制正常");
    ESP_LOGI(TAG, "   - 建议检查LCD初始化流程和MIPI DSI配置");

    ESP_LOGI(TAG, "=== LCD详细诊断完成 ===");
}

// LCD诊断任务
static void lcd_diagnostic_task(void *param)
{
    detailed_lcd_diagnostic();
    vTaskDelete(NULL);
}

// 启动LCD诊断
void start_lcd_diagnostic(void)
{
    xTaskCreate(lcd_diagnostic_task, "lcd_diagnostic", 6144, NULL, 5, NULL);
}
