/* Door Bell Demo

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#include <esp_wifi.h>
#include <esp_event.h>
#include <esp_log.h>
#include <esp_system.h>
#include <nvs_flash.h>
#include <sys/param.h>
#include "argtable3/argtable3.h"
#include "esp_console.h"
#include "esp_webrtc.h"
#include "media_lib_adapter.h"
#include "media_lib_os.h"
#include "esp_timer.h"
#include "webrtc_utils_time.h"
#include "esp_cpu.h"
#include "settings.h"
#include "common.h"
#include "test_lcd_render.h"
#include "direct_lcd_test.h"
#include "ek79007_test.h"
#include "multi_gpio_test.h"
#include "simple_ek79007_test.h"
#include "lcd_diagnostic.h"
#include "lcd_reinit.h"
#include "ui_manager.h"
#include "codec_init.h"

static const char *TAG = "Webrtc_Test";

static struct {
    struct arg_str *room_id;
    struct arg_end *end;
} room_args;

static char room_url[128];

#define RUN_ASYNC(name, body)           \
    void run_async##name(void *arg)     \
    {                                   \
        body;                           \
        media_lib_thread_destroy(NULL); \
    }                                   \
    media_lib_thread_create_from_scheduler(NULL, #name, run_async##name, NULL);

static int join_room(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&room_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, room_args.end, argv[0]);
        return 1;
    }
    static bool sntp_synced = false;
    if (sntp_synced == false) {
        if (0 == webrtc_utils_time_sync_init()) {
            sntp_synced = true;
        }
    }
    const char *room_id = room_args.room_id->sval[0];
    snprintf(room_url, sizeof(room_url), "https://webrtc.espressif.cn/join/%s", room_id);
    ESP_LOGI(TAG, "Start to join in room %s", room_id);
    start_webrtc(room_url);
    return 0;
}

static int leave_room(int argc, char **argv)
{
    RUN_ASYNC(leave, { stop_webrtc(); });
    return 0;
}

static int assert_cli(int argc, char **argv)
{
    *(int *)0 = 0;
    return 0;
}

static int sys_cli(int argc, char **argv)
{
    sys_state_show();
    return 0;
}

static int wifi_cli(int argc, char **argv)
{
    if (argc < 1) {
        return -1;
    }
    char *ssid = argv[1];
    char *password = argc > 2 ? argv[2] : NULL;
    return network_connect_wifi(ssid, password);
}

static int capture_to_player_cli(int argc, char **argv)
{
    return test_capture_to_player();
}

void key_pressed(void);

static int button_cli(int argc, char **argv)
{
    key_pressed();
    return 0;
}

static int measure_cli(int argc, char **argv)
{
    void measure_enable(bool enable);
    void show_measure(void);
    measure_enable(true);
    media_lib_thread_sleep(1500);
    measure_enable(false);
    return 0;
}

static int lcd_test_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动LCD渲染测试...");
    start_lcd_render_test();
    return 0;
}

static int lcd_direct_test_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动直接LCD硬件测试...");
    start_direct_lcd_test();
    return 0;
}

static int lcd_simple_direct_test_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动简单直接LCD测试...");
    // 声明外部函数
    extern void start_simple_direct_lcd_test(void);
    start_simple_direct_lcd_test();
    return 0;
}

static int backlight_test_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动背光控制测试...");
    extern void start_backlight_test(void);
    start_backlight_test();
    return 0;
}

static int backlight_manual_cli(int argc, char **argv)
{
    if (argc < 3) {
        ESP_LOGI(TAG, "用法: backlight <gpio> <level>");
        ESP_LOGI(TAG, "例如: backlight 45 1");
        return 0;
    }

    int pin = atoi(argv[1]);
    int level = atoi(argv[2]);

    extern void manual_backlight_test(int pin, int level);
    manual_backlight_test(pin, level);
    return 0;
}

static int ek79007_test_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动EK79007面板测试...");
    start_ek79007_test();
    return 0;
}

static int multi_gpio_test_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动多GPIO背光测试...");
    start_multi_gpio_test();
    return 0;
}

static int simple_ek79007_test_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动简化EK79007测试...");
    start_simple_ek79007_test();
    return 0;
}

static int lcd_diagnostic_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动LCD详细诊断...");
    start_lcd_diagnostic();
    return 0;
}

static int lcd_reinit_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动LCD重新初始化...");
    start_lcd_reinit();
    return 0;
}

static int ui_test_cli(int argc, char **argv)
{
    if (argc < 2) {
        ESP_LOGI(TAG, "用法: ui_test <state>");
        ESP_LOGI(TAG, "状态: 0=屏保, 1=来电, 2=通话中, 3=通话结束");
        return 0;
    }

    int state = atoi(argv[1]);
    ESP_LOGI(TAG, "测试UI状态: %d", state);

    switch (state) {
        case 0:
            ui_manager_set_state(UI_STATE_SCREENSAVER);
            break;
        case 1:
            ui_manager_set_caller_info("Test Caller");
            ui_manager_set_state(UI_STATE_INCOMING_CALL);
            break;
        case 2:
            ui_manager_set_state(UI_STATE_IN_CALL);
            break;
        case 3:
            ui_manager_set_state(UI_STATE_CALL_ENDED);
            break;
        default:
            ESP_LOGI(TAG, "无效的状态值");
            break;
    }

    return 0;
}

static int init_console()
{
    esp_console_repl_t *repl = NULL;
    esp_console_repl_config_t repl_config = ESP_CONSOLE_REPL_CONFIG_DEFAULT();
    repl_config.prompt = "esp>";
    repl_config.task_stack_size = 10 * 1024;
    repl_config.task_priority = 22;
    repl_config.max_cmdline_length = 1024;
    // install console REPL environment
#if CONFIG_ESP_CONSOLE_UART
    esp_console_dev_uart_config_t uart_config = ESP_CONSOLE_DEV_UART_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_console_new_repl_uart(&uart_config, &repl_config, &repl));
#elif CONFIG_ESP_CONSOLE_USB_CDC
    esp_console_dev_usb_cdc_config_t cdc_config = ESP_CONSOLE_DEV_CDC_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_console_new_repl_usb_cdc(&cdc_config, &repl_config, &repl));
#elif CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG
    esp_console_dev_usb_serial_jtag_config_t usbjtag_config = ESP_CONSOLE_DEV_USB_SERIAL_JTAG_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_console_new_repl_usb_serial_jtag(&usbjtag_config, &repl_config, &repl));
#endif

    room_args.room_id = arg_str1(NULL, NULL, "<w123456>", "room name");
    room_args.end = arg_end(2);
    esp_console_cmd_t cmds[] = {
        {
            .command = "join",
            .help = "Please enter a room name.\r\n",
            .func = join_room,
            .argtable = &room_args,
        },
        {
            .command = "leave",
            .help = "Leave from room\n",
            .func = leave_room,
        },
        {
            .command = "i",
            .help = "Show system status\r\n",
            .func = sys_cli,
        },
        {
            .command = "assert",
            .help = "Assert system\r\n",
            .func = assert_cli,
        },
        {
            .command = "rec2play",
            .help = "Play capture content\n",
            .func = capture_to_player_cli,
        },
        {
            .command = "wifi",
            .help = "wifi ssid psw\r\n",
            .func = wifi_cli,
        },
        {
            .command = "m",
            .help = "measure system loading\r\n",
            .func = measure_cli,
        },
        {
            .command = "b",
            .help = "Click button\r\n",
            .func = button_cli,
        },
        {
            .command = "lcd_test",
            .help = "Test LCD render directly\r\n",
            .func = lcd_test_cli,
        },
        {
            .command = "lcd_direct",
            .help = "Test LCD hardware directly\r\n",
            .func = lcd_direct_test_cli,
        },
        {
            .command = "lcd_simple",
            .help = "Simple direct LCD test\r\n",
            .func = lcd_simple_direct_test_cli,
        },
        {
            .command = "backlight_test",
            .help = "Test LCD backlight control\r\n",
            .func = backlight_test_cli,
        },
        {
            .command = "backlight",
            .help = "Manual backlight control: backlight <gpio> <level>\r\n",
            .func = backlight_manual_cli,
        },
        {
            .command = "ek79007_test",
            .help = "Test EK79007 panel directly\r\n",
            .func = ek79007_test_cli,
        },
        {
            .command = "multi_gpio_test",
            .help = "Test multiple GPIO pins for backlight\r\n",
            .func = multi_gpio_test_cli,
        },
        {
            .command = "simple_ek79007",
            .help = "Simple EK79007 test with correct GPIO\r\n",
            .func = simple_ek79007_test_cli,
        },
        {
            .command = "lcd_diagnostic",
            .help = "Detailed LCD diagnostic\r\n",
            .func = lcd_diagnostic_cli,
        },
        {
            .command = "lcd_reinit",
            .help = "Reinitialize LCD with correct GPIO\r\n",
            .func = lcd_reinit_cli,
        },
        {
            .command = "ui_test",
            .help = "Test UI states: ui_test <0-3>\r\n",
            .func = ui_test_cli,
        },
    };
    for (int i = 0; i < sizeof(cmds) / sizeof(cmds[0]); i++) {
        ESP_ERROR_CHECK(esp_console_cmd_register(&cmds[i]));
    }
    ESP_ERROR_CHECK(esp_console_start_repl(repl));
    return 0;
}

static void thread_scheduler(const char *thread_name, media_lib_thread_cfg_t *thread_cfg)
{
    if (strcmp(thread_name, "pc_task") == 0) {
        thread_cfg->stack_size = 25 * 1024;
        thread_cfg->priority = 18;
        thread_cfg->core_id = 1;
    }
    if (strcmp(thread_name, "venc") == 0) {
#if CONFIG_IDF_TARGET_ESP32S3
        thread_cfg->stack_size = 20 * 1024;
#endif
        thread_cfg->priority = 10;
    }
#ifdef WEBRTC_SUPPORT_OPUS
    if (strcmp(thread_name, "aenc") == 0) {
        thread_cfg->stack_size = 40 * 1024;
        thread_cfg->priority = 10;
    }
#endif
}

static int network_event_handler(bool connected)
{
    if (connected == false) {
        stop_webrtc();
    }
    return 0;
}

void app_main(void)
{
    esp_log_level_set("*", ESP_LOG_INFO);
    media_lib_add_default_adapter();
    media_lib_thread_set_schedule_cb(thread_scheduler);
    init_board();
    media_sys_buildup();

    // 初始化UI管理器
    ESP_LOGI(TAG, "初始化UI管理器...");
    ui_manager_config_t ui_config = {
        .lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle(),
        .screen_width = 1024,
        .screen_height = 600,
    };

    esp_err_t ret = ui_manager_init(&ui_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "UI管理器初始化失败: %s", esp_err_to_name(ret));
    } else {
        ret = ui_manager_start();
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "UI管理器启动失败: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "UI管理器启动成功");
        }
    }

    init_console();
    network_init(WIFI_SSID, WIFI_PASSWORD, network_event_handler);
    while (1) {
        media_lib_thread_sleep(2000);
        query_webrtc();
    }
}
