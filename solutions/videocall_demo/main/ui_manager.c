/* UI Manager Implementation
 * 
 * 界面管理器实现 - 管理不同界面状态的切换
 */

#include "ui_manager.h"
#include "screensaver.h"
#include "call_ui.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include <string.h>

static const char *TAG = "UI_MANAGER";

// UI管理器状态
typedef struct {
    bool initialized;
    bool running;
    ui_state_t current_state;
    ui_state_t previous_state;
    esp_lcd_panel_handle_t lcd_handle;
    int screen_width;
    int screen_height;
    char caller_info[64];
    TaskHandle_t ui_task_handle;
    SemaphoreHandle_t state_mutex;
    SemaphoreHandle_t lcd_mutex;  // LCD绘制互斥锁
} ui_manager_t;

static ui_manager_t s_ui_manager = {0};

// 全局安全LCD绘制函数
esp_err_t ui_safe_lcd_draw_bitmap(int x_start, int y_start, int x_end, int y_end, const void *color_data)
{
    if (!s_ui_manager.initialized || s_ui_manager.lcd_handle == NULL) {
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret = ESP_FAIL;
    if (xSemaphoreTake(s_ui_manager.lcd_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        ret = esp_lcd_panel_draw_bitmap(s_ui_manager.lcd_handle, x_start, y_start, x_end, y_end, color_data);
        if (ret != ESP_OK) {
            // 如果绘制失败，等待一小段时间再释放锁
            vTaskDelay(pdMS_TO_TICKS(10));
        }
        xSemaphoreGive(s_ui_manager.lcd_mutex);
    }

    return ret;
}

// UI更新任务
static void ui_manager_task(void *param)
{
    ESP_LOGI(TAG, "UI管理器任务启动");
    
    while (s_ui_manager.running) {
        if (xSemaphoreTake(s_ui_manager.state_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
            // 检查状态是否发生变化
            if (s_ui_manager.current_state != s_ui_manager.previous_state) {
                ESP_LOGI(TAG, "UI状态变化: %d -> %d", s_ui_manager.previous_state, s_ui_manager.current_state);
                
                // 清除之前的界面
                switch (s_ui_manager.previous_state) {
                    case UI_STATE_SCREENSAVER:
                        if (screensaver_is_initialized()) {
                            screensaver_clear();
                        }
                        break;
                    case UI_STATE_INCOMING_CALL:
                    case UI_STATE_IN_CALL:
                    case UI_STATE_CALL_ENDED:
                        if (call_ui_is_initialized()) {
                            call_ui_clear();
                        }
                        break;
                    default:
                        break;
                }
                
                // 显示新界面
                switch (s_ui_manager.current_state) {
                    case UI_STATE_SCREENSAVER:
                        if (screensaver_is_initialized()) {
                            screensaver_show();
                        }
                        break;
                    case UI_STATE_INCOMING_CALL:
                        if (call_ui_is_initialized()) {
                            call_ui_show_incoming(s_ui_manager.caller_info);
                        }
                        break;
                    case UI_STATE_IN_CALL:
                        if (call_ui_is_initialized()) {
                            call_ui_show_in_call();
                        }
                        break;
                    case UI_STATE_CALL_ENDED:
                        if (call_ui_is_initialized()) {
                            call_ui_show_call_ended();
                        }
                        break;
                    default:
                        break;
                }
                
                s_ui_manager.previous_state = s_ui_manager.current_state;
            }
            
            // 定期更新当前界面（仅屏保需要定期更新时间）
            if (s_ui_manager.current_state == UI_STATE_SCREENSAVER) {
                if (screensaver_is_initialized()) {
                    screensaver_update_time();
                }
            }
            
            xSemaphoreGive(s_ui_manager.state_mutex);
        }
        
        // 每5秒更新一次，减少LCD绘制频率
        vTaskDelay(pdMS_TO_TICKS(5000));
    }
    
    ESP_LOGI(TAG, "UI管理器任务结束");
    vTaskDelete(NULL);
}

esp_err_t ui_manager_init(const ui_manager_config_t *config)
{
    if (config == NULL || config->lcd_handle == NULL) {
        ESP_LOGE(TAG, "无效的配置参数");
        return ESP_ERR_INVALID_ARG;
    }
    
    if (s_ui_manager.initialized) {
        ESP_LOGW(TAG, "UI管理器已经初始化");
        return ESP_OK;
    }
    
    // 初始化UI管理器状态
    memset(&s_ui_manager, 0, sizeof(ui_manager_t));
    s_ui_manager.lcd_handle = config->lcd_handle;
    s_ui_manager.screen_width = config->screen_width;
    s_ui_manager.screen_height = config->screen_height;
    s_ui_manager.current_state = UI_STATE_SCREENSAVER;
    s_ui_manager.previous_state = UI_STATE_SCREENSAVER;
    strcpy(s_ui_manager.caller_info, "Unknown Caller");
    
    // 创建状态互斥锁
    s_ui_manager.state_mutex = xSemaphoreCreateMutex();
    if (s_ui_manager.state_mutex == NULL) {
        ESP_LOGE(TAG, "创建状态互斥锁失败");
        return ESP_ERR_NO_MEM;
    }

    // 创建LCD绘制互斥锁
    s_ui_manager.lcd_mutex = xSemaphoreCreateMutex();
    if (s_ui_manager.lcd_mutex == NULL) {
        ESP_LOGE(TAG, "创建LCD互斥锁失败");
        vSemaphoreDelete(s_ui_manager.state_mutex);
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化屏保
    screensaver_config_t screensaver_cfg = {
        .lcd_handle = config->lcd_handle,
        .screen_width = config->screen_width,
        .screen_height = config->screen_height,
        .bg_color = 0x0000,    // 黑色背景
        .text_color = 0xFFFF,  // 白色文字
    };
    
    esp_err_t ret = screensaver_init(&screensaver_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "屏保初始化失败: %s", esp_err_to_name(ret));
        vSemaphoreDelete(s_ui_manager.state_mutex);
        return ret;
    }
    
    // 初始化接听界面
    call_ui_config_t call_ui_cfg = {
        .lcd_handle = config->lcd_handle,
        .screen_width = config->screen_width,
        .screen_height = config->screen_height,
        .bg_color = 0x001F,      // 深蓝色背景
        .text_color = 0xFFFF,    // 白色文字
        .accept_color = 0x07E0,  // 绿色接听按钮
        .reject_color = 0xF800,  // 红色拒绝按钮
    };
    
    ret = call_ui_init(&call_ui_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "接听界面初始化失败: %s", esp_err_to_name(ret));
        vSemaphoreDelete(s_ui_manager.state_mutex);
        return ret;
    }
    
    s_ui_manager.initialized = true;
    ESP_LOGI(TAG, "UI管理器初始化成功");
    
    return ESP_OK;
}

esp_err_t ui_manager_start(void)
{
    if (!s_ui_manager.initialized) {
        ESP_LOGE(TAG, "UI管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (s_ui_manager.running) {
        ESP_LOGW(TAG, "UI管理器已经在运行");
        return ESP_OK;
    }
    
    s_ui_manager.running = true;
    
    // 创建UI更新任务
    BaseType_t ret = xTaskCreate(
        ui_manager_task,
        "ui_manager",
        4096,
        NULL,
        5,
        &s_ui_manager.ui_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建UI管理器任务失败");
        s_ui_manager.running = false;
        return ESP_ERR_NO_MEM;
    }
    
    ESP_LOGI(TAG, "UI管理器启动成功");
    return ESP_OK;
}

void ui_manager_stop(void)
{
    if (!s_ui_manager.running) {
        return;
    }
    
    s_ui_manager.running = false;
    
    // 等待任务结束
    if (s_ui_manager.ui_task_handle != NULL) {
        vTaskDelay(pdMS_TO_TICKS(100)); // 给任务时间退出
        s_ui_manager.ui_task_handle = NULL;
    }
    
    ESP_LOGI(TAG, "UI管理器已停止");
}

void ui_manager_set_state(ui_state_t state)
{
    if (!s_ui_manager.initialized) {
        ESP_LOGW(TAG, "UI管理器未初始化");
        return;
    }
    
    if (xSemaphoreTake(s_ui_manager.state_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        if (s_ui_manager.current_state != state) {
            ESP_LOGI(TAG, "设置UI状态: %d", state);
            s_ui_manager.current_state = state;
        }
        xSemaphoreGive(s_ui_manager.state_mutex);
    }
}

ui_state_t ui_manager_get_state(void)
{
    if (!s_ui_manager.initialized) {
        return UI_STATE_SCREENSAVER;
    }
    
    ui_state_t state = UI_STATE_SCREENSAVER;
    if (xSemaphoreTake(s_ui_manager.state_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        state = s_ui_manager.current_state;
        xSemaphoreGive(s_ui_manager.state_mutex);
    }
    
    return state;
}

void ui_manager_refresh(void)
{
    if (!s_ui_manager.initialized) {
        return;
    }
    
    if (xSemaphoreTake(s_ui_manager.state_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 强制状态变化以触发界面刷新
        s_ui_manager.previous_state = (ui_state_t)(-1);
        xSemaphoreGive(s_ui_manager.state_mutex);
    }
}

void ui_manager_set_caller_info(const char *caller_info)
{
    if (!s_ui_manager.initialized || caller_info == NULL) {
        return;
    }
    
    if (xSemaphoreTake(s_ui_manager.state_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        strncpy(s_ui_manager.caller_info, caller_info, sizeof(s_ui_manager.caller_info) - 1);
        s_ui_manager.caller_info[sizeof(s_ui_manager.caller_info) - 1] = '\0';
        xSemaphoreGive(s_ui_manager.state_mutex);
    }
}
