/*
 * LCD渲染测试程序
 * 直接测试av_render LCD功能，绕过WebRTC和JPEG解码
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "codec_board.h"
#include "codec_init.h"
#include "video_render.h"
#include "av_render_default.h"

static const char *TAG = "LCD_TEST";

// RGB565颜色定义
#define RGB565_RED      0xF800
#define RGB565_GREEN    0x07E0
#define RGB565_BLUE     0x001F
#define RGB565_WHITE    0xFFFF
#define RGB565_BLACK    0x0000
#define RGB565_YELLOW   0xFFE0
#define RGB565_CYAN     0x07FF
#define RGB565_MAGENTA  0xF81F

// 测试图案类型
typedef enum {
    TEST_PATTERN_SOLID_RED = 0,
    TEST_PATTERN_SOLID_GREEN,
    TEST_PATTERN_SOLID_BLUE,
    TEST_PATTERN_SOLID_WHITE,
    TEST_PATTERN_STRIPES_H,
    TEST_PATTERN_STRIPES_V,
    TEST_PATTERN_CHECKERBOARD,
    TEST_PATTERN_GRADIENT,
    TEST_PATTERN_MAX
} test_pattern_t;

static video_render_handle_t lcd_render = NULL;

// 生成纯色图案
static void generate_solid_color(uint16_t *buffer, int width, int height, uint16_t color)
{
    for (int i = 0; i < width * height; i++) {
        buffer[i] = color;
    }
}

// 生成水平条纹
static void generate_horizontal_stripes(uint16_t *buffer, int width, int height)
{
    uint16_t colors[] = {RGB565_RED, RGB565_GREEN, RGB565_BLUE, RGB565_WHITE, RGB565_YELLOW, RGB565_CYAN, RGB565_MAGENTA};
    int stripe_height = height / 7;
    
    for (int y = 0; y < height; y++) {
        uint16_t color = colors[y / stripe_height];
        if (y / stripe_height >= 7) color = RGB565_BLACK;
        
        for (int x = 0; x < width; x++) {
            buffer[y * width + x] = color;
        }
    }
}

// 生成垂直条纹
static void generate_vertical_stripes(uint16_t *buffer, int width, int height)
{
    uint16_t colors[] = {RGB565_RED, RGB565_GREEN, RGB565_BLUE, RGB565_WHITE, RGB565_YELLOW, RGB565_CYAN, RGB565_MAGENTA};
    int stripe_width = width / 7;
    
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            uint16_t color = colors[x / stripe_width];
            if (x / stripe_width >= 7) color = RGB565_BLACK;
            buffer[y * width + x] = color;
        }
    }
}

// 生成棋盘图案
static void generate_checkerboard(uint16_t *buffer, int width, int height)
{
    int block_size = 64; // 64x64像素的方块
    
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int block_x = x / block_size;
            int block_y = y / block_size;
            uint16_t color = ((block_x + block_y) % 2) ? RGB565_WHITE : RGB565_BLACK;
            buffer[y * width + x] = color;
        }
    }
}

// 生成渐变图案
static void generate_gradient(uint16_t *buffer, int width, int height)
{
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            // 红色渐变
            uint8_t red = (x * 31) / width;
            // 绿色渐变
            uint8_t green = (y * 63) / height;
            // 蓝色固定
            uint8_t blue = 15;
            
            uint16_t color = (red << 11) | (green << 5) | blue;
            buffer[y * width + x] = color;
        }
    }
}

// 生成测试图案
static void generate_test_pattern(uint16_t *buffer, int width, int height, test_pattern_t pattern)
{
    switch (pattern) {
        case TEST_PATTERN_SOLID_RED:
            generate_solid_color(buffer, width, height, RGB565_RED);
            break;
        case TEST_PATTERN_SOLID_GREEN:
            generate_solid_color(buffer, width, height, RGB565_GREEN);
            break;
        case TEST_PATTERN_SOLID_BLUE:
            generate_solid_color(buffer, width, height, RGB565_BLUE);
            break;
        case TEST_PATTERN_SOLID_WHITE:
            generate_solid_color(buffer, width, height, RGB565_WHITE);
            break;
        case TEST_PATTERN_STRIPES_H:
            generate_horizontal_stripes(buffer, width, height);
            break;
        case TEST_PATTERN_STRIPES_V:
            generate_vertical_stripes(buffer, width, height);
            break;
        case TEST_PATTERN_CHECKERBOARD:
            generate_checkerboard(buffer, width, height);
            break;
        case TEST_PATTERN_GRADIENT:
            generate_gradient(buffer, width, height);
            break;
        default:
            generate_solid_color(buffer, width, height, RGB565_BLACK);
            break;
    }
}

// 获取图案名称
static const char* get_pattern_name(test_pattern_t pattern)
{
    switch (pattern) {
        case TEST_PATTERN_SOLID_RED: return "纯红色";
        case TEST_PATTERN_SOLID_GREEN: return "纯绿色";
        case TEST_PATTERN_SOLID_BLUE: return "纯蓝色";
        case TEST_PATTERN_SOLID_WHITE: return "纯白色";
        case TEST_PATTERN_STRIPES_H: return "水平条纹";
        case TEST_PATTERN_STRIPES_V: return "垂直条纹";
        case TEST_PATTERN_CHECKERBOARD: return "棋盘图案";
        case TEST_PATTERN_GRADIENT: return "颜色渐变";
        default: return "未知图案";
    }
}

// 初始化LCD渲染器
static int init_lcd_render(void)
{
    // 获取LCD句柄
    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    if (lcd_handle == NULL) {
        ESP_LOGE(TAG, "Failed to get LCD handle");
        return -1;
    }

    // 配置LCD渲染器
    lcd_render_cfg_t lcd_cfg = {
        .lcd_handle = lcd_handle,
        .dsi_panel = true,  // ESP32P4使用DSI面板
        .use_frame_buffer = false,  // 不使用frame buffer，直接渲染
    };

    // 创建LCD渲染器
    lcd_render = av_render_alloc_lcd_render(&lcd_cfg);
    if (lcd_render == NULL) {
        ESP_LOGE(TAG, "Failed to create LCD render");
        return -1;
    }

    // 设置帧信息
    av_render_video_frame_info_t frame_info = {
        .type = AV_RENDER_VIDEO_RAW_TYPE_RGB565,
        .width = 1024,
        .height = 600,
    };

    // 获取渲染器操作接口
    video_render_cfg_t *render_cfg = (video_render_cfg_t *)lcd_render;
    if (render_cfg == NULL || render_cfg->ops.set_frame_info == NULL) {
        ESP_LOGE(TAG, "Invalid render operations");
        return -1;
    }

    int ret = render_cfg->ops.set_frame_info(lcd_render, &frame_info);
    if (ret != 0) {
        ESP_LOGE(TAG, "Failed to set frame info: %d", ret);
        return ret;
    }

    ESP_LOGI(TAG, "LCD render initialized successfully");
    return 0;
}

// LCD渲染测试任务
void lcd_render_test_task(void *pvParameters)
{
    ESP_LOGI(TAG, "=== LCD渲染测试开始 ===");

    // 初始化LCD渲染器
    if (init_lcd_render() != 0) {
        ESP_LOGE(TAG, "LCD渲染器初始化失败");
        vTaskDelete(NULL);
        return;
    }

    // 分配图像缓冲区
    const int width = 1024;
    const int height = 600;
    const int buffer_size = width * height * 2; // RGB565 = 2 bytes per pixel
    
    uint16_t *image_buffer = (uint16_t *)malloc(buffer_size);
    if (image_buffer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate image buffer");
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGI(TAG, "图像缓冲区分配成功: %d bytes", buffer_size);

    // 循环测试不同图案
    test_pattern_t current_pattern = 0;
    int frame_count = 0;

    while (1) {
        // 生成测试图案
        generate_test_pattern(image_buffer, width, height, current_pattern);
        
        ESP_LOGI(TAG, "显示图案 %d: %s (帧 %d)", 
                 current_pattern, get_pattern_name(current_pattern), frame_count);

        // 准备渲染数据
        av_render_video_frame_t video_frame = {
            .data = (uint8_t *)image_buffer,
            .size = buffer_size,
            .pts = esp_timer_get_time(),
        };

        // 渲染到LCD
        video_render_cfg_t *render_cfg = (video_render_cfg_t *)lcd_render;
        int ret = render_cfg->ops.write(lcd_render, &video_frame);
        if (ret != 0) {
            ESP_LOGE(TAG, "LCD渲染失败: %d", ret);
        } else {
            ESP_LOGI(TAG, "LCD渲染成功");
        }

        // 等待3秒后切换到下一个图案
        vTaskDelay(pdMS_TO_TICKS(3000));
        
        current_pattern = (current_pattern + 1) % TEST_PATTERN_MAX;
        frame_count++;
    }

    // 清理资源
    free(image_buffer);
    if (lcd_render) {
        video_render_cfg_t *render_cfg = (video_render_cfg_t *)lcd_render;
        render_cfg->ops.close(lcd_render);
    }
    
    vTaskDelete(NULL);
}

// 启动LCD渲染测试
void start_lcd_render_test(void)
{
    xTaskCreate(lcd_render_test_task, "lcd_test", 8192, NULL, 5, NULL);
}
