/*
 * 简化EK79007测试程序头文件
 */

#ifndef SIMPLE_EK79007_TEST_H
#define SIMPLE_EK79007_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 启动简化EK79007测试
 * 
 * 根据官方文档配置：
 * - GPIO 27: RST_LCD (复位引脚)
 * - GPIO 26: PWM (背光控制引脚)
 * 
 * 测试内容：
 * 1. GPIO控制测试（复位和背光开关）
 * 2. 使用现有LCD句柄进行简单绘制测试
 * 
 * 避免复杂的EK79007初始化，专注于基本功能验证。
 */
void start_simple_ek79007_test(void);

#ifdef __cplusplus
}
#endif

#endif // SIMPLE_EK79007_TEST_H
