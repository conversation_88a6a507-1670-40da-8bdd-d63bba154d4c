/*
 * 直接LCD硬件测试头文件
 */

#ifndef DIRECT_LCD_TEST_H
#define DIRECT_LCD_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 启动直接LCD硬件测试
 * 
 * 这个函数会：
 * 1. 测试LCD面板的基本功能（开关、颜色反转）
 * 2. 直接调用esp_lcd_panel_draw_bitmap绘制测试图案
 * 3. 先测试小区域绘制，再测试全屏绘制
 * 
 * 这可以帮助确定LCD硬件驱动是否正常工作。
 */
void start_direct_lcd_test(void);

/**
 * @brief 测试LCD面板基本功能
 * 
 * 测试LCD面板的开关和颜色反转功能
 */
void test_lcd_panel_basic(void);

/**
 * @brief 直接LCD硬件绘制测试
 * 
 * 绕过av_render，直接调用ESP32 LCD API进行绘制测试
 */
void direct_lcd_test(void);

#ifdef __cplusplus
}
#endif

#endif // DIRECT_LCD_TEST_H
