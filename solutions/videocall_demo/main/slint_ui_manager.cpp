/* Slint UI Manager Implementation
 * 
 * 基于Slint的UI管理器实现
 */

#include "slint_ui_manager.h"
#include "esp_log.h"
#include "esp_err.h"
#include <slint-esp.h>
#include <vector>
#include <cstring>
#include <ctime>

// 包含生成的Slint头文件
#include "videocall-ui.h"

static const char *TAG = "SLINT_UI";

// UI管理器状态
typedef struct {
    bool initialized;
    bool running;
    slint_ui_state_t current_state;
    std::shared_ptr<VideoCallWindow> ui_window;
    slint_ui_callback_t accept_callback;
    slint_ui_callback_t reject_callback;
    slint_ui_callback_t hangup_callback;
    char caller_info[64];
} slint_ui_manager_t;

static slint_ui_manager_t s_ui_manager = {0};

// 时间更新任务
static void time_update_task(void *param)
{
    while (s_ui_manager.running) {
        if (s_ui_manager.ui_window && s_ui_manager.current_state == SLINT_UI_STATE_SCREENSAVER) {
            // 获取当前时间
            time_t now;
            struct tm timeinfo;
            time(&now);
            localtime_r(&now, &timeinfo);
            
            // 格式化时间字符串
            char time_str[16];
            char date_str[16];
            strftime(time_str, sizeof(time_str), "%H:%M", &timeinfo);
            strftime(date_str, sizeof(date_str), "%Y-%m-%d", &timeinfo);
            
            // 更新UI
            s_ui_manager.ui_window->set_current_time(slint::SharedString(time_str));
            s_ui_manager.ui_window->set_current_date(slint::SharedString(date_str));
        }
        
        // 每分钟更新一次
        vTaskDelay(pdMS_TO_TICKS(60000));
    }
    
    vTaskDelete(NULL);
}

esp_err_t slint_ui_manager_init(const slint_ui_config_t *config)
{
    if (config == NULL) {
        ESP_LOGE(TAG, "无效的配置参数");
        return ESP_ERR_INVALID_ARG;
    }
    
    if (s_ui_manager.initialized) {
        ESP_LOGW(TAG, "Slint UI管理器已经初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化Slint UI管理器...");
    
    // 初始化状态
    memset(&s_ui_manager, 0, sizeof(slint_ui_manager_t));
    s_ui_manager.current_state = SLINT_UI_STATE_SCREENSAVER;
    strcpy(s_ui_manager.caller_info, "Unknown Caller");
    
    try {
        // 分配绘制缓冲区
        static std::vector<slint::platform::Rgb565Pixel> buffer(config->screen_width * config->screen_height);
        
        // 初始化Slint ESP平台支持
        slint_esp_init(SlintPlatformConfiguration {
            .size = slint::PhysicalSize({ config->screen_width, config->screen_height }),
            .panel_handle = (esp_lcd_panel_handle_t)config->lcd_panel_handle,
            .touch_handle = (esp_lcd_touch_handle_t)config->touch_handle,
            .buffer1 = buffer,
            .byte_swap = true
        });
        
        // 创建UI窗口
        s_ui_manager.ui_window = VideoCallWindow::create();
        if (!s_ui_manager.ui_window) {
            ESP_LOGE(TAG, "创建UI窗口失败");
            return ESP_ERR_NO_MEM;
        }
        
        // 设置初始状态
        s_ui_manager.ui_window->set_ui_state(s_ui_manager.current_state);
        s_ui_manager.ui_window->set_caller_info(slint::SharedString(s_ui_manager.caller_info));
        
        // 设置回调函数
        s_ui_manager.ui_window->on_accept_call([&]() {
            ESP_LOGI(TAG, "接听按钮被点击");
            if (s_ui_manager.accept_callback) {
                s_ui_manager.accept_callback();
            }
        });
        
        s_ui_manager.ui_window->on_reject_call([&]() {
            ESP_LOGI(TAG, "拒绝按钮被点击");
            if (s_ui_manager.reject_callback) {
                s_ui_manager.reject_callback();
            }
        });
        
        s_ui_manager.ui_window->on_hang_up([&]() {
            ESP_LOGI(TAG, "挂断按钮被点击");
            if (s_ui_manager.hangup_callback) {
                s_ui_manager.hangup_callback();
            }
        });
        
        s_ui_manager.initialized = true;
        ESP_LOGI(TAG, "Slint UI管理器初始化成功");
        
        return ESP_OK;
        
    } catch (const std::exception& e) {
        ESP_LOGE(TAG, "Slint初始化异常: %s", e.what());
        return ESP_ERR_NO_MEM;
    }
}

esp_err_t slint_ui_manager_start(void)
{
    if (!s_ui_manager.initialized) {
        ESP_LOGE(TAG, "UI管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (s_ui_manager.running) {
        ESP_LOGW(TAG, "UI管理器已经在运行");
        return ESP_OK;
    }
    
    s_ui_manager.running = true;
    
    // 创建时间更新任务
    xTaskCreate(time_update_task, "time_update", 4096, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "Slint UI管理器启动成功");
    return ESP_OK;
}

void slint_ui_manager_stop(void)
{
    if (!s_ui_manager.running) {
        return;
    }
    
    s_ui_manager.running = false;
    ESP_LOGI(TAG, "Slint UI管理器已停止");
}

void slint_ui_set_state(slint_ui_state_t state)
{
    if (!s_ui_manager.initialized || !s_ui_manager.ui_window) {
        ESP_LOGW(TAG, "UI管理器未初始化");
        return;
    }
    
    if (s_ui_manager.current_state != state) {
        ESP_LOGI(TAG, "UI状态变化: %d -> %d", s_ui_manager.current_state, state);
        s_ui_manager.current_state = state;
        s_ui_manager.ui_window->set_ui_state(state);
    }
}

slint_ui_state_t slint_ui_get_state(void)
{
    return s_ui_manager.current_state;
}

void slint_ui_update_time(const char *time_str, const char *date_str)
{
    if (!s_ui_manager.initialized || !s_ui_manager.ui_window) {
        return;
    }
    
    if (time_str) {
        s_ui_manager.ui_window->set_current_time(slint::SharedString(time_str));
    }
    
    if (date_str) {
        s_ui_manager.ui_window->set_current_date(slint::SharedString(date_str));
    }
}

void slint_ui_set_caller_info(const char *caller_info)
{
    if (!s_ui_manager.initialized || !s_ui_manager.ui_window || !caller_info) {
        return;
    }
    
    strncpy(s_ui_manager.caller_info, caller_info, sizeof(s_ui_manager.caller_info) - 1);
    s_ui_manager.caller_info[sizeof(s_ui_manager.caller_info) - 1] = '\0';
    
    s_ui_manager.ui_window->set_caller_info(slint::SharedString(s_ui_manager.caller_info));
}

void slint_ui_set_accept_callback(slint_ui_callback_t callback)
{
    s_ui_manager.accept_callback = callback;
}

void slint_ui_set_reject_callback(slint_ui_callback_t callback)
{
    s_ui_manager.reject_callback = callback;
}

void slint_ui_set_hangup_callback(slint_ui_callback_t callback)
{
    s_ui_manager.hangup_callback = callback;
}

void slint_ui_run_event_loop(void)
{
    if (!s_ui_manager.initialized || !s_ui_manager.ui_window) {
        return;
    }
    
    // 运行Slint事件循环（非阻塞）
    slint::platform::update_timers_and_animations();
}

// C++包装器，用于从C代码调用
extern "C" {
    // 这些函数已经在头文件中声明，实现在上面
}
