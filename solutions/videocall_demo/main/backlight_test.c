/*
 * ESP32P4背光控制测试
 * 尝试不同的GPIO引脚来控制LCD背光
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "codec_init.h"
#include "esp_lcd_panel_ops.h"

static const char *TAG = "BACKLIGHT_TEST";

// 可能的背光控制引脚（根据ESP32P4开发板原理图）
static const int backlight_pins[] = {
    45,  // 常见的背光控制引脚
    46,  // PA引脚，可能用于背光
    47,  // 其他可能的引脚
    48,
    49,
    50,
    51,
    52,
    54,  // 跳过53（已用于PA）
    -1   // 结束标记
};

// 配置GPIO为输出模式
static void configure_gpio_output(int pin)
{
    if (pin < 0) return;
    
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << pin),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    gpio_config(&io_conf);
}

// 测试背光控制
void test_backlight_control(void)
{
    ESP_LOGI(TAG, "=== ESP32P4背光控制测试开始 ===");

    // 先绘制一个测试图案
    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    if (lcd_handle == NULL) {
        ESP_LOGE(TAG, "无法获取LCD句柄");
        return;
    }

    // 创建测试图案
    const int width = 200;
    const int height = 200;
    const int buffer_size = width * height * 2;
    
    uint16_t *buffer = (uint16_t *)malloc(buffer_size);
    if (buffer == NULL) {
        ESP_LOGE(TAG, "内存分配失败");
        return;
    }

    // 填充白色（最容易看见）
    for (int i = 0; i < width * height; i++) {
        buffer[i] = 0xFFFF; // 白色
    }

    // 绘制白色方块
    ESP_LOGI(TAG, "绘制200x200白色方块...");
    esp_err_t ret = esp_lcd_panel_draw_bitmap(lcd_handle, 400, 200, width + 400, height + 200, buffer);
    ESP_LOGI(TAG, "绘制结果: %s", esp_err_to_name(ret));

    // 测试各个可能的背光引脚
    for (int i = 0; backlight_pins[i] >= 0; i++) {
        int pin = backlight_pins[i];
        
        ESP_LOGI(TAG, "测试GPIO %d作为背光控制...", pin);
        
        // 配置为输出
        configure_gpio_output(pin);
        
        // 先设置为低电平（可能是关闭背光）
        gpio_set_level(pin, 0);
        ESP_LOGI(TAG, "  GPIO %d = 0 (可能关闭背光)", pin);
        vTaskDelay(pdMS_TO_TICKS(2000));
        
        // 再设置为高电平（可能是开启背光）
        gpio_set_level(pin, 1);
        ESP_LOGI(TAG, "  GPIO %d = 1 (可能开启背光)", pin);
        vTaskDelay(pdMS_TO_TICKS(3000));
        
        ESP_LOGI(TAG, "  请观察屏幕是否有变化...");
        vTaskDelay(pdMS_TO_TICKS(2000));
    }

    // 测试PWM背光控制（某些引脚可能需要PWM）
    ESP_LOGI(TAG, "测试PWM背光控制...");
    
    // 尝试GPIO 45和46的PWM控制
    int pwm_pins[] = {45, 46, -1};
    
    for (int i = 0; pwm_pins[i] >= 0; i++) {
        int pin = pwm_pins[i];
        ESP_LOGI(TAG, "测试GPIO %d的PWM背光控制...", pin);
        
        configure_gpio_output(pin);
        
        // 模拟PWM（快速开关）
        for (int cycle = 0; cycle < 100; cycle++) {
            gpio_set_level(pin, 1);
            vTaskDelay(pdMS_TO_TICKS(5));
            gpio_set_level(pin, 0);
            vTaskDelay(pdMS_TO_TICKS(5));
        }
        
        // 保持高电平
        gpio_set_level(pin, 1);
        ESP_LOGI(TAG, "  GPIO %d PWM测试完成，保持高电平", pin);
        vTaskDelay(pdMS_TO_TICKS(3000));
    }

    free(buffer);
    ESP_LOGI(TAG, "=== 背光控制测试完成 ===");
}

// 手动背光控制测试
void manual_backlight_test(int pin, int level)
{
    ESP_LOGI(TAG, "手动设置GPIO %d = %d", pin, level);
    
    if (pin < 0 || pin > 54) {
        ESP_LOGE(TAG, "无效的GPIO引脚: %d", pin);
        return;
    }
    
    configure_gpio_output(pin);
    gpio_set_level(pin, level);
    
    ESP_LOGI(TAG, "GPIO %d已设置为 %d", pin, level);
}

// 测试所有GPIO的状态
void test_all_gpio_states(void)
{
    ESP_LOGI(TAG, "=== 测试所有可能的背光GPIO ===");
    
    // 先绘制测试图案
    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    if (lcd_handle != NULL) {
        // 创建全屏白色
        const int buffer_size = 1024 * 600 * 2;
        uint16_t *buffer = (uint16_t *)malloc(buffer_size);
        if (buffer != NULL) {
            for (int i = 0; i < 1024 * 600; i++) {
                buffer[i] = 0xFFFF; // 白色
            }
            
            ESP_LOGI(TAG, "绘制全屏白色...");
            esp_lcd_panel_draw_bitmap(lcd_handle, 0, 0, 1024, 600, buffer);
            free(buffer);
        }
    }
    
    // 尝试所有可能的背光引脚，全部设置为高电平
    ESP_LOGI(TAG, "将所有可能的背光引脚设置为高电平...");
    for (int i = 0; backlight_pins[i] >= 0; i++) {
        int pin = backlight_pins[i];
        configure_gpio_output(pin);
        gpio_set_level(pin, 1);
        ESP_LOGI(TAG, "GPIO %d = 1", pin);
    }
    
    ESP_LOGI(TAG, "所有背光引脚已设置为高电平，请观察屏幕...");
}

// 启动背光测试
void start_backlight_test(void)
{
    // 先测试所有GPIO
    test_all_gpio_states();
    
    vTaskDelay(pdMS_TO_TICKS(5000));
    
    // 再进行详细测试
    test_backlight_control();
}
