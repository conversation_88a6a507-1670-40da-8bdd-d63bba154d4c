/*
 * LCD重新初始化程序
 * 强制重新初始化LCD，使用正确的GPIO配置
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "codec_init.h"
#include "esp_lcd_panel_ops.h"

static const char *TAG = "LCD_REINIT";

// GPIO配置
#define RST_LCD_GPIO    (27)    // 复位引脚
#define PWM_LCD_GPIO    (26)    // 背光控制引脚

// 强制GPIO初始化
void force_gpio_init(void)
{
    ESP_LOGI(TAG, "=== 强制GPIO初始化 ===");

    // 1. 配置复位引脚
    ESP_LOGI(TAG, "配置复位引脚 GPIO %d", RST_LCD_GPIO);
    gpio_reset_pin(RST_LCD_GPIO);  // 先重置
    gpio_config_t rst_config = {
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << RST_LCD_GPIO),
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    esp_err_t ret = gpio_config(&rst_config);
    ESP_LOGI(TAG, "复位引脚配置: %s", esp_err_to_name(ret));

    // 2. 配置背光引脚
    ESP_LOGI(TAG, "配置背光引脚 GPIO %d", PWM_LCD_GPIO);
    gpio_reset_pin(PWM_LCD_GPIO);  // 先重置
    gpio_config_t pwm_config = {
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << PWM_LCD_GPIO),
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    ret = gpio_config(&pwm_config);
    ESP_LOGI(TAG, "背光引脚配置: %s", esp_err_to_name(ret));

    // 3. 执行完整的复位序列
    ESP_LOGI(TAG, "执行完整复位序列...");
    
    // 先关闭背光
    gpio_set_level(PWM_LCD_GPIO, 0);
    ESP_LOGI(TAG, "背光关闭");
    vTaskDelay(pdMS_TO_TICKS(100));
    
    // 复位LCD
    gpio_set_level(RST_LCD_GPIO, 0);  // 拉低复位
    ESP_LOGI(TAG, "复位拉低");
    vTaskDelay(pdMS_TO_TICKS(200));   // 保持200ms
    
    gpio_set_level(RST_LCD_GPIO, 1);  // 释放复位
    ESP_LOGI(TAG, "复位释放");
    vTaskDelay(pdMS_TO_TICKS(200));   // 等待200ms
    
    // 开启背光
    gpio_set_level(PWM_LCD_GPIO, 1);
    ESP_LOGI(TAG, "背光开启");
    vTaskDelay(pdMS_TO_TICKS(100));

    ESP_LOGI(TAG, "=== GPIO初始化完成 ===");
}

// 重新初始化LCD
void reinit_lcd(void)
{
    ESP_LOGI(TAG, "=== LCD重新初始化 ===");

    // 1. 强制GPIO初始化
    force_gpio_init();

    // 2. 重新初始化LCD
    ESP_LOGI(TAG, "调用board_lcd_init()...");
    int ret = board_lcd_init();
    if (ret == 0) {
        ESP_LOGI(TAG, "✅ LCD重新初始化成功");
    } else {
        ESP_LOGE(TAG, "❌ LCD重新初始化失败: %d", ret);
        return;
    }

    // 3. 获取新的LCD句柄
    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    if (lcd_handle == NULL) {
        ESP_LOGE(TAG, "❌ 重新初始化后LCD句柄仍为NULL");
        return;
    }
    ESP_LOGI(TAG, "✅ 新LCD句柄: %p", lcd_handle);

    // 4. 测试基本绘制
    ESP_LOGI(TAG, "测试重新初始化后的绘制功能...");
    
    const int width = 100;
    const int height = 100;
    uint16_t *buffer = (uint16_t *)malloc(width * height * 2);
    if (buffer == NULL) {
        ESP_LOGE(TAG, "内存分配失败");
        return;
    }

    // 绘制红色方块
    for (int i = 0; i < width * height; i++) {
        buffer[i] = 0xF800; // 红色
    }

    esp_err_t draw_ret = esp_lcd_panel_draw_bitmap(lcd_handle, 100, 100, 100 + width, 100 + height, buffer);
    ESP_LOGI(TAG, "绘制测试结果: %s", esp_err_to_name(draw_ret));

    if (draw_ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ 重新初始化后绘制API正常");
        ESP_LOGI(TAG, "💡 如果仍然看不到红色方块，可能是硬件连接问题");
    } else {
        ESP_LOGE(TAG, "❌ 重新初始化后绘制API仍然失败");
    }

    free(buffer);
    ESP_LOGI(TAG, "=== LCD重新初始化完成 ===");
}

// 测试硬件连接
void test_hardware_connection(void)
{
    ESP_LOGI(TAG, "=== 硬件连接测试 ===");

    ESP_LOGI(TAG, "测试复位引脚连接...");
    
    // 测试复位引脚是否正常工作
    for (int i = 0; i < 5; i++) {
        gpio_set_level(RST_LCD_GPIO, 0);
        ESP_LOGI(TAG, "复位引脚 = 0");
        vTaskDelay(pdMS_TO_TICKS(500));
        
        gpio_set_level(RST_LCD_GPIO, 1);
        ESP_LOGI(TAG, "复位引脚 = 1");
        vTaskDelay(pdMS_TO_TICKS(500));
    }

    ESP_LOGI(TAG, "测试背光引脚连接...");
    
    // 测试背光引脚是否正常工作
    for (int i = 0; i < 5; i++) {
        gpio_set_level(PWM_LCD_GPIO, 0);
        ESP_LOGI(TAG, "背光引脚 = 0 (应该看到屏幕变暗)");
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        gpio_set_level(PWM_LCD_GPIO, 1);
        ESP_LOGI(TAG, "背光引脚 = 1 (应该看到屏幕变亮)");
        vTaskDelay(pdMS_TO_TICKS(1000));
    }

    ESP_LOGI(TAG, "=== 硬件连接测试完成 ===");
    ESP_LOGI(TAG, "💡 如果背光能正常开关，说明GPIO 26连接正确");
    ESP_LOGI(TAG, "💡 如果看不到显示内容，可能是:");
    ESP_LOGI(TAG, "   1. MIPI DSI排线连接问题");
    ESP_LOGI(TAG, "   2. LCD电源供应问题");
    ESP_LOGI(TAG, "   3. LCD面板本身问题");
    ESP_LOGI(TAG, "   4. 软件配置问题");
}

// LCD重新初始化任务
static void lcd_reinit_task(void *param)
{
    // 1. 测试硬件连接
    test_hardware_connection();
    
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 2. 重新初始化LCD
    reinit_lcd();
    
    vTaskDelete(NULL);
}

// 启动LCD重新初始化
void start_lcd_reinit(void)
{
    xTaskCreate(lcd_reinit_task, "lcd_reinit", 6144, NULL, 5, NULL);
}
