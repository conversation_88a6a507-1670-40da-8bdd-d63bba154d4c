/* Screensaver UI for Video Call Demo
 * 
 * 屏保界面 - 显示当前时间和日期
 */

#pragma once

#include "esp_lcd_panel_ops.h"
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 屏保配置结构
 */
typedef struct {
    esp_lcd_panel_handle_t lcd_handle;  // LCD句柄
    int screen_width;                   // 屏幕宽度
    int screen_height;                  // 屏幕高度
    uint16_t bg_color;                  // 背景颜色 (RGB565)
    uint16_t text_color;                // 文字颜色 (RGB565)
} screensaver_config_t;

/**
 * @brief 初始化屏保
 * 
 * @param config 屏保配置
 * @return 
 *      - ESP_OK: 成功
 *      - ESP_FAIL: 失败
 */
esp_err_t screensaver_init(const screensaver_config_t *config);

/**
 * @brief 显示屏保界面
 */
void screensaver_show(void);

/**
 * @brief 更新屏保时间显示
 */
void screensaver_update_time(void);

/**
 * @brief 清除屏保界面
 */
void screensaver_clear(void);

/**
 * @brief 屏保是否已初始化
 * 
 * @return true: 已初始化, false: 未初始化
 */
bool screensaver_is_initialized(void);

#ifdef __cplusplus
}
#endif
