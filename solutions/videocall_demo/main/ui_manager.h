/* UI Manager for Video Call Demo
 * 
 * 管理不同界面状态的切换：屏保界面、接听界面等
 */

#pragma once

#include <stdbool.h>
#include "esp_lcd_panel_ops.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief UI状态枚举
 */
typedef enum {
    UI_STATE_SCREENSAVER,    // 屏保状态 - 显示时间
    UI_STATE_INCOMING_CALL,  // 来电状态 - 显示接听界面
    UI_STATE_IN_CALL,        // 通话中状态 - 显示视频通话界面
    UI_STATE_CALL_ENDED,     // 通话结束状态
} ui_state_t;

/**
 * @brief UI管理器配置结构
 */
typedef struct {
    esp_lcd_panel_handle_t lcd_handle;  // LCD句柄
    int screen_width;                   // 屏幕宽度
    int screen_height;                  // 屏幕高度
} ui_manager_config_t;

/**
 * @brief 初始化UI管理器
 * 
 * @param config UI管理器配置
 * @return 
 *      - ESP_OK: 成功
 *      - ESP_FAIL: 失败
 */
esp_err_t ui_manager_init(const ui_manager_config_t *config);

/**
 * @brief 启动UI管理器
 * 
 * @return 
 *      - ESP_OK: 成功
 *      - ESP_FAIL: 失败
 */
esp_err_t ui_manager_start(void);

/**
 * @brief 停止UI管理器
 */
void ui_manager_stop(void);

/**
 * @brief 设置UI状态
 * 
 * @param state 新的UI状态
 */
void ui_manager_set_state(ui_state_t state);

/**
 * @brief 获取当前UI状态
 * 
 * @return 当前UI状态
 */
ui_state_t ui_manager_get_state(void);

/**
 * @brief 强制刷新当前界面
 */
void ui_manager_refresh(void);

/**
 * @brief 设置来电信息
 *
 * @param caller_info 来电者信息字符串
 */
void ui_manager_set_caller_info(const char *caller_info);

/**
 * @brief 安全的LCD绘制函数（带互斥锁保护）
 *
 * @param x_start 起始X坐标
 * @param y_start 起始Y坐标
 * @param x_end 结束X坐标
 * @param y_end 结束Y坐标
 * @param color_data 颜色数据
 * @return
 *      - ESP_OK: 成功
 *      - ESP_FAIL: 失败
 */
esp_err_t ui_safe_lcd_draw_bitmap(int x_start, int y_start, int x_end, int y_end, const void *color_data);

#ifdef __cplusplus
}
#endif
