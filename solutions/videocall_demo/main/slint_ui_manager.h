/* Slint UI Manager for Video Call Demo
 * 
 * 基于Slint的UI管理器，替代原有的手动绘制UI
 */

#pragma once

#include <memory>
#include <string>
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief UI状态枚举
 */
typedef enum {
    SLINT_UI_STATE_SCREENSAVER = 0,    // 屏保状态
    SLINT_UI_STATE_INCOMING_CALL = 1,  // 来电状态
    SLINT_UI_STATE_IN_CALL = 2,        // 通话中状态
    SLINT_UI_STATE_CALL_ENDED = 3,     // 通话结束状态
} slint_ui_state_t;

/**
 * @brief Slint UI管理器配置结构
 */
typedef struct {
    void *lcd_panel_handle;  // LCD面板句柄
    void *touch_handle;      // 触摸句柄
    int screen_width;        // 屏幕宽度
    int screen_height;       // 屏幕高度
} slint_ui_config_t;

/**
 * @brief UI事件回调函数类型
 */
typedef void (*slint_ui_callback_t)(void);

/**
 * @brief 初始化Slint UI管理器
 * 
 * @param config UI配置
 * @return 
 *      - ESP_OK: 成功
 *      - ESP_FAIL: 失败
 */
esp_err_t slint_ui_manager_init(const slint_ui_config_t *config);

/**
 * @brief 启动Slint UI管理器
 * 
 * @return 
 *      - ESP_OK: 成功
 *      - ESP_FAIL: 失败
 */
esp_err_t slint_ui_manager_start(void);

/**
 * @brief 停止Slint UI管理器
 */
void slint_ui_manager_stop(void);

/**
 * @brief 设置UI状态
 * 
 * @param state 新的UI状态
 */
void slint_ui_set_state(slint_ui_state_t state);

/**
 * @brief 获取当前UI状态
 * 
 * @return 当前UI状态
 */
slint_ui_state_t slint_ui_get_state(void);

/**
 * @brief 更新时间显示
 * 
 * @param time_str 时间字符串 (HH:MM)
 * @param date_str 日期字符串 (YYYY-MM-DD)
 */
void slint_ui_update_time(const char *time_str, const char *date_str);

/**
 * @brief 设置来电者信息
 * 
 * @param caller_info 来电者信息字符串
 */
void slint_ui_set_caller_info(const char *caller_info);

/**
 * @brief 设置接听回调函数
 * 
 * @param callback 接听回调函数
 */
void slint_ui_set_accept_callback(slint_ui_callback_t callback);

/**
 * @brief 设置拒绝回调函数
 * 
 * @param callback 拒绝回调函数
 */
void slint_ui_set_reject_callback(slint_ui_callback_t callback);

/**
 * @brief 设置挂断回调函数
 * 
 * @param callback 挂断回调函数
 */
void slint_ui_set_hangup_callback(slint_ui_callback_t callback);

/**
 * @brief 运行UI事件循环（非阻塞）
 */
void slint_ui_run_event_loop(void);

#ifdef __cplusplus
}
#endif
