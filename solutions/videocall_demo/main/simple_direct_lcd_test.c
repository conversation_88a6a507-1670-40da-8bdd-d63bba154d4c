/*
 * 简化的直接LCD测试
 * 最简单的LCD硬件测试，避免复杂的函数调用
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "codec_init.h"
#include "esp_lcd_panel_ops.h"

static const char *TAG = "SIMPLE_LCD_TEST";

// RGB565颜色定义
#define RGB565_RED      0xF800
#define RGB565_GREEN    0x07E0
#define RGB565_BLUE     0x001F
#define RGB565_WHITE    0xFFFF

// 最简单的LCD测试
void simple_direct_lcd_test(void)
{
    ESP_LOGI(TAG, "=== 最简单的LCD测试开始 ===");

    // 获取LCD面板句柄
    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    if (lcd_handle == NULL) {
        ESP_LOGE(TAG, "无法获取LCD句柄");
        return;
    }

    ESP_LOGI(TAG, "LCD句柄获取成功: %p", lcd_handle);

    // 分配最小的测试缓冲区
    const int width = 50;
    const int height = 50;
    const int buffer_size = width * height * 2; // RGB565 = 2 bytes per pixel
    
    uint16_t *buffer = (uint16_t *)malloc(buffer_size);
    if (buffer == NULL) {
        ESP_LOGE(TAG, "内存分配失败");
        return;
    }

    ESP_LOGI(TAG, "缓冲区分配成功: %d bytes", buffer_size);

    // 填充红色
    for (int i = 0; i < width * height; i++) {
        buffer[i] = RGB565_RED;
    }

    ESP_LOGI(TAG, "开始绘制红色方块 50x50...");

    // 尝试绘制
    esp_err_t ret = esp_lcd_panel_draw_bitmap(lcd_handle, 0, 0, width, height, buffer);
    
    ESP_LOGI(TAG, "绘制结果: %s (0x%x)", esp_err_to_name(ret), ret);

    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ LCD绘制成功！");
    } else {
        ESP_LOGE(TAG, "❌ LCD绘制失败: %s", esp_err_to_name(ret));
    }

    // 等待5秒
    ESP_LOGI(TAG, "等待5秒观察效果...");
    vTaskDelay(pdMS_TO_TICKS(5000));

    // 测试绿色
    for (int i = 0; i < width * height; i++) {
        buffer[i] = RGB565_GREEN;
    }

    ESP_LOGI(TAG, "开始绘制绿色方块...");
    ret = esp_lcd_panel_draw_bitmap(lcd_handle, 100, 100, width + 100, height + 100, buffer);
    ESP_LOGI(TAG, "绿色绘制结果: %s", esp_err_to_name(ret));

    vTaskDelay(pdMS_TO_TICKS(3000));

    // 测试蓝色
    for (int i = 0; i < width * height; i++) {
        buffer[i] = RGB565_BLUE;
    }

    ESP_LOGI(TAG, "开始绘制蓝色方块...");
    ret = esp_lcd_panel_draw_bitmap(lcd_handle, 200, 200, width + 200, height + 200, buffer);
    ESP_LOGI(TAG, "蓝色绘制结果: %s", esp_err_to_name(ret));

    vTaskDelay(pdMS_TO_TICKS(3000));

    // 测试白色
    for (int i = 0; i < width * height; i++) {
        buffer[i] = RGB565_WHITE;
    }

    ESP_LOGI(TAG, "开始绘制白色方块...");
    ret = esp_lcd_panel_draw_bitmap(lcd_handle, 300, 300, width + 300, height + 300, buffer);
    ESP_LOGI(TAG, "白色绘制结果: %s", esp_err_to_name(ret));

    // 清理
    free(buffer);
    ESP_LOGI(TAG, "=== 简单LCD测试完成 ===");
}

// 测试LCD面板状态
void test_lcd_panel_status(void)
{
    ESP_LOGI(TAG, "=== LCD面板状态测试 ===");

    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    if (lcd_handle == NULL) {
        ESP_LOGE(TAG, "无法获取LCD句柄");
        return;
    }

    // 测试显示开关
    ESP_LOGI(TAG, "测试显示关闭...");
    esp_err_t ret = esp_lcd_panel_disp_on_off(lcd_handle, false);
    ESP_LOGI(TAG, "显示关闭结果: %s", esp_err_to_name(ret));
    
    vTaskDelay(pdMS_TO_TICKS(2000));

    ESP_LOGI(TAG, "测试显示开启...");
    ret = esp_lcd_panel_disp_on_off(lcd_handle, true);
    ESP_LOGI(TAG, "显示开启结果: %s", esp_err_to_name(ret));

    ESP_LOGI(TAG, "=== LCD面板状态测试完成 ===");
}

// 启动简单测试
void start_simple_direct_lcd_test(void)
{
    // 先测试面板状态
    test_lcd_panel_status();
    
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 再测试绘制
    simple_direct_lcd_test();
}
