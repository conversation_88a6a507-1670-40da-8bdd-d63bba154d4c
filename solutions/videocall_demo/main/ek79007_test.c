/*
 * EK79007面板测试程序
 * 基于官方demo，验证EK79007面板的显示功能
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "esp_heap_caps.h"
#include "esp_lcd_panel_ops.h"
#include "esp_lcd_panel_io.h"
#include "esp_ldo_regulator.h"
#include "esp_lcd_mipi_dsi.h"
#include "esp_lcd_ek79007.h"

static const char *TAG = "EK79007_TEST";

#define TEST_LCD_H_RES                  (1024)
#define TEST_LCD_V_RES                  (600)
#define TEST_LCD_BIT_PER_PIXEL          (16)  // 使用RGB565
#define TEST_PIN_NUM_LCD_RST            (27)    // 使用GPIO 27作为复位引脚（官方文档）
#define TEST_PIN_NUM_BK_LIGHT           (26)    // 使用GPIO 26作为背光（官方文档）
#define TEST_LCD_BK_LIGHT_ON_LEVEL      (1)
#define TEST_MIPI_DSI_LANE_NUM          (2)
#define TEST_MIPI_DPI_PX_FORMAT         (LCD_COLOR_PIXEL_FORMAT_RGB565)
#define TEST_MIPI_DSI_PHY_PWR_LDO_CHAN  (3)
#define TEST_MIPI_DSI_PHY_PWR_LDO_VOLTAGE_MV (2500)

static esp_ldo_channel_handle_t ldo_mipi_phy = NULL;
static esp_lcd_panel_handle_t panel_handle = NULL;
static esp_lcd_dsi_bus_handle_t mipi_dsi_bus = NULL;
static esp_lcd_panel_io_handle_t mipi_dbi_io = NULL;
static SemaphoreHandle_t refresh_finish = NULL;

IRAM_ATTR static bool test_notify_refresh_ready(esp_lcd_panel_handle_t panel, esp_lcd_dpi_panel_event_data_t *edata, void *user_ctx)
{
    SemaphoreHandle_t refresh_finish = (SemaphoreHandle_t)user_ctx;
    BaseType_t need_yield = pdFALSE;

    xSemaphoreGiveFromISR(refresh_finish, &need_yield);

    return (need_yield == pdTRUE);
}

static int test_init_ek79007_lcd(void)
{
    ESP_LOGI(TAG, "=== EK79007面板初始化开始 ===");

    // 1. 先开启背光
    if (TEST_PIN_NUM_BK_LIGHT >= 0) {
        ESP_LOGI(TAG, "Turn on LCD backlight on GPIO %d", TEST_PIN_NUM_BK_LIGHT);
        gpio_config_t bk_gpio_config = {
            .mode = GPIO_MODE_OUTPUT,
            .pin_bit_mask = 1ULL << TEST_PIN_NUM_BK_LIGHT
        };
        esp_err_t ret = gpio_config(&bk_gpio_config);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to configure backlight GPIO: %s", esp_err_to_name(ret));
            return -1;
        }
        ret = gpio_set_level(TEST_PIN_NUM_BK_LIGHT, TEST_LCD_BK_LIGHT_ON_LEVEL);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to set backlight level: %s", esp_err_to_name(ret));
            return -1;
        }
        ESP_LOGI(TAG, "✅ LCD backlight enabled");
    }

    // 2. 开启MIPI DSI PHY电源
    ESP_LOGI(TAG, "MIPI DSI PHY Powered on");
    esp_ldo_channel_config_t ldo_mipi_phy_config = {
        .chan_id = TEST_MIPI_DSI_PHY_PWR_LDO_CHAN,
        .voltage_mv = TEST_MIPI_DSI_PHY_PWR_LDO_VOLTAGE_MV,
    };
    esp_err_t ret = esp_ldo_acquire_channel(&ldo_mipi_phy_config, &ldo_mipi_phy);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to acquire LDO channel: %s", esp_err_to_name(ret));
        return -1;
    }

    // 3. 初始化MIPI DSI总线
    ESP_LOGI(TAG, "Initialize MIPI DSI bus");
    esp_lcd_dsi_bus_config_t bus_config = EK79007_PANEL_BUS_DSI_2CH_CONFIG();
    ret = esp_lcd_new_dsi_bus(&bus_config, &mipi_dsi_bus);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create DSI bus: %s", esp_err_to_name(ret));
        return -1;
    }

    // 4. 安装面板IO
    ESP_LOGI(TAG, "Install panel IO");
    esp_lcd_dbi_io_config_t dbi_config = EK79007_PANEL_IO_DBI_CONFIG();
    ret = esp_lcd_new_panel_io_dbi(mipi_dsi_bus, &dbi_config, &mipi_dbi_io);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create panel IO: %s", esp_err_to_name(ret));
        return -1;
    }

    // 5. 安装EK79007 LCD驱动
    ESP_LOGI(TAG, "Install LCD driver of ek79007");
    esp_lcd_dpi_panel_config_t dpi_config = EK79007_1024_600_PANEL_60HZ_CONFIG(TEST_MIPI_DPI_PX_FORMAT);
    ek79007_vendor_config_t vendor_config = {
        .mipi_config = {
            .dsi_bus = mipi_dsi_bus,
            .dpi_config = &dpi_config,
            .lane_num = TEST_MIPI_DSI_LANE_NUM,
        },
    };
    const esp_lcd_panel_dev_config_t panel_config = {
        .reset_gpio_num = TEST_PIN_NUM_LCD_RST,
        .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB,
        .bits_per_pixel = TEST_LCD_BIT_PER_PIXEL,
        .vendor_config = &vendor_config,
    };
    ret = esp_lcd_new_panel_ek79007(mipi_dbi_io, &panel_config, &panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create EK79007 panel: %s", esp_err_to_name(ret));
        return -1;
    }

    // 6. 复位和初始化面板
    ret = esp_lcd_panel_reset(panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to reset panel: %s", esp_err_to_name(ret));
        return -1;
    }
    
    ret = esp_lcd_panel_init(panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to init panel: %s", esp_err_to_name(ret));
        return -1;
    }

    // 7. 设置刷新完成回调
    refresh_finish = xSemaphoreCreateBinary();
    if (refresh_finish == NULL) {
        ESP_LOGE(TAG, "Failed to create semaphore");
        return -1;
    }
    
    esp_lcd_dpi_panel_event_callbacks_t cbs = {
        .on_color_trans_done = test_notify_refresh_ready,
    };
    ret = esp_lcd_dpi_panel_register_event_callbacks(panel_handle, &cbs, refresh_finish);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register callbacks: %s", esp_err_to_name(ret));
        return -1;
    }

    ESP_LOGI(TAG, "✅ EK79007面板初始化完成");
    return 0;
}

static void test_draw_color_blocks(void)
{
    ESP_LOGI(TAG, "=== 绘制彩色方块测试 ===");

    // RGB565颜色定义
    uint16_t colors[] = {
        0xF800, // 红色
        0x07E0, // 绿色
        0x001F, // 蓝色
        0xFFFF, // 白色
        0xFFE0, // 黄色
        0xF81F, // 洋红
        0x07FF, // 青色
        0x0000, // 黑色
    };
    
    const char* color_names[] = {
        "红色", "绿色", "蓝色", "白色", "黄色", "洋红", "青色", "黑色"
    };

    int block_width = TEST_LCD_H_RES / 4;
    int block_height = TEST_LCD_V_RES / 2;
    
    // 分配缓冲区
    uint16_t *color_buf = (uint16_t *)heap_caps_malloc(block_width * block_height * 2, MALLOC_CAP_DMA);
    if (color_buf == NULL) {
        ESP_LOGE(TAG, "Failed to allocate color buffer");
        return;
    }

    for (int i = 0; i < 8; i++) {
        int x = (i % 4) * block_width;
        int y = (i / 4) * block_height;
        
        ESP_LOGI(TAG, "绘制%s方块 (%d,%d)", color_names[i], x, y);
        
        // 填充颜色
        for (int j = 0; j < block_width * block_height; j++) {
            color_buf[j] = colors[i];
        }
        
        // 绘制到屏幕
        esp_err_t ret = esp_lcd_panel_draw_bitmap(panel_handle, x, y, x + block_width, y + block_height, color_buf);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "✅ %s方块绘制成功", color_names[i]);
            // 等待刷新完成
            xSemaphoreTake(refresh_finish, portMAX_DELAY);
        } else {
            ESP_LOGE(TAG, "❌ %s方块绘制失败: %s", color_names[i], esp_err_to_name(ret));
        }
        
        vTaskDelay(pdMS_TO_TICKS(500)); // 延时500ms
    }

    free(color_buf);
    ESP_LOGI(TAG, "=== 彩色方块测试完成 ===");
}

static void test_hardware_pattern(void)
{
    ESP_LOGI(TAG, "=== 硬件测试图案 ===");

    ESP_LOGI(TAG, "显示垂直条纹");
    esp_err_t ret = esp_lcd_dpi_panel_set_pattern(panel_handle, MIPI_DSI_PATTERN_BAR_VERTICAL);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ 垂直条纹显示成功");
    } else {
        ESP_LOGE(TAG, "❌ 垂直条纹显示失败: %s", esp_err_to_name(ret));
    }
    vTaskDelay(pdMS_TO_TICKS(3000));

    ESP_LOGI(TAG, "显示水平条纹");
    ret = esp_lcd_dpi_panel_set_pattern(panel_handle, MIPI_DSI_PATTERN_BAR_HORIZONTAL);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ 水平条纹显示成功");
    } else {
        ESP_LOGE(TAG, "❌ 水平条纹显示失败: %s", esp_err_to_name(ret));
    }
    vTaskDelay(pdMS_TO_TICKS(3000));

    ESP_LOGI(TAG, "关闭硬件测试图案");
    ret = esp_lcd_dpi_panel_set_pattern(panel_handle, MIPI_DSI_PATTERN_NONE);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ 硬件图案关闭成功");
    } else {
        ESP_LOGE(TAG, "❌ 硬件图案关闭失败: %s", esp_err_to_name(ret));
    }

    ESP_LOGI(TAG, "=== 硬件测试图案完成 ===");
}

// EK79007测试任务
static void ek79007_test_task(void *param)
{
    // 初始化EK79007面板
    if (test_init_ek79007_lcd() != 0) {
        ESP_LOGE(TAG, "EK79007面板初始化失败");
        vTaskDelete(NULL);
        return;
    }

    // 测试硬件图案
    test_hardware_pattern();

    // 测试软件绘制
    test_draw_color_blocks();

    ESP_LOGI(TAG, "=== EK79007测试完成 ===");
    vTaskDelete(NULL);
}

// 启动EK79007测试
void start_ek79007_test(void)
{
    xTaskCreate(ek79007_test_task, "ek79007_test", 8192, NULL, 5, NULL);
}
