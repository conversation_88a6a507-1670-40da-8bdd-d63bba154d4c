idf_component_register(SRCS "webrtc.c"  "main.cpp" "board.c" "media_sys.c" "test_lcd_render.c" "direct_lcd_test.c" "simple_direct_lcd_test.c" "backlight_test.c" "ek79007_test.c" "multi_gpio_test.c" "simple_ek79007_test.c" "lcd_diagnostic.c" "lcd_reinit.c" "ui_manager.c" "screensaver.c" "call_ui.c" "slint_ui_manager.cpp"
                       EMBED_TXTFILES "ring.aac"
                       INCLUDE_DIRS "."
                       REQUIRES slint)

# 编译Slint UI文件
slint_target_sources(${COMPONENT_LIB} videocall-ui.slint)
