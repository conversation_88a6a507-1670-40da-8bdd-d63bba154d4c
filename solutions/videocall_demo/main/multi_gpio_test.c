/*
 * 多GPIO背光测试程序
 * 自动测试多个可能的背光GPIO引脚
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "esp_heap_caps.h"
#include "esp_lcd_panel_ops.h"
#include "esp_lcd_panel_io.h"
#include "esp_ldo_regulator.h"
#include "esp_lcd_mipi_dsi.h"
#include "esp_lcd_ek79007.h"

static const char *TAG = "MULTI_GPIO_TEST";

// 可能的背光GPIO引脚列表
static const int backlight_pins[] = {
    22,  // 官方demo使用的引脚
    26,  // 你之前发现的引脚
    45,  // 常见的背光控制引脚
    46,  // 其他可能的引脚
    47,
    48,
    49,
    50,
    51,
    52,
    54,
    -1   // 结束标记
};

static const char* pin_names[] = {
    "GPIO 22 (官方demo)",
    "GPIO 26 (用户发现)",
    "GPIO 45",
    "GPIO 46",
    "GPIO 47",
    "GPIO 48",
    "GPIO 49",
    "GPIO 50",
    "GPIO 51",
    "GPIO 52",
    "GPIO 54",
};

#define TEST_LCD_H_RES                  (1024)
#define TEST_LCD_V_RES                  (600)
#define TEST_LCD_BIT_PER_PIXEL          (16)
#define TEST_PIN_NUM_LCD_RST            (-1)
#define TEST_LCD_BK_LIGHT_ON_LEVEL      (1)
#define TEST_MIPI_DSI_LANE_NUM          (2)
#define TEST_MIPI_DPI_PX_FORMAT         (LCD_COLOR_PIXEL_FORMAT_RGB565)
#define TEST_MIPI_DSI_PHY_PWR_LDO_CHAN  (3)
#define TEST_MIPI_DSI_PHY_PWR_LDO_VOLTAGE_MV (2500)

static esp_ldo_channel_handle_t ldo_mipi_phy = NULL;
static esp_lcd_panel_handle_t panel_handle = NULL;
static esp_lcd_dsi_bus_handle_t mipi_dsi_bus = NULL;
static esp_lcd_panel_io_handle_t mipi_dbi_io = NULL;
static SemaphoreHandle_t refresh_finish = NULL;

IRAM_ATTR static bool test_notify_refresh_ready(esp_lcd_panel_handle_t panel, esp_lcd_dpi_panel_event_data_t *edata, void *user_ctx)
{
    SemaphoreHandle_t refresh_finish = (SemaphoreHandle_t)user_ctx;
    BaseType_t need_yield = pdFALSE;

    xSemaphoreGiveFromISR(refresh_finish, &need_yield);

    return (need_yield == pdTRUE);
}

static int test_init_ek79007_with_gpio(int backlight_gpio)
{
    ESP_LOGI(TAG, "=== 使用%s初始化EK79007面板 ===", 
             backlight_gpio == 22 ? "GPIO 22 (官方demo)" :
             backlight_gpio == 26 ? "GPIO 26 (用户发现)" :
             "其他GPIO");

    // 1. 先开启背光
    if (backlight_gpio >= 0) {
        ESP_LOGI(TAG, "Turn on LCD backlight on GPIO %d", backlight_gpio);
        gpio_config_t bk_gpio_config = {
            .mode = GPIO_MODE_OUTPUT,
            .pin_bit_mask = 1ULL << backlight_gpio
        };
        esp_err_t ret = gpio_config(&bk_gpio_config);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to configure backlight GPIO %d: %s", backlight_gpio, esp_err_to_name(ret));
            return -1;
        }
        ret = gpio_set_level(backlight_gpio, TEST_LCD_BK_LIGHT_ON_LEVEL);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to set backlight level on GPIO %d: %s", backlight_gpio, esp_err_to_name(ret));
            return -1;
        }
        ESP_LOGI(TAG, "✅ LCD backlight enabled on GPIO %d", backlight_gpio);
    }

    // 2. 开启MIPI DSI PHY电源
    ESP_LOGI(TAG, "MIPI DSI PHY Powered on");
    esp_ldo_channel_config_t ldo_mipi_phy_config = {
        .chan_id = TEST_MIPI_DSI_PHY_PWR_LDO_CHAN,
        .voltage_mv = TEST_MIPI_DSI_PHY_PWR_LDO_VOLTAGE_MV,
    };
    esp_err_t ret = esp_ldo_acquire_channel(&ldo_mipi_phy_config, &ldo_mipi_phy);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to acquire LDO channel: %s", esp_err_to_name(ret));
        return -1;
    }

    // 3. 初始化MIPI DSI总线
    ESP_LOGI(TAG, "Initialize MIPI DSI bus");
    esp_lcd_dsi_bus_config_t bus_config = EK79007_PANEL_BUS_DSI_2CH_CONFIG();
    ret = esp_lcd_new_dsi_bus(&bus_config, &mipi_dsi_bus);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create DSI bus: %s", esp_err_to_name(ret));
        return -1;
    }

    // 4. 安装面板IO
    ESP_LOGI(TAG, "Install panel IO");
    esp_lcd_dbi_io_config_t dbi_config = EK79007_PANEL_IO_DBI_CONFIG();
    ret = esp_lcd_new_panel_io_dbi(mipi_dsi_bus, &dbi_config, &mipi_dbi_io);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create panel IO: %s", esp_err_to_name(ret));
        return -1;
    }

    // 5. 安装EK79007 LCD驱动
    ESP_LOGI(TAG, "Install LCD driver of ek79007");
    esp_lcd_dpi_panel_config_t dpi_config = EK79007_1024_600_PANEL_60HZ_CONFIG(TEST_MIPI_DPI_PX_FORMAT);
    ek79007_vendor_config_t vendor_config = {
        .mipi_config = {
            .dsi_bus = mipi_dsi_bus,
            .dpi_config = &dpi_config,
            .lane_num = TEST_MIPI_DSI_LANE_NUM,
        },
    };
    const esp_lcd_panel_dev_config_t panel_config = {
        .reset_gpio_num = TEST_PIN_NUM_LCD_RST,
        .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB,
        .bits_per_pixel = TEST_LCD_BIT_PER_PIXEL,
        .vendor_config = &vendor_config,
    };
    ret = esp_lcd_new_panel_ek79007(mipi_dbi_io, &panel_config, &panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create EK79007 panel: %s", esp_err_to_name(ret));
        return -1;
    }

    // 6. 复位和初始化面板
    ret = esp_lcd_panel_reset(panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to reset panel: %s", esp_err_to_name(ret));
        return -1;
    }
    
    ret = esp_lcd_panel_init(panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to init panel: %s", esp_err_to_name(ret));
        return -1;
    }

    // 7. 设置刷新完成回调
    refresh_finish = xSemaphoreCreateBinary();
    if (refresh_finish == NULL) {
        ESP_LOGE(TAG, "Failed to create semaphore");
        return -1;
    }
    
    esp_lcd_dpi_panel_event_callbacks_t cbs = {
        .on_color_trans_done = test_notify_refresh_ready,
    };
    ret = esp_lcd_dpi_panel_register_event_callbacks(panel_handle, &cbs, refresh_finish);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register callbacks: %s", esp_err_to_name(ret));
        return -1;
    }

    ESP_LOGI(TAG, "✅ EK79007面板初始化完成");
    return 0;
}

static void test_draw_simple_pattern(void)
{
    ESP_LOGI(TAG, "=== 绘制简单测试图案 ===");

    // 分配全屏缓冲区
    uint16_t *color_buf = (uint16_t *)heap_caps_malloc(TEST_LCD_H_RES * TEST_LCD_V_RES * 2, MALLOC_CAP_DMA);
    if (color_buf == NULL) {
        ESP_LOGE(TAG, "Failed to allocate full screen buffer");
        return;
    }

    // 绘制红色全屏
    ESP_LOGI(TAG, "绘制红色全屏...");
    for (int i = 0; i < TEST_LCD_H_RES * TEST_LCD_V_RES; i++) {
        color_buf[i] = 0xF800; // 红色
    }
    
    esp_err_t ret = esp_lcd_panel_draw_bitmap(panel_handle, 0, 0, TEST_LCD_H_RES, TEST_LCD_V_RES, color_buf);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ 红色全屏绘制成功");
        xSemaphoreTake(refresh_finish, portMAX_DELAY);
    } else {
        ESP_LOGE(TAG, "❌ 红色全屏绘制失败: %s", esp_err_to_name(ret));
    }

    free(color_buf);
    ESP_LOGI(TAG, "=== 简单测试图案完成 ===");
}

static void cleanup_ek79007(void)
{
    if (panel_handle) {
        esp_lcd_panel_del(panel_handle);
        panel_handle = NULL;
    }
    if (mipi_dbi_io) {
        esp_lcd_panel_io_del(mipi_dbi_io);
        mipi_dbi_io = NULL;
    }
    if (mipi_dsi_bus) {
        esp_lcd_del_dsi_bus(mipi_dsi_bus);
        mipi_dsi_bus = NULL;
    }
    if (ldo_mipi_phy) {
        esp_ldo_release_channel(ldo_mipi_phy);
        ldo_mipi_phy = NULL;
    }
    if (refresh_finish) {
        vSemaphoreDelete(refresh_finish);
        refresh_finish = NULL;
    }
}

// 多GPIO测试任务
static void multi_gpio_test_task(void *param)
{
    ESP_LOGI(TAG, "=== 开始多GPIO背光测试 ===");
    ESP_LOGI(TAG, "将依次测试%d个可能的背光GPIO引脚", sizeof(backlight_pins)/sizeof(int) - 1);

    for (int i = 0; backlight_pins[i] >= 0; i++) {
        int gpio = backlight_pins[i];
        
        ESP_LOGI(TAG, "\n🔍 测试 %s", pin_names[i]);
        ESP_LOGI(TAG, "请观察屏幕是否显示红色...");

        // 初始化EK79007面板
        if (test_init_ek79007_with_gpio(gpio) == 0) {
            // 绘制测试图案
            test_draw_simple_pattern();
            
            ESP_LOGI(TAG, "⏰ 等待10秒观察效果...");
            vTaskDelay(pdMS_TO_TICKS(10000));
            
            ESP_LOGI(TAG, "如果看到红色屏幕，说明GPIO %d是正确的背光引脚！", gpio);
        } else {
            ESP_LOGE(TAG, "GPIO %d初始化失败", gpio);
        }

        // 清理资源
        cleanup_ek79007();
        
        ESP_LOGI(TAG, "等待3秒后测试下一个GPIO...\n");
        vTaskDelay(pdMS_TO_TICKS(3000));
    }

    ESP_LOGI(TAG, "=== 多GPIO背光测试完成 ===");
    ESP_LOGI(TAG, "如果某个GPIO测试时屏幕显示了红色，那就是正确的背光引脚！");
    
    vTaskDelete(NULL);
}

// 启动多GPIO测试
void start_multi_gpio_test(void)
{
    xTaskCreate(multi_gpio_test_task, "multi_gpio_test", 12288, NULL, 5, NULL);
}
