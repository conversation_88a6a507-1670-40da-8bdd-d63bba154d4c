/* Screensaver Implementation
 * 
 * 屏保界面实现 - 显示当前时间和日期
 */

#include "screensaver.h"
#include "ui_manager.h"
#include "esp_log.h"
#include "esp_sntp.h"
#include <time.h>
#include <sys/time.h>
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "SCREENSAVER";

// 屏保状态
typedef struct {
    bool initialized;
    esp_lcd_panel_handle_t lcd_handle;
    int screen_width;
    int screen_height;
    uint16_t bg_color;
    uint16_t text_color;
    char last_time_str[32];
    char last_date_str[32];
} screensaver_t;

static screensaver_t s_screensaver = {0};

// 简单的字体数据 (8x16像素，数字0-9和冒号)
static const uint8_t font_8x16[][16] = {
    // '0'
    {0x3C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C},
    // '1'
    {0x18, 0x38, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x7E},
    // '2'
    {0x3C, 0x66, 0x66, 0x06, 0x06, 0x0C, 0x18, 0x30, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x66, 0x7E},
    // '3'
    {0x3C, 0x66, 0x66, 0x06, 0x06, 0x0C, 0x1C, 0x0C, 0x06, 0x06, 0x06, 0x06, 0x66, 0x66, 0x66, 0x3C},
    // '4'
    {0x0C, 0x1C, 0x1C, 0x2C, 0x2C, 0x4C, 0x4C, 0x8C, 0xFE, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},
    // '5'
    {0x7E, 0x60, 0x60, 0x60, 0x60, 0x7C, 0x66, 0x06, 0x06, 0x06, 0x06, 0x06, 0x66, 0x66, 0x66, 0x3C},
    // '6'
    {0x3C, 0x66, 0x60, 0x60, 0x60, 0x7C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C},
    // '7'
    {0x7E, 0x06, 0x06, 0x0C, 0x0C, 0x18, 0x18, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30},
    // '8'
    {0x3C, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C},
    // '9'
    {0x3C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3E, 0x06, 0x06, 0x06, 0x06, 0x66, 0x66, 0x3C},
    // ':'
    {0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00},
    // ' '
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
};

// 获取字符对应的字体索引
static int get_font_index(char c)
{
    if (c >= '0' && c <= '9') {
        return c - '0';
    } else if (c == ':') {
        return 10;
    } else {
        return 11; // 空格
    }
}

// 使用全局安全绘制函数
static esp_err_t safe_lcd_draw_bitmap(int x_start, int y_start, int x_end, int y_end, const void *color_data)
{
    return ui_safe_lcd_draw_bitmap(x_start, y_start, x_end, y_end, color_data);
}

// 在指定位置绘制字符
static void draw_char(int x, int y, char c, uint16_t color)
{
    if (!s_screensaver.initialized) {
        return;
    }

    int font_idx = get_font_index(c);
    const uint8_t *font_data = font_8x16[font_idx];

    // 创建字符缓冲区
    uint16_t char_buffer[8 * 16];

    for (int row = 0; row < 16; row++) {
        uint8_t line = font_data[row];
        for (int col = 0; col < 8; col++) {
            if (line & (0x80 >> col)) {
                char_buffer[row * 8 + col] = color;
            } else {
                char_buffer[row * 8 + col] = s_screensaver.bg_color;
            }
        }
    }

    // 安全绘制到LCD，如果失败就跳过
    esp_err_t ret = safe_lcd_draw_bitmap(x, y, x + 8, y + 16, char_buffer);
    if (ret != ESP_OK) {
        // 绘制失败，添加延时
        vTaskDelay(pdMS_TO_TICKS(20));
    }
}

// 在指定位置绘制字符串
static void draw_string(int x, int y, const char *str, uint16_t color)
{
    if (!s_screensaver.initialized || str == NULL) {
        return;
    }

    int pos_x = x;
    for (int i = 0; str[i] != '\0'; i++) {
        draw_char(pos_x, y, str[i], color);
        pos_x += 8; // 字符宽度
        // 在字符之间添加小延时，避免LCD过载
        vTaskDelay(pdMS_TO_TICKS(5));
    }
}

// 清除屏幕
static void clear_screen(void)
{
    if (!s_screensaver.initialized) {
        return;
    }
    
    // 创建背景色缓冲区
    const int buffer_size = 100 * 100; // 分块清除
    uint16_t *buffer = (uint16_t *)malloc(buffer_size * sizeof(uint16_t));
    if (buffer == NULL) {
        ESP_LOGE(TAG, "内存分配失败");
        return;
    }
    
    // 填充背景色
    for (int i = 0; i < buffer_size; i++) {
        buffer[i] = s_screensaver.bg_color;
    }
    
    // 分块清除屏幕
    for (int y = 0; y < s_screensaver.screen_height; y += 100) {
        for (int x = 0; x < s_screensaver.screen_width; x += 100) {
            int w = (x + 100 > s_screensaver.screen_width) ? (s_screensaver.screen_width - x) : 100;
            int h = (y + 100 > s_screensaver.screen_height) ? (s_screensaver.screen_height - y) : 100;
            safe_lcd_draw_bitmap(x, y, x + w, y + h, buffer);
            // 添加小延时，避免LCD过载
            vTaskDelay(pdMS_TO_TICKS(10));
        }
    }
    
    free(buffer);
}

esp_err_t screensaver_init(const screensaver_config_t *config)
{
    if (config == NULL || config->lcd_handle == NULL) {
        ESP_LOGE(TAG, "无效的配置参数");
        return ESP_ERR_INVALID_ARG;
    }
    
    if (s_screensaver.initialized) {
        ESP_LOGW(TAG, "屏保已经初始化");
        return ESP_OK;
    }
    
    // 初始化屏保状态
    memset(&s_screensaver, 0, sizeof(screensaver_t));
    s_screensaver.lcd_handle = config->lcd_handle;
    s_screensaver.screen_width = config->screen_width;
    s_screensaver.screen_height = config->screen_height;
    s_screensaver.bg_color = config->bg_color;
    s_screensaver.text_color = config->text_color;
    s_screensaver.initialized = true;
    
    ESP_LOGI(TAG, "屏保初始化成功 (分辨率: %dx%d)", config->screen_width, config->screen_height);
    
    return ESP_OK;
}

void screensaver_show(void)
{
    if (!s_screensaver.initialized) {
        ESP_LOGW(TAG, "屏保未初始化");
        return;
    }

    ESP_LOGI(TAG, "显示屏保界面");

    // 清除屏幕
    clear_screen();

    // 显示标题
    const char *title = "ESP32 Video Call";
    int title_x = (s_screensaver.screen_width - strlen(title) * 8) / 2;
    int title_y = 100;
    draw_string(title_x, title_y, title, s_screensaver.text_color);

    // 显示时间（初始显示）
    screensaver_update_time();
}

void screensaver_update_time(void)
{
    if (!s_screensaver.initialized) {
        return;
    }

    // 获取当前时间
    time_t now;
    struct tm timeinfo;
    time(&now);
    localtime_r(&now, &timeinfo);

    // 格式化时间字符串（只显示分钟，减少更新频率）
    char time_str[32];
    char date_str[32];
    strftime(time_str, sizeof(time_str), "%H:%M", &timeinfo);
    strftime(date_str, sizeof(date_str), "%Y-%m-%d", &timeinfo);

    // 检查时间是否发生变化（只在分钟变化时更新）
    if (strcmp(time_str, s_screensaver.last_time_str) != 0) {
        ESP_LOGI(TAG, "更新时间显示: %s", time_str);

        // 清除旧的时间显示区域
        int time_x = (s_screensaver.screen_width - 5 * 8) / 2; // "HH:MM" = 5字符
        int time_y = 250;

        // 用背景色覆盖旧时间
        uint16_t *clear_buffer = (uint16_t *)malloc(5 * 8 * 16 * sizeof(uint16_t));
        if (clear_buffer != NULL) {
            for (int i = 0; i < 5 * 8 * 16; i++) {
                clear_buffer[i] = s_screensaver.bg_color;
            }
            esp_err_t ret = safe_lcd_draw_bitmap(time_x, time_y, time_x + 5 * 8, time_y + 16, clear_buffer);
            free(clear_buffer);

            // 只有清除成功才显示新时间
            if (ret == ESP_OK) {
                // 添加延时避免冲突
                vTaskDelay(pdMS_TO_TICKS(50));
                draw_string(time_x, time_y, time_str, s_screensaver.text_color);
            }
        }

        strcpy(s_screensaver.last_time_str, time_str);
    }

    // 检查日期是否发生变化
    if (strcmp(date_str, s_screensaver.last_date_str) != 0) {
        // 清除旧的日期显示区域
        int date_x = (s_screensaver.screen_width - 10 * 8) / 2; // "YYYY-MM-DD" = 10字符
        int date_y = 300;

        // 用背景色覆盖旧日期
        uint16_t *clear_buffer = (uint16_t *)malloc(10 * 8 * 16 * sizeof(uint16_t));
        if (clear_buffer != NULL) {
            for (int i = 0; i < 10 * 8 * 16; i++) {
                clear_buffer[i] = s_screensaver.bg_color;
            }
            esp_err_t ret = safe_lcd_draw_bitmap(date_x, date_y, date_x + 10 * 8, date_y + 16, clear_buffer);
            free(clear_buffer);

            // 只有清除成功才显示新日期
            if (ret == ESP_OK) {
                // 添加延时避免冲突
                vTaskDelay(pdMS_TO_TICKS(50));
                draw_string(date_x, date_y, date_str, s_screensaver.text_color);
            }
        }

        strcpy(s_screensaver.last_date_str, date_str);
    }
}

void screensaver_clear(void)
{
    if (!s_screensaver.initialized) {
        return;
    }
    
    ESP_LOGI(TAG, "清除屏保界面");
    clear_screen();
    
    // 清除缓存的时间字符串
    memset(s_screensaver.last_time_str, 0, sizeof(s_screensaver.last_time_str));
    memset(s_screensaver.last_date_str, 0, sizeof(s_screensaver.last_date_str));
}

bool screensaver_is_initialized(void)
{
    return s_screensaver.initialized;
}
