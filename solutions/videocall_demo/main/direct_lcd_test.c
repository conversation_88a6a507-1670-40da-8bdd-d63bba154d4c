/*
 * 直接LCD硬件测试
 * 绕过av_render，直接调用ESP32 LCD面板API
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "codec_init.h"
#include "esp_lcd_panel_ops.h"

static const char *TAG = "DIRECT_LCD_TEST";

// RGB565颜色定义
#define RGB565_RED      0xF800
#define RGB565_GREEN    0x07E0
#define RGB565_BLUE     0x001F
#define RGB565_WHITE    0xFFFF
#define RGB565_BLACK    0x0000

// 直接LCD硬件测试
void direct_lcd_test(void)
{
    ESP_LOGI(TAG, "=== 直接LCD硬件测试开始 ===");

    // 获取LCD面板句柄
    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    if (lcd_handle == NULL) {
        ESP_LOGE(TAG, "Failed to get LCD handle");
        return;
    }

    ESP_LOGI(TAG, "LCD句柄获取成功: %p", lcd_handle);

    // 分配小的测试缓冲区（先测试小区域）
    const int test_width = 100;
    const int test_height = 100;
    const int buffer_size = test_width * test_height * 2; // RGB565 = 2 bytes per pixel
    
    uint16_t *test_buffer = (uint16_t *)malloc(buffer_size);
    if (test_buffer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate test buffer");
        return;
    }

    ESP_LOGI(TAG, "测试缓冲区分配成功: %d bytes", buffer_size);

    // 测试不同颜色
    uint16_t test_colors[] = {RGB565_RED, RGB565_GREEN, RGB565_BLUE, RGB565_WHITE, RGB565_BLACK};
    const char* color_names[] = {"红色", "绿色", "蓝色", "白色", "黑色"};

    for (int i = 0; i < 5; i++) {
        ESP_LOGI(TAG, "测试颜色 %d: %s", i, color_names[i]);

        // 填充测试颜色
        for (int j = 0; j < test_width * test_height; j++) {
            test_buffer[j] = test_colors[i];
        }

        // 直接调用LCD面板API绘制小方块
        esp_err_t ret = esp_lcd_panel_draw_bitmap(lcd_handle, 0, 0, test_width, test_height, test_buffer);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "LCD绘制成功");
        } else {
            ESP_LOGE(TAG, "LCD绘制失败: %s (0x%x)", esp_err_to_name(ret), ret);
        }

        // 等待2秒
        vTaskDelay(pdMS_TO_TICKS(2000));
    }

    // 测试全屏绘制
    ESP_LOGI(TAG, "开始全屏测试...");
    
    // 重新分配全屏缓冲区
    free(test_buffer);
    const int full_width = 1024;
    const int full_height = 600;
    const int full_buffer_size = full_width * full_height * 2;
    
    test_buffer = (uint16_t *)malloc(full_buffer_size);
    if (test_buffer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate full screen buffer");
        return;
    }

    ESP_LOGI(TAG, "全屏缓冲区分配成功: %d bytes", full_buffer_size);

    // 全屏红色测试
    for (int j = 0; j < full_width * full_height; j++) {
        test_buffer[j] = RGB565_RED;
    }

    ESP_LOGI(TAG, "开始全屏红色绘制...");
    esp_err_t ret = esp_lcd_panel_draw_bitmap(lcd_handle, 0, 0, full_width, full_height, test_buffer);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "全屏LCD绘制成功");
    } else {
        ESP_LOGE(TAG, "全屏LCD绘制失败: %s (0x%x)", esp_err_to_name(ret), ret);
    }

    // 等待5秒
    vTaskDelay(pdMS_TO_TICKS(5000));

    // 清理资源
    free(test_buffer);
    ESP_LOGI(TAG, "=== 直接LCD硬件测试完成 ===");
}

// 测试LCD面板基本功能
void test_lcd_panel_basic(void)
{
    ESP_LOGI(TAG, "=== LCD面板基本功能测试 ===");

    esp_lcd_panel_handle_t lcd_handle = (esp_lcd_panel_handle_t)board_get_lcd_handle();
    if (lcd_handle == NULL) {
        ESP_LOGE(TAG, "Failed to get LCD handle");
        return;
    }

    // 测试面板开关
    ESP_LOGI(TAG, "测试LCD面板开关...");
    
    esp_err_t ret = esp_lcd_panel_disp_on_off(lcd_handle, false);
    ESP_LOGI(TAG, "LCD关闭: %s", esp_err_to_name(ret));
    vTaskDelay(pdMS_TO_TICKS(2000));

    ret = esp_lcd_panel_disp_on_off(lcd_handle, true);
    ESP_LOGI(TAG, "LCD开启: %s", esp_err_to_name(ret));
    vTaskDelay(pdMS_TO_TICKS(2000));

    // 测试颜色反转
    ESP_LOGI(TAG, "测试颜色反转...");
    ret = esp_lcd_panel_invert_color(lcd_handle, true);
    ESP_LOGI(TAG, "颜色反转开启: %s", esp_err_to_name(ret));
    vTaskDelay(pdMS_TO_TICKS(3000));

    ret = esp_lcd_panel_invert_color(lcd_handle, false);
    ESP_LOGI(TAG, "颜色反转关闭: %s", esp_err_to_name(ret));

    ESP_LOGI(TAG, "=== LCD面板基本功能测试完成 ===");
}

// 直接LCD测试任务
static void direct_lcd_test_task(void *param)
{
    // 先测试基本功能
    test_lcd_panel_basic();
    vTaskDelay(pdMS_TO_TICKS(1000));

    // 再测试绘制功能
    direct_lcd_test();

    vTaskDelete(NULL);
}

// 启动直接LCD测试
void start_direct_lcd_test(void)
{
    xTaskCreate(direct_lcd_test_task, "direct_lcd_test", 8192, NULL, 5, NULL);
}
