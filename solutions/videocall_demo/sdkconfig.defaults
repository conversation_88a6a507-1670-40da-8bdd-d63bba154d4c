
# Enable SPIRAM
CONFIG_SPIRAM=y

# Enable GDBStub
CONFIG_ESP_SYSTEM_PANIC_GDBSTUB=y

# Enable FreeRTOS trace
CONFIG_FREERTOS_USE_TRACE_FACILITY=y
CONFIG_FREERTOS_VTASKLIST_INCLUDE_COREID=y
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y

# Support 2 SNTP
CONFIG_LWIP_SNTP_MAX_SERVERS=2

# Enable DTLS SRTP
CONFIG_MBEDTLS_SSL_PROTO_DTLS=y
CONFIG_MBEDTLS_SSL_DTLS_SRTP=y

# Use new I2C master
CONFIG_CODEC_I2C_BACKWARD_COMPATIBLE=n

# Enable experimental features
CONFIG_IDF_EXPERIMENTAL_FEATURES=y

# Use customer partition table
CONFIG_PARTITION_TABLE_CUSTOM=y

# Use lare receive buffer
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=64
CONFIG_LWIP_MAX_UDP_PCBS=1024
CONFIG_LWIP_UDP_RECVMBOX_SIZE=64

# Allocate LWIP and MbedTLS on SPIRAM
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=256
CONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP=y
CONFIG_MBEDTLS_EXTERNAL_MEM_ALLOC=y
