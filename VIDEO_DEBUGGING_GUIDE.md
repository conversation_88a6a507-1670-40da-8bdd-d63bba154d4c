# 🎥 Video Capture Debugging Guide

## 🎯 Purpose

This guide helps diagnose whether the black screen issue is caused by:
1. **Video capture problems** (camera not working)
2. **Video encoding issues** (H.264 encoder problems)
3. **WebRTC transmission problems** (network/protocol issues)
4. **Browser rendering problems** (decoding/display issues)

## 🔧 New Debugging Features Added

### 1. Video Statistics Monitoring
- **Frame count tracking**
- **Frame rate calculation**
- **Resolution monitoring**
- **Data throughput measurement**

### 2. Video Frame Capture
- **Save frames as JPEG files** for visual inspection
- **Automatic frame sampling** during video tests
- **Debug directory creation** (`video_debug/`)

### 3. Video Track Validation
- **Check if video tracks are active**
- **Verify track properties**
- **Test video capture duration**

### 4. Interactive Debug Commands
- `debug video` - Enable frame saving
- `debug stats` - Show current statistics
- `debug test [seconds]` - Test capture for N seconds
- `debug validate` - Validate video tracks

## 🚀 Step-by-Step Debugging Process

### Step 1: Basic Camera Access Test
```bash
# Test if camera is accessible
./door-station-esp32 --devices
```

**Expected Output:**
```
✅ 视频设备访问正常
📹 找到 1 个视频轨道
  - Video Track 0: [track-id]
```

**If Failed:**
- Check camera permissions (macOS: System Preferences > Security & Privacy > Camera)
- Ensure no other apps are using the camera
- Try different camera devices

### Step 2: Connect and Auto-Validate
```bash
# Start with auto-validation
./door-station-esp32 https://webrtc.espressif.com video_debug_test
```

**In the console:**
```
door-station> connect video_debug_test
```

**Expected Logs:**
```
[DEBUG] Video constraints: 640x480@15fps
[INFO] Media stream created with 1 video tracks and 1 audio tracks
[DEBUG] Video Track 0: ID=xxx, Kind=video
[DEBUG] Auto-validating video tracks...
[DEBUG] Starting video capture test for 10s
[VIDEO_STATS] Frames: 300, FPS: 30.00, Resolution: 640x480, Total Bytes: 307200
```

### Step 3: Manual Video Testing
```bash
# In the door-station console:
door-station> debug test 15
door-station> debug stats
door-station> debug video
```

**What to Look For:**
1. **Frame Count > 0**: Camera is capturing frames
2. **FPS > 0**: Frame rate is reasonable (15-30 fps)
3. **Total Bytes > 0**: Data is being generated
4. **JPEG files created**: Visual confirmation in `video_debug/` folder

### Step 4: Visual Frame Inspection
```bash
# Check if frames are being saved
ls -la video_debug/
open video_debug/frame_000030.jpg  # View a sample frame
```

**Analysis:**
- **Files exist + show camera feed**: Camera capture is working ✅
- **Files exist but black/corrupted**: Camera/encoding issue ❌
- **No files created**: Video track not producing data ❌

### Step 5: WebRTC Connection Test
```bash
# After connecting and starting call
door-station> debug stats
```

**During WebRTC Connection:**
```
[INFO] ICE connection state changed: connected
[INFO] 📡 Data channel opened - connection is working!
[VIDEO_STATS] Frames: 450, FPS: 30.00, Resolution: 640x480, Total Bytes: 460800
```

## 🔍 Diagnostic Scenarios

### Scenario A: Camera Not Working
**Symptoms:**
- `debug test` shows 0 frames
- No JPEG files created
- Video track validation fails

**Solutions:**
1. Check camera permissions
2. Try different camera device ID
3. Lower resolution/fps settings
4. Check if camera is in use by other apps

### Scenario B: Camera Works, WebRTC Fails
**Symptoms:**
- `debug test` shows frames being captured
- JPEG files show proper camera feed
- Browser still shows black screen
- WebRTC connection established

**Solutions:**
1. Check H.264 encoding compatibility
2. Try different video codecs (VP8/VP9)
3. Lower bitrate/resolution
4. Check browser console for errors

### Scenario C: Encoding Issues
**Symptoms:**
- Camera captures frames
- WebRTC connects
- Browser receives data but shows black/corrupted video

**Solutions:**
1. Check H.264 profile compatibility
2. Try different packetization modes
3. Verify SDP offer/answer codec negotiation
4. Test with different browsers

### Scenario D: Network/Protocol Issues
**Symptoms:**
- Camera works
- Encoding works
- ICE connection fails or unstable

**Solutions:**
1. Check firewall settings
2. Test with different STUN/TURN servers
3. Verify network connectivity
4. Check for NAT traversal issues

## 🎯 Success Criteria

### Video Capture Working:
- ✅ Frame count increases over time
- ✅ FPS is reasonable (>10)
- ✅ JPEG files show clear camera feed
- ✅ Video track validation passes

### WebRTC Transmission Working:
- ✅ ICE connection state: connected
- ✅ Data channel opens
- ✅ No signaling errors
- ✅ Browser receives video data

### End-to-End Success:
- ✅ All above criteria met
- ✅ Browser displays live video feed
- ✅ No black screen
- ✅ Video is smooth and clear

## 🛠️ Advanced Debugging

### Enable Detailed Logging
```bash
# Add more verbose logging
export PION_LOG_TRACE=all
./door-station-esp32 https://webrtc.espressif.com debug_test
```

### Test Different Configurations
```bash
# Test with lower resolution
./door-station-esp32 --width=320 --height=240 --fps=15

# Test with different bitrate
./door-station-esp32 --bitrate=250000

# Test with specific camera
./door-station-esp32 --camera=0
```

### Browser-Side Debugging
```javascript
// In browser console
pc.getStats().then(stats => {
    stats.forEach(report => {
        if (report.type === 'inbound-rtp' && report.kind === 'video') {
            console.log('Video RTP stats:', report);
        }
    });
});
```

## 📊 Expected Results

After running through this debugging process, you should be able to determine:

1. **Is the camera capturing video?** (Step 1-4)
2. **Is the video being encoded properly?** (Step 4)
3. **Is WebRTC transmitting the data?** (Step 5)
4. **Is the browser receiving and decoding?** (Browser tools)

This systematic approach will pinpoint exactly where the video pipeline is failing and guide you to the appropriate solution! 🎉
