/*
 * SPDX-FileCopyrightText: 2021-2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include "sdkconfig.h"

#include "esp_painter_font.h"

#if CONFIG_ESP_PAINTER_BASIC_FONT_36

static const uint8_t bitmap[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*" ",0*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0,
    0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00,
    0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"!",1*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7, 0x00, 0x01, 0xC7, 0x00, 0x03, 0xCF, 0x00, 0x03,
    0x8E, 0x00, 0x07, 0x1C, 0x00, 0x06, 0x18, 0x00, 0x0C, 0x30, 0x00, 0x18, 0x60, 0x00, 0x10, 0x40,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*""",2*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x04, 0x04, 0x00, 0x04, 0x04, 0x00, 0x04, 0x04, 0x00, 0x04, 0x04, 0x00, 0x04, 0x04,
    0x00, 0x04, 0x04, 0x00, 0x7F, 0xFF, 0x80, 0x7F, 0xFF, 0x80, 0x7F, 0xFF, 0x80, 0x0C, 0x0C, 0x00,
    0x08, 0x08, 0x00, 0x08, 0x08, 0x00, 0x08, 0x08, 0x00, 0x08, 0x08, 0x00, 0x08, 0x08, 0x00, 0x08,
    0x08, 0x00, 0x7F, 0xFF, 0x80, 0x7F, 0xFF, 0x80, 0x7F, 0xFF, 0x80, 0x18, 0x18, 0x00, 0x18, 0x18,
    0x00, 0x18, 0x18, 0x00, 0x10, 0x10, 0x00, 0x10, 0x10, 0x00, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"#",3*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00,
    0x80, 0x00, 0x03, 0xF0, 0x00, 0x06, 0x88, 0x00, 0x0C, 0x84, 0x00, 0x18, 0x86, 0x00, 0x18, 0x86,
    0x00, 0x18, 0x8E, 0x00, 0x18, 0x8E, 0x00, 0x1C, 0x80, 0x00, 0x0E, 0x80, 0x00, 0x0F, 0x80, 0x00,
    0x07, 0x80, 0x00, 0x03, 0xC0, 0x00, 0x01, 0xF0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xBC, 0x00, 0x00,
    0x9C, 0x00, 0x00, 0x8E, 0x00, 0x00, 0x86, 0x00, 0x38, 0x86, 0x00, 0x38, 0x86, 0x00, 0x38, 0x86,
    0x00, 0x30, 0x84, 0x00, 0x10, 0x8C, 0x00, 0x08, 0x98, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x80, 0x00,
    0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, /*"$",4*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x3C, 0x04, 0x00, 0x66, 0x0C, 0x00, 0x42, 0x08, 0x00, 0xC3, 0x08, 0x00, 0xC3, 0x10,
    0x00, 0xC3, 0x10, 0x00, 0xC3, 0x20, 0x00, 0xC3, 0x20, 0x00, 0xC3, 0x20, 0x00, 0xC3, 0x40, 0x00,
    0x42, 0x40, 0x00, 0x66, 0x80, 0x00, 0x3C, 0x9E, 0x00, 0x00, 0xB3, 0x00, 0x01, 0x31, 0x00, 0x01,
    0x61, 0x80, 0x02, 0x61, 0x80, 0x02, 0x61, 0x80, 0x06, 0x61, 0x80, 0x04, 0x61, 0x80, 0x04, 0x61,
    0x80, 0x08, 0x61, 0x80, 0x08, 0x21, 0x00, 0x10, 0x33, 0x00, 0x10, 0x1E, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"%",5*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x07, 0x80, 0x00, 0x0C, 0x80, 0x00, 0x08, 0xC0, 0x00, 0x18, 0xC0, 0x00, 0x18, 0xC0,
    0x00, 0x18, 0xC0, 0x00, 0x18, 0xC0, 0x00, 0x18, 0x80, 0x00, 0x19, 0x80, 0x00, 0x1D, 0x00, 0x00,
    0x0E, 0x00, 0x00, 0x0C, 0x3E, 0x00, 0x1E, 0x18, 0x00, 0x26, 0x08, 0x00, 0x67, 0x08, 0x00, 0x43,
    0x08, 0x00, 0xC3, 0x10, 0x00, 0xC3, 0x90, 0x00, 0xC1, 0xD0, 0x00, 0xC0, 0xD0, 0x00, 0xC0, 0xE0,
    0x00, 0x60, 0x70, 0x80, 0x60, 0x79, 0x80, 0x30, 0x9F, 0x00, 0x1F, 0x0E, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"&",6*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x0E,
    0x00, 0x00, 0x06, 0x00, 0x00, 0x04, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x18, 0x00, 0x00, 0x30, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"'",7*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x01, 0x00, 0x00, 0x02, 0x00, 0x00,
    0x04, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x30, 0x00, 0x00, 0x30,
    0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00,
    0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0xC0, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x20, 0x00, 0x00, 0x30,
    0x00, 0x00, 0x10, 0x00, 0x00, 0x18, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x04, 0x00, 0x00, 0x02, 0x00,
    0x00, 0x01, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"(",8*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x20, 0x00, 0x00, 0x10, 0x00, 0x00, 0x08,
    0x00, 0x00, 0x0C, 0x00, 0x00, 0x06, 0x00, 0x00, 0x02, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00,
    0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00,
    0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0xC0, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x00, 0x00, 0x03, 0x00,
    0x00, 0x02, 0x00, 0x00, 0x06, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x08, 0x00, 0x00, 0x10, 0x00, 0x00,
    0x20, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*")",9*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x01, 0xC0,
    0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x70, 0xC7, 0x00, 0x78, 0x8F, 0x00, 0x3C, 0x9E, 0x00,
    0x0E, 0xB8, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x0E, 0xB8, 0x00, 0x3C, 0x9E, 0x00, 0x78,
    0x8F, 0x00, 0x71, 0x87, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"*",10*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40,
    0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00,
    0x00, 0x40, 0x00, 0x3F, 0xFF, 0x80, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00,
    0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"+",11*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x00,
    0x00, 0x1E, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x06, 0x00, 0x00, 0x04, 0x00, 0x00,
    0x0C, 0x00, 0x00, 0x18, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, /*",",12*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x7F, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"-",13*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x18, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*".",14*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x01, 0x80, 0x00,
    0x01, 0x00, 0x00, 0x03, 0x00, 0x00, 0x02, 0x00, 0x00, 0x06, 0x00, 0x00, 0x04, 0x00, 0x00, 0x0C,
    0x00, 0x00, 0x08, 0x00, 0x00, 0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x30, 0x00, 0x00, 0x20, 0x00,
    0x00, 0x60, 0x00, 0x00, 0x40, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01,
    0x00, 0x00, 0x03, 0x00, 0x00, 0x02, 0x00, 0x00, 0x06, 0x00, 0x00, 0x04, 0x00, 0x00, 0x0C, 0x00,
    0x00, 0x08, 0x00, 0x00, 0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x30, 0x00, 0x00, 0x20, 0x00, 0x00,
    0x60, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"/",15*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x01, 0xE0, 0x00, 0x06, 0x18, 0x00, 0x04, 0x0C, 0x00, 0x0C, 0x0C, 0x00, 0x18, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00,
    0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30,
    0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06,
    0x00, 0x0C, 0x0C, 0x00, 0x04, 0x08, 0x00, 0x06, 0x18, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"0",16*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
    0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00,
    0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
    0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"1",17*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0xF0, 0x00, 0x0C, 0x1C, 0x00, 0x18, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x03,
    0x00, 0x38, 0x03, 0x00, 0x38, 0x03, 0x00, 0x38, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x06, 0x00,
    0x00, 0x06, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x18, 0x00, 0x00, 0x30, 0x00, 0x00, 0x60, 0x00, 0x00,
    0xC0, 0x00, 0x01, 0x80, 0x00, 0x03, 0x00, 0x00, 0x02, 0x00, 0x00, 0x04, 0x01, 0x00, 0x08, 0x01,
    0x00, 0x10, 0x03, 0x00, 0x30, 0x07, 0x00, 0x3F, 0xFE, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"2",18*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x07, 0xE0, 0x00, 0x08, 0x38, 0x00, 0x10, 0x0C, 0x00, 0x20, 0x0E, 0x00, 0x30, 0x06,
    0x00, 0x30, 0x06, 0x00, 0x30, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00,
    0x00, 0x30, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x18, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x06, 0x00, 0x00,
    0x02, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x18, 0x03, 0x00, 0x38, 0x03, 0x00, 0x38, 0x03,
    0x00, 0x38, 0x06, 0x00, 0x18, 0x04, 0x00, 0x0C, 0x18, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"3",19*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x38, 0x00, 0x00, 0x38, 0x00, 0x00, 0x58,
    0x00, 0x00, 0xD8, 0x00, 0x00, 0x98, 0x00, 0x01, 0x18, 0x00, 0x03, 0x18, 0x00, 0x02, 0x18, 0x00,
    0x04, 0x18, 0x00, 0x0C, 0x18, 0x00, 0x08, 0x18, 0x00, 0x10, 0x18, 0x00, 0x10, 0x18, 0x00, 0x20,
    0x18, 0x00, 0x60, 0x18, 0x00, 0x7F, 0xFF, 0x80, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18,
    0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"4",20*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x0F, 0xFF, 0x00, 0x0F, 0xFF, 0x00, 0x08, 0x00, 0x00, 0x08, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x11, 0xF0, 0x00,
    0x16, 0x0C, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00,
    0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x18, 0x03, 0x00, 0x38, 0x03, 0x00, 0x38, 0x06,
    0x00, 0x30, 0x06, 0x00, 0x10, 0x0C, 0x00, 0x0C, 0x18, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"5",21*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0xF8, 0x00, 0x03, 0x04, 0x00, 0x06, 0x06, 0x00, 0x0C, 0x06, 0x00, 0x08, 0x06,
    0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x30, 0x00, 0x00, 0x31, 0xF0, 0x00,
    0x36, 0x1C, 0x00, 0x34, 0x06, 0x00, 0x38, 0x06, 0x00, 0x38, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30,
    0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x02,
    0x00, 0x0C, 0x06, 0x00, 0x0C, 0x04, 0x00, 0x06, 0x08, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"6",22*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x1F, 0xFF, 0x00, 0x1F, 0xFF, 0x00, 0x18, 0x02, 0x00, 0x30, 0x04, 0x00, 0x20, 0x04,
    0x00, 0x20, 0x08, 0x00, 0x00, 0x08, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x20, 0x00,
    0x00, 0x20, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x80, 0x00, 0x01,
    0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x03, 0x80, 0x00, 0x03, 0x80,
    0x00, 0x03, 0x80, 0x00, 0x03, 0x80, 0x00, 0x03, 0x80, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"7",23*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x01, 0xF0, 0x00, 0x06, 0x0C, 0x00, 0x0C, 0x06, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03,
    0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x1C, 0x03, 0x00, 0x0E, 0x06, 0x00, 0x0F, 0x06, 0x00,
    0x07, 0x88, 0x00, 0x03, 0xF0, 0x00, 0x06, 0xF8, 0x00, 0x0C, 0x3C, 0x00, 0x18, 0x1E, 0x00, 0x18,
    0x0E, 0x00, 0x30, 0x07, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03,
    0x00, 0x18, 0x02, 0x00, 0x18, 0x06, 0x00, 0x0E, 0x0C, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"8",24*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0xE0, 0x00, 0x0E, 0x18, 0x00, 0x08, 0x0C, 0x00, 0x18, 0x04, 0x00, 0x10, 0x06,
    0x00, 0x30, 0x02, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00,
    0x30, 0x07, 0x00, 0x18, 0x07, 0x00, 0x18, 0x0B, 0x00, 0x0E, 0x13, 0x00, 0x03, 0xE3, 0x00, 0x00,
    0x03, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x18, 0x0C,
    0x00, 0x18, 0x08, 0x00, 0x18, 0x18, 0x00, 0x08, 0x70, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"9",25*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x01, 0xE0, 0x00,
    0x01, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*":",26*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC0, 0x00,
    0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x00, 0xC0, 0x00,
    0x00, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, /*";",27*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x04, 0x00, 0x00, 0x08, 0x00, 0x00, 0x10, 0x00, 0x00, 0x20,
    0x00, 0x00, 0x40, 0x00, 0x00, 0xC0, 0x00, 0x01, 0x80, 0x00, 0x03, 0x00, 0x00, 0x06, 0x00, 0x00,
    0x0C, 0x00, 0x00, 0x18, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x06,
    0x00, 0x00, 0x03, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x40, 0x00, 0x00, 0x20,
    0x00, 0x00, 0x10, 0x00, 0x00, 0x08, 0x00, 0x00, 0x04, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"<",28*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F,
    0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"=",29*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x10, 0x00, 0x00, 0x08, 0x00, 0x00, 0x04, 0x00, 0x00, 0x02, 0x00, 0x00, 0x01, 0x00,
    0x00, 0x00, 0x80, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x60, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x0C, 0x00, 0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x06, 0x00, 0x00, 0x0C, 0x00, 0x00,
    0x18, 0x00, 0x00, 0x30, 0x00, 0x00, 0x60, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x80, 0x00, 0x01, 0x00,
    0x00, 0x02, 0x00, 0x00, 0x04, 0x00, 0x00, 0x08, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*">",30*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0xF0, 0x00, 0x06, 0x0C, 0x00, 0x08, 0x06, 0x00, 0x10, 0x02, 0x00, 0x30, 0x03,
    0x00, 0x30, 0x03, 0x00, 0x3C, 0x03, 0x00, 0x3C, 0x03, 0x00, 0x1C, 0x03, 0x00, 0x00, 0x07, 0x00,
    0x00, 0x0E, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x78, 0x00, 0x00, 0x60, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"?",31*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x01, 0xF0, 0x00, 0x07, 0x0C, 0x00, 0x0C, 0x02, 0x00, 0x08, 0x01, 0x00, 0x18, 0x7D,
    0x00, 0x30, 0xCD, 0x00, 0x31, 0x8C, 0x80, 0x21, 0x8C, 0x80, 0x63, 0x08, 0x80, 0x63, 0x08, 0x80,
    0x63, 0x18, 0x80, 0x66, 0x18, 0x80, 0x66, 0x18, 0x80, 0x66, 0x18, 0x80, 0x66, 0x18, 0x80, 0x66,
    0x31, 0x00, 0x66, 0x31, 0x00, 0x22, 0x52, 0x00, 0x33, 0x9C, 0x00, 0x30, 0x00, 0x80, 0x10, 0x01,
    0x00, 0x18, 0x03, 0x00, 0x0C, 0x06, 0x00, 0x06, 0x0C, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"@",32*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x03, 0x60,
    0x00, 0x02, 0x60, 0x00, 0x02, 0x60, 0x00, 0x02, 0x60, 0x00, 0x06, 0x30, 0x00, 0x04, 0x30, 0x00,
    0x04, 0x30, 0x00, 0x04, 0x30, 0x00, 0x0C, 0x38, 0x00, 0x08, 0x18, 0x00, 0x08, 0x18, 0x00, 0x0F,
    0xF8, 0x00, 0x18, 0x18, 0x00, 0x10, 0x1C, 0x00, 0x10, 0x0C, 0x00, 0x10, 0x0C, 0x00, 0x30, 0x0C,
    0x00, 0x20, 0x0E, 0x00, 0x20, 0x06, 0x00, 0x30, 0x06, 0x00, 0xF8, 0x1F, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"A",33*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7F, 0xF8, 0x00, 0x18, 0x1E, 0x00, 0x18, 0x06, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03,
    0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00,
    0x18, 0x18, 0x00, 0x1F, 0xF0, 0x00, 0x18, 0x0C, 0x00, 0x18, 0x06, 0x00, 0x18, 0x03, 0x00, 0x18,
    0x01, 0x00, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01,
    0x80, 0x18, 0x03, 0x00, 0x18, 0x07, 0x00, 0x18, 0x0E, 0x00, 0x7F, 0xF8, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"B",34*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x01, 0xF9, 0x00, 0x02, 0x0F, 0x00, 0x0C, 0x03, 0x00, 0x08, 0x01, 0x00, 0x18, 0x01,
    0x80, 0x30, 0x00, 0x80, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00,
    0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60,
    0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x30, 0x00, 0x80, 0x30, 0x00, 0x80, 0x30, 0x01,
    0x00, 0x18, 0x01, 0x00, 0x0C, 0x02, 0x00, 0x06, 0x0C, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"C",35*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7F, 0xE0, 0x00, 0x18, 0x38, 0x00, 0x18, 0x0C, 0x00, 0x18, 0x06, 0x00, 0x18, 0x02,
    0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80,
    0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18,
    0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x18, 0x0C, 0x00, 0x18, 0x38, 0x00, 0x7F, 0xE0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"D",36*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7F, 0xFF, 0x00, 0x18, 0x07, 0x00, 0x18, 0x03, 0x00, 0x18, 0x01, 0x80, 0x18, 0x00,
    0x80, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x08, 0x00, 0x18, 0x08, 0x00, 0x18, 0x08, 0x00,
    0x18, 0x18, 0x00, 0x1F, 0xF8, 0x00, 0x18, 0x18, 0x00, 0x18, 0x08, 0x00, 0x18, 0x08, 0x00, 0x18,
    0x08, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x80, 0x18, 0x00,
    0x80, 0x18, 0x01, 0x00, 0x18, 0x03, 0x00, 0x18, 0x07, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"E",37*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7F, 0xFE, 0x00, 0x18, 0x0F, 0x00, 0x18, 0x03, 0x00, 0x18, 0x01, 0x00, 0x18, 0x00,
    0x80, 0x18, 0x00, 0x80, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x08, 0x00, 0x18, 0x08, 0x00,
    0x18, 0x18, 0x00, 0x1F, 0xF8, 0x00, 0x18, 0x18, 0x00, 0x18, 0x08, 0x00, 0x18, 0x08, 0x00, 0x18,
    0x08, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"F",38*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x01, 0xE0, 0x00, 0x06, 0x1C, 0x00, 0x0C, 0x0C, 0x00, 0x18, 0x04, 0x00, 0x10, 0x06,
    0x00, 0x30, 0x02, 0x00, 0x30, 0x02, 0x00, 0x20, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00,
    0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x3F, 0x80, 0x60,
    0x06, 0x00, 0x60, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06, 0x00, 0x10, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x0C, 0x06, 0x00, 0x06, 0x08, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"G",39*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7E, 0x1F, 0x80, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00,
    0x18, 0x06, 0x00, 0x1F, 0xFE, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18,
    0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x7E, 0x1F, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"H",40*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
    0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00,
    0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
    0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"I",41*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0xFF, 0xC0, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18,
    0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00,
    0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18,
    0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x70, 0x18, 0x00,
    0x70, 0x30, 0x00, 0x70, 0x30, 0x00, 0x30, 0x40, 0x00, 0x1F, 0x80, 0x00, /*"J",42*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7E, 0x3F, 0x00, 0x18, 0x0C, 0x00, 0x18, 0x08, 0x00, 0x18, 0x10, 0x00, 0x18, 0x30,
    0x00, 0x18, 0x20, 0x00, 0x18, 0x40, 0x00, 0x18, 0xC0, 0x00, 0x18, 0x80, 0x00, 0x19, 0x80, 0x00,
    0x19, 0x80, 0x00, 0x1B, 0xC0, 0x00, 0x1C, 0xC0, 0x00, 0x1C, 0xE0, 0x00, 0x18, 0x60, 0x00, 0x18,
    0x60, 0x00, 0x18, 0x30, 0x00, 0x18, 0x30, 0x00, 0x18, 0x18, 0x00, 0x18, 0x18, 0x00, 0x18, 0x1C,
    0x00, 0x18, 0x0C, 0x00, 0x18, 0x0E, 0x00, 0x18, 0x06, 0x00, 0x7E, 0x1F, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"K",43*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7E, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00,
    0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18,
    0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x80, 0x18, 0x00,
    0x80, 0x18, 0x01, 0x00, 0x18, 0x03, 0x00, 0x18, 0x07, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"L",44*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xF8, 0x07, 0xC0, 0x38, 0x07, 0x00, 0x38, 0x07, 0x00, 0x38, 0x07, 0x00, 0x38, 0x07,
    0x00, 0x2C, 0x0B, 0x00, 0x2C, 0x0B, 0x00, 0x2C, 0x0B, 0x00, 0x2C, 0x0B, 0x00, 0x2E, 0x13, 0x00,
    0x26, 0x13, 0x00, 0x26, 0x13, 0x00, 0x26, 0x13, 0x00, 0x26, 0x23, 0x00, 0x23, 0x23, 0x00, 0x23,
    0x23, 0x00, 0x23, 0x23, 0x00, 0x23, 0x43, 0x00, 0x23, 0xC3, 0x00, 0x21, 0xC3, 0x00, 0x21, 0xC3,
    0x00, 0x21, 0xC3, 0x00, 0x21, 0x83, 0x00, 0x20, 0x83, 0x00, 0xF8, 0x8F, 0xC0, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"M",45*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x78, 0x0F, 0x80, 0x1C, 0x02, 0x00, 0x1C, 0x02, 0x00, 0x16, 0x02, 0x00, 0x16, 0x02,
    0x00, 0x16, 0x02, 0x00, 0x13, 0x02, 0x00, 0x13, 0x02, 0x00, 0x11, 0x82, 0x00, 0x11, 0x82, 0x00,
    0x10, 0xC2, 0x00, 0x10, 0xC2, 0x00, 0x10, 0xE2, 0x00, 0x10, 0x62, 0x00, 0x10, 0x62, 0x00, 0x10,
    0x32, 0x00, 0x10, 0x32, 0x00, 0x10, 0x1A, 0x00, 0x10, 0x1A, 0x00, 0x10, 0x0E, 0x00, 0x10, 0x0E,
    0x00, 0x10, 0x0E, 0x00, 0x10, 0x06, 0x00, 0x10, 0x06, 0x00, 0x7C, 0x02, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"N",46*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0xF0, 0x00, 0x06, 0x18, 0x00, 0x0C, 0x04, 0x00, 0x18, 0x06, 0x00, 0x10, 0x02,
    0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x20, 0x01, 0x00, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80,
    0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x60,
    0x01, 0x80, 0x60, 0x01, 0x80, 0x20, 0x01, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x10, 0x02,
    0x00, 0x18, 0x06, 0x00, 0x0C, 0x04, 0x00, 0x06, 0x18, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"O",47*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7F, 0xF8, 0x00, 0x18, 0x0E, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x01,
    0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x03, 0x00,
    0x18, 0x07, 0x00, 0x18, 0x0E, 0x00, 0x1F, 0xF8, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18,
    0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"P",48*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0xF0, 0x00, 0x06, 0x18, 0x00, 0x0C, 0x04, 0x00, 0x18, 0x06, 0x00, 0x10, 0x02,
    0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80,
    0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x60,
    0x01, 0x80, 0x60, 0x01, 0x80, 0x60, 0x01, 0x80, 0x27, 0xC1, 0x00, 0x3C, 0x63, 0x00, 0x38, 0x33,
    0x00, 0x18, 0x32, 0x00, 0x08, 0x3C, 0x00, 0x06, 0x18, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x1C, 0x80,
    0x00, 0x0F, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"Q",49*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7F, 0xF0, 0x00, 0x18, 0x1C, 0x00, 0x18, 0x06, 0x00, 0x18, 0x07, 0x00, 0x18, 0x03,
    0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00,
    0x18, 0x1C, 0x00, 0x1F, 0xF0, 0x00, 0x18, 0x60, 0x00, 0x18, 0x60, 0x00, 0x18, 0x70, 0x00, 0x18,
    0x30, 0x00, 0x18, 0x30, 0x00, 0x18, 0x38, 0x00, 0x18, 0x18, 0x00, 0x18, 0x18, 0x00, 0x18, 0x1C,
    0x00, 0x18, 0x0C, 0x00, 0x18, 0x0E, 0x00, 0x18, 0x06, 0x00, 0x7E, 0x07, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"R",50*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0xE0, 0x00, 0x0C, 0x1E, 0x00, 0x18, 0x0E, 0x00, 0x10, 0x06, 0x00, 0x30, 0x02,
    0x00, 0x30, 0x02, 0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x38, 0x00, 0x00, 0x1E, 0x00, 0x00,
    0x0F, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x0E, 0x00, 0x00,
    0x06, 0x00, 0x00, 0x07, 0x00, 0x00, 0x03, 0x00, 0x20, 0x03, 0x00, 0x20, 0x03, 0x00, 0x30, 0x03,
    0x00, 0x10, 0x06, 0x00, 0x18, 0x06, 0x00, 0x1E, 0x0C, 0x00, 0x11, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"S",51*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x3F, 0xFF, 0x00, 0x30, 0xC3, 0x00, 0x20, 0xC1, 0x00, 0x20, 0xC1, 0x80, 0x40, 0xC0,
    0x80, 0x40, 0xC0, 0x80, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00,
    0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
    0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"T",52*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7E, 0x0F, 0x80, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02,
    0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00,
    0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18,
    0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x18, 0x02,
    0x00, 0x18, 0x04, 0x00, 0x0C, 0x04, 0x00, 0x06, 0x18, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"U",53*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7E, 0x0F, 0x80, 0x18, 0x02, 0x00, 0x18, 0x02, 0x00, 0x1C, 0x06, 0x00, 0x0C, 0x04,
    0x00, 0x0C, 0x04, 0x00, 0x0C, 0x04, 0x00, 0x0E, 0x0C, 0x00, 0x0E, 0x08, 0x00, 0x06, 0x08, 0x00,
    0x06, 0x08, 0x00, 0x06, 0x18, 0x00, 0x07, 0x10, 0x00, 0x03, 0x10, 0x00, 0x03, 0x10, 0x00, 0x03,
    0x20, 0x00, 0x03, 0xA0, 0x00, 0x01, 0xA0, 0x00, 0x01, 0xA0, 0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0,
    0x00, 0x01, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"V",54*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xF9, 0xF3, 0xC0, 0x70, 0xC1, 0x80, 0x30, 0xC1, 0x00, 0x30, 0xC1, 0x00, 0x30, 0xC1,
    0x00, 0x30, 0x61, 0x00, 0x30, 0xE1, 0x00, 0x30, 0xE2, 0x00, 0x30, 0xE2, 0x00, 0x18, 0xE2, 0x00,
    0x18, 0xE2, 0x00, 0x19, 0x62, 0x00, 0x19, 0x32, 0x00, 0x19, 0x34, 0x00, 0x19, 0x34, 0x00, 0x19,
    0x34, 0x00, 0x0E, 0x34, 0x00, 0x0E, 0x34, 0x00, 0x0E, 0x38, 0x00, 0x0E, 0x18, 0x00, 0x0E, 0x18,
    0x00, 0x0E, 0x18, 0x00, 0x04, 0x18, 0x00, 0x04, 0x18, 0x00, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"W",55*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7E, 0x1F, 0x00, 0x1C, 0x06, 0x00, 0x0C, 0x04, 0x00, 0x0C, 0x08, 0x00, 0x0E, 0x08,
    0x00, 0x06, 0x18, 0x00, 0x07, 0x10, 0x00, 0x03, 0x30, 0x00, 0x03, 0xA0, 0x00, 0x01, 0xE0, 0x00,
    0x01, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01,
    0x70, 0x00, 0x03, 0x30, 0x00, 0x02, 0x30, 0x00, 0x06, 0x18, 0x00, 0x04, 0x18, 0x00, 0x04, 0x1C,
    0x00, 0x08, 0x0C, 0x00, 0x08, 0x0E, 0x00, 0x18, 0x06, 0x00, 0x7C, 0x1F, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"X",56*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x7E, 0x0F, 0x80, 0x38, 0x06, 0x00, 0x18, 0x06, 0x00, 0x1C, 0x04, 0x00, 0x0C, 0x04,
    0x00, 0x0C, 0x08, 0x00, 0x06, 0x08, 0x00, 0x06, 0x08, 0x00, 0x07, 0x10, 0x00, 0x03, 0x10, 0x00,
    0x03, 0x30, 0x00, 0x01, 0xA0, 0x00, 0x01, 0xA0, 0x00, 0x01, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
    0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"Y",57*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x1F, 0xFF, 0x00, 0x1C, 0x06, 0x00, 0x18, 0x0E, 0x00, 0x30, 0x0C, 0x00, 0x20, 0x1C,
    0x00, 0x00, 0x18, 0x00, 0x00, 0x38, 0x00, 0x00, 0x30, 0x00, 0x00, 0x70, 0x00, 0x00, 0x60, 0x00,
    0x00, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x01, 0x80, 0x00, 0x03, 0x80, 0x00, 0x03,
    0x00, 0x00, 0x07, 0x00, 0x00, 0x06, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x0C, 0x01, 0x00, 0x1C, 0x01,
    0x00, 0x18, 0x02, 0x00, 0x38, 0x06, 0x00, 0x30, 0x0E, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"Z",58*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01,
    0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00,
    0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00,
    0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01,
    0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00,
    0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00,
    0x01, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"[",59*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18,
    0x00, 0x00, 0x08, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x04, 0x00, 0x00, 0x06, 0x00,
    0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01, 0x80, 0x00,
    0x01, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x60, 0x00, 0x00,
    0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x10, 0x00, 0x00, 0x18,
    0x00, 0x00, 0x18, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x06, 0x00,
    0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, /*"\",60*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00,
    0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60,
    0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00,
    0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00,
    0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60,
    0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00,
    0x00, 0x60, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"]",61*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x03, 0x60, 0x00, 0x06, 0x10, 0x00, 0x08,
    0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"^",62*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xC0, /*"_",63*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x07, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00,
    0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"`",64*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xF0, 0x00,
    0x0C, 0x18, 0x00, 0x18, 0x0C, 0x00, 0x18, 0x0C, 0x00, 0x18, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x01,
    0xFC, 0x00, 0x07, 0x0C, 0x00, 0x1C, 0x0C, 0x00, 0x18, 0x0C, 0x00, 0x30, 0x0C, 0x00, 0x30, 0x0C,
    0x00, 0x30, 0x0C, 0x80, 0x30, 0x1C, 0x80, 0x18, 0x3C, 0x80, 0x0F, 0xC7, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"a",65*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
    0x00, 0x00, 0x78, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0xF8, 0x00,
    0x1B, 0x0C, 0x00, 0x1E, 0x06, 0x00, 0x1C, 0x06, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18,
    0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03,
    0x00, 0x18, 0x06, 0x00, 0x1C, 0x06, 0x00, 0x1E, 0x0C, 0x00, 0x11, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"b",66*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xF8, 0x00,
    0x07, 0x0C, 0x00, 0x0C, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x30, 0x00, 0x00, 0x30,
    0x00, 0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x10, 0x01,
    0x00, 0x18, 0x02, 0x00, 0x0C, 0x02, 0x00, 0x06, 0x0C, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"c",67*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x02, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06,
    0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x03, 0xE6, 0x00,
    0x06, 0x1E, 0x00, 0x0C, 0x0E, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30,
    0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06,
    0x00, 0x18, 0x0E, 0x00, 0x18, 0x0E, 0x00, 0x0C, 0x17, 0x80, 0x03, 0xE4, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"d",68*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xF0, 0x00,
    0x06, 0x0C, 0x00, 0x0C, 0x06, 0x00, 0x18, 0x06, 0x00, 0x10, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30,
    0x03, 0x00, 0x3F, 0xFF, 0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x10, 0x01,
    0x00, 0x18, 0x01, 0x00, 0x0C, 0x02, 0x00, 0x06, 0x0C, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"e",69*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0xC1, 0x00, 0x01, 0x81, 0x80, 0x01, 0x01, 0x80, 0x03, 0x01,
    0x80, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x3F, 0xFC, 0x00,
    0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03,
    0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00,
    0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x3F, 0xF8, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"f",70*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xE3, 0x80,
    0x06, 0x3D, 0x80, 0x0C, 0x18, 0x00, 0x18, 0x0C, 0x00, 0x18, 0x0C, 0x00, 0x18, 0x0C, 0x00, 0x18,
    0x0C, 0x00, 0x0C, 0x18, 0x00, 0x06, 0x18, 0x00, 0x0F, 0xE0, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x1F, 0xC0, 0x00, 0x07, 0xFC, 0x00, 0x18, 0x1E, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00,
    0x30, 0x03, 0x00, 0x38, 0x02, 0x00, 0x1C, 0x0E, 0x00, 0x07, 0xF8, 0x00, /*"g",71*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
    0x00, 0x00, 0x78, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0xF8, 0x00,
    0x1B, 0x0C, 0x00, 0x1C, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18,
    0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x7E, 0x1F, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"h",72*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x0F, 0xC0, 0x00,
    0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
    0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"i",73*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0xFC, 0x00,
    0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00,
    0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C,
    0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x0C, 0x00,
    0x18, 0x08, 0x00, 0x18, 0x18, 0x00, 0x18, 0x30, 0x00, 0x0F, 0xC0, 0x00, /*"j",74*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
    0x00, 0x00, 0x78, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00,
    0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x3F, 0x00,
    0x18, 0x1C, 0x00, 0x18, 0x10, 0x00, 0x18, 0x30, 0x00, 0x18, 0x60, 0x00, 0x18, 0xC0, 0x00, 0x19,
    0xC0, 0x00, 0x1B, 0xE0, 0x00, 0x1E, 0x60, 0x00, 0x1C, 0x70, 0x00, 0x18, 0x38, 0x00, 0x18, 0x18,
    0x00, 0x18, 0x1C, 0x00, 0x18, 0x0E, 0x00, 0x18, 0x06, 0x00, 0x7E, 0x1F, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"k",75*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x40, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
    0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00,
    0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00,
    0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0,
    0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"l",76*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x77, 0x9E, 0x00,
    0x38, 0xE3, 0x00, 0x38, 0xC3, 0x00, 0x30, 0xC3, 0x00, 0x30, 0xC3, 0x00, 0x30, 0xC3, 0x00, 0x30,
    0xC3, 0x00, 0x30, 0xC3, 0x00, 0x30, 0xC3, 0x00, 0x30, 0xC3, 0x00, 0x30, 0xC3, 0x00, 0x30, 0xC3,
    0x00, 0x30, 0xC3, 0x00, 0x30, 0xC3, 0x00, 0x30, 0xC3, 0x00, 0x79, 0xE7, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"m",77*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xF8, 0x00,
    0x7B, 0x0C, 0x00, 0x1E, 0x06, 0x00, 0x1C, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18,
    0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x7E, 0x1F, 0x80, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"n",78*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xF0, 0x00,
    0x06, 0x18, 0x00, 0x0C, 0x0C, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x30, 0x03, 0x00, 0x30,
    0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x10, 0x02,
    0x00, 0x18, 0x06, 0x00, 0x0C, 0x0C, 0x00, 0x06, 0x18, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"o",79*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0xF0, 0x00,
    0x7B, 0x0C, 0x00, 0x1C, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18,
    0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x03, 0x00, 0x18, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x1C, 0x0C, 0x00, 0x1A, 0x18, 0x00, 0x19, 0xF0, 0x00, 0x18, 0x00, 0x00,
    0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, 0x7E, 0x00, 0x00, /*"p",80*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xE2, 0x00,
    0x06, 0x1E, 0x00, 0x0C, 0x0E, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30,
    0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06, 0x00, 0x30, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x18, 0x0E, 0x00, 0x0C, 0x16, 0x00, 0x03, 0xE6, 0x00, 0x00, 0x06, 0x00,
    0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x1F, 0x80, /*"q",81*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x1E, 0x00,
    0x7E, 0x23, 0x00, 0x06, 0xC3, 0x00, 0x06, 0x83, 0x00, 0x07, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06,
    0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00,
    0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x7F, 0xE0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"r",82*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xF2, 0x00,
    0x06, 0x0E, 0x00, 0x0C, 0x06, 0x00, 0x0C, 0x02, 0x00, 0x0C, 0x02, 0x00, 0x0E, 0x00, 0x00, 0x07,
    0x80, 0x00, 0x03, 0xE0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x1C, 0x00, 0x10, 0x0E, 0x00, 0x10, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x1E, 0x0C, 0x00, 0x11, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"s",83*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00,
    0x00, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x03, 0x00, 0x00, 0x07, 0x00, 0x00, 0x3F, 0xFC, 0x00,
    0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03,
    0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00,
    0x00, 0x03, 0x02, 0x00, 0x03, 0x04, 0x00, 0x01, 0x84, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"t",84*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x02, 0x00, 0x78, 0x1E, 0x00,
    0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18,
    0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06, 0x00, 0x18, 0x06,
    0x00, 0x18, 0x06, 0x00, 0x18, 0x0E, 0x00, 0x0C, 0x17, 0x80, 0x07, 0xE4, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"u",85*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x1F, 0x00,
    0x18, 0x0C, 0x00, 0x18, 0x04, 0x00, 0x0C, 0x08, 0x00, 0x0C, 0x08, 0x00, 0x0C, 0x18, 0x00, 0x06,
    0x10, 0x00, 0x06, 0x10, 0x00, 0x06, 0x20, 0x00, 0x03, 0x20, 0x00, 0x03, 0x20, 0x00, 0x03, 0xC0,
    0x00, 0x01, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x01, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"v",86*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFB, 0xE7, 0xC0,
    0x70, 0xC1, 0x00, 0x30, 0xC1, 0x00, 0x30, 0xC1, 0x00, 0x30, 0xE2, 0x00, 0x10, 0xE2, 0x00, 0x18,
    0xE2, 0x00, 0x19, 0x66, 0x00, 0x19, 0x34, 0x00, 0x0D, 0x34, 0x00, 0x0F, 0x34, 0x00, 0x0E, 0x38,
    0x00, 0x0E, 0x18, 0x00, 0x06, 0x18, 0x00, 0x06, 0x18, 0x00, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"w",87*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x3E, 0x00,
    0x0E, 0x08, 0x00, 0x06, 0x10, 0x00, 0x06, 0x10, 0x00, 0x03, 0x20, 0x00, 0x03, 0xE0, 0x00, 0x01,
    0xC0, 0x00, 0x00, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x01, 0x60, 0x00, 0x02, 0x30, 0x00, 0x06, 0x30,
    0x00, 0x04, 0x18, 0x00, 0x08, 0x18, 0x00, 0x18, 0x0C, 0x00, 0x7C, 0x3F, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"x",88*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x1F, 0x80,
    0x0C, 0x06, 0x00, 0x0C, 0x04, 0x00, 0x06, 0x04, 0x00, 0x06, 0x0C, 0x00, 0x02, 0x08, 0x00, 0x03,
    0x08, 0x00, 0x03, 0x18, 0x00, 0x01, 0x90, 0x00, 0x01, 0x90, 0x00, 0x01, 0xB0, 0x00, 0x00, 0xE0,
    0x00, 0x00, 0xE0, 0x00, 0x00, 0x60, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x80, 0x00,
    0x00, 0x80, 0x00, 0x19, 0x80, 0x00, 0x1F, 0x00, 0x00, 0x1E, 0x00, 0x00, /*"y",89*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFC, 0x00,
    0x38, 0x1C, 0x00, 0x30, 0x38, 0x00, 0x20, 0x30, 0x00, 0x20, 0x70, 0x00, 0x00, 0xE0, 0x00, 0x00,
    0xC0, 0x00, 0x01, 0xC0, 0x00, 0x03, 0x80, 0x00, 0x03, 0x00, 0x00, 0x06, 0x02, 0x00, 0x0E, 0x02,
    0x00, 0x0C, 0x06, 0x00, 0x18, 0x04, 0x00, 0x38, 0x0C, 0x00, 0x3F, 0xFC, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"z",90*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x08, 0x00, 0x00, 0x10, 0x00, 0x00,
    0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10,
    0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00,
    0x00, 0x10, 0x00, 0x00, 0x60, 0x00, 0x00, 0x80, 0x00, 0x00, 0x60, 0x00, 0x00, 0x10, 0x00, 0x00,
    0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10,
    0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00,
    0x00, 0x08, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"{",91*/

    0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00,
    0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80,
    0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00,
    0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00,
    0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80,
    0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00,
    0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, /*"|",92*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x04, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02,
    0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00,
    0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00,
    0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x01, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02,
    0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00,
    0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00,
    0x04, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"}",93*/

    0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x23, 0x00, 0x00, 0x21, 0x80, 0x80, 0x40, 0xC0, 0x80, 0x40,
    0x61, 0x00, 0x00, 0x31, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"~",94*/
};

const esp_painter_basic_font_t esp_painter_basic_font_36 = {
    .bitmap = bitmap,
    .width = 18,
    .height = 36,
};

#endif
