/*
 * SPDX-FileCopyrightText: 2021-2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include "sdkconfig.h"

#include "esp_painter_font.h"

#if CONFIG_ESP_PAINTER_BASIC_FONT_12

static const uint8_t bitmap[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*" ",0*/

    0x00, 0x00, 0x20, 0x20, 0x20, 0x20, 0x20, 0x00, 0x00, 0x20, 0x00, 0x00, /*"!",1*/

    0x28, 0x28, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*""",2*/

    0x00, 0x00, 0x50, 0x50, 0xF8, 0x50, 0x50, 0xF8, 0x50, 0x50, 0x00, 0x00, /*"#",3*/

    0x00, 0x20, 0x70, 0xA8, 0xA0, 0x60, 0x30, 0x28, 0xA8, 0x70, 0x20, 0x00, /*"$",4*/

    0x00, 0x00, 0x48, 0xA8, 0xB0, 0xA8, 0x74, 0x34, 0x54, 0x48, 0x00, 0x00, /*"%",5*/

    0x00, 0x00, 0x20, 0x50, 0x50, 0x6C, 0xA8, 0xA8, 0x94, 0x68, 0x00, 0x00, /*"&",6*/

    0x40, 0x40, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"'",7*/

    0x08, 0x10, 0x10, 0x20, 0x20, 0x20, 0x20, 0x20, 0x10, 0x10, 0x08, 0x00, /*"(",8*/

    0x40, 0x20, 0x20, 0x10, 0x10, 0x10, 0x10, 0x10, 0x20, 0x20, 0x40, 0x00, /*")",9*/

    0x00, 0x00, 0x00, 0x20, 0xA8, 0x70, 0x70, 0xA8, 0x20, 0x00, 0x00, 0x00, /*"*",10*/

    0x00, 0x00, 0x00, 0x10, 0x10, 0x7C, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, /*"+",11*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x80, 0x00, /*",",12*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"-",13*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, /*".",14*/

    0x00, 0x04, 0x08, 0x08, 0x10, 0x10, 0x20, 0x20, 0x40, 0x40, 0x80, 0x00, /*"/",15*/

    0x00, 0x00, 0x70, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x70, 0x00, 0x00, /*"0",16*/

    0x00, 0x00, 0x20, 0x60, 0x20, 0x20, 0x20, 0x20, 0x20, 0x70, 0x00, 0x00, /*"1",17*/

    0x00, 0x00, 0x70, 0x88, 0x88, 0x10, 0x20, 0x40, 0x80, 0xF8, 0x00, 0x00, /*"2",18*/

    0x00, 0x00, 0x70, 0x88, 0x08, 0x30, 0x08, 0x08, 0x88, 0x70, 0x00, 0x00, /*"3",19*/

    0x00, 0x00, 0x10, 0x30, 0x30, 0x50, 0x90, 0xF8, 0x10, 0x38, 0x00, 0x00, /*"4",20*/

    0x00, 0x00, 0xF8, 0x80, 0x80, 0xF0, 0x88, 0x08, 0x88, 0x70, 0x00, 0x00, /*"5",21*/

    0x00, 0x00, 0x30, 0x48, 0x80, 0xB0, 0xC8, 0x88, 0x88, 0x70, 0x00, 0x00, /*"6",22*/

    0x00, 0x00, 0x78, 0x08, 0x10, 0x10, 0x20, 0x20, 0x20, 0x20, 0x00, 0x00, /*"7",23*/

    0x00, 0x00, 0x70, 0x88, 0x88, 0x70, 0x88, 0x88, 0x88, 0x70, 0x00, 0x00, /*"8",24*/

    0x00, 0x00, 0x70, 0x88, 0x88, 0x98, 0x68, 0x08, 0x90, 0x60, 0x00, 0x00, /*"9",25*/

    0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, /*":",26*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x20, 0x20, 0x00, /*";",27*/

    0x00, 0x00, 0x08, 0x10, 0x20, 0x40, 0x40, 0x20, 0x10, 0x08, 0x00, 0x00, /*"<",28*/

    0x00, 0x00, 0x00, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, /*"=",29*/

    0x00, 0x00, 0x40, 0x20, 0x10, 0x08, 0x08, 0x10, 0x20, 0x40, 0x00, 0x00, /*">",30*/

    0x00, 0x00, 0x70, 0x88, 0x88, 0x10, 0x20, 0x20, 0x00, 0x20, 0x00, 0x00, /*"?",31*/

    0x00, 0x00, 0x38, 0x44, 0x94, 0xB4, 0xB4, 0xB8, 0x44, 0x38, 0x00, 0x00, /*"@",32*/

    0x00, 0x00, 0x20, 0x20, 0x30, 0x50, 0x50, 0x78, 0x48, 0xCC, 0x00, 0x00, /*"A",33*/

    0x00, 0x00, 0xF0, 0x48, 0x48, 0x70, 0x48, 0x48, 0x48, 0xF0, 0x00, 0x00, /*"B",34*/

    0x00, 0x00, 0x78, 0x88, 0x80, 0x80, 0x80, 0x80, 0x88, 0x70, 0x00, 0x00, /*"C",35*/

    0x00, 0x00, 0xF0, 0x48, 0x48, 0x48, 0x48, 0x48, 0x48, 0xF0, 0x00, 0x00, /*"D",36*/

    0x00, 0x00, 0xF8, 0x48, 0x50, 0x70, 0x50, 0x40, 0x48, 0xF8, 0x00, 0x00, /*"E",37*/

    0x00, 0x00, 0xF8, 0x48, 0x50, 0x70, 0x50, 0x40, 0x40, 0xE0, 0x00, 0x00, /*"F",38*/

    0x00, 0x00, 0x38, 0x48, 0x80, 0x80, 0x9C, 0x88, 0x48, 0x30, 0x00, 0x00, /*"G",39*/

    0x00, 0x00, 0xCC, 0x48, 0x48, 0x78, 0x48, 0x48, 0x48, 0xCC, 0x00, 0x00, /*"H",40*/

    0x00, 0x00, 0xF8, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0xF8, 0x00, 0x00, /*"I",41*/

    0x00, 0x00, 0x7C, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x90, 0xE0, /*"J",42*/

    0x00, 0x00, 0xEC, 0x48, 0x50, 0x60, 0x50, 0x48, 0x48, 0xEC, 0x00, 0x00, /*"K",43*/

    0x00, 0x00, 0xE0, 0x40, 0x40, 0x40, 0x40, 0x40, 0x44, 0xFC, 0x00, 0x00, /*"L",44*/

    0x00, 0x00, 0xDC, 0xD8, 0xD8, 0xD8, 0xA8, 0xA8, 0xA8, 0xAC, 0x00, 0x00, /*"M",45*/

    0x00, 0x00, 0xDC, 0x48, 0x68, 0x68, 0x58, 0x58, 0x48, 0xE8, 0x00, 0x00, /*"N",46*/

    0x00, 0x00, 0x70, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x70, 0x00, 0x00, /*"O",47*/

    0x00, 0x00, 0xF0, 0x48, 0x48, 0x70, 0x40, 0x40, 0x40, 0xE0, 0x00, 0x00, /*"P",48*/

    0x00, 0x00, 0x70, 0x88, 0x88, 0x88, 0x88, 0xE8, 0x98, 0x70, 0x18, 0x00, /*"Q",49*/

    0x00, 0x00, 0xF0, 0x48, 0x48, 0x70, 0x50, 0x48, 0x48, 0xEC, 0x00, 0x00, /*"R",50*/

    0x00, 0x00, 0x78, 0x88, 0x80, 0x60, 0x10, 0x08, 0x88, 0xF0, 0x00, 0x00, /*"S",51*/

    0x00, 0x00, 0xF8, 0xA8, 0x20, 0x20, 0x20, 0x20, 0x20, 0x70, 0x00, 0x00, /*"T",52*/

    0x00, 0x00, 0xCC, 0x48, 0x48, 0x48, 0x48, 0x48, 0x48, 0x30, 0x00, 0x00, /*"U",53*/

    0x00, 0x00, 0xCC, 0x48, 0x48, 0x50, 0x50, 0x30, 0x20, 0x20, 0x00, 0x00, /*"V",54*/

    0x00, 0x00, 0xA8, 0xA8, 0xA8, 0xA8, 0x70, 0x50, 0x50, 0x50, 0x00, 0x00, /*"W",55*/

    0x00, 0x00, 0xD8, 0x50, 0x50, 0x20, 0x20, 0x50, 0x50, 0xD8, 0x00, 0x00, /*"X",56*/

    0x00, 0x00, 0xD8, 0x50, 0x50, 0x50, 0x20, 0x20, 0x20, 0x70, 0x00, 0x00, /*"Y",57*/

    0x00, 0x00, 0xF8, 0x90, 0x10, 0x20, 0x20, 0x40, 0x48, 0xF8, 0x00, 0x00, /*"Z",58*/

    0x38, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x38, 0x00, /*"[",59*/

    0x00, 0x40, 0x40, 0x20, 0x20, 0x20, 0x10, 0x10, 0x10, 0x08, 0x08, 0x00, /*"\",60*/

    0x70, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x70, 0x00, /*"]",61*/

    0x20, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"^",62*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, /*"_",63*/

    0x40, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"`",64*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x48, 0x38, 0x48, 0x3C, 0x00, 0x00, /*"a",65*/

    0x00, 0xC0, 0x40, 0x40, 0x40, 0x70, 0x48, 0x48, 0x48, 0x70, 0x00, 0x00, /*"b",66*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x48, 0x40, 0x48, 0x30, 0x00, 0x00, /*"c",67*/

    0x00, 0x18, 0x08, 0x08, 0x08, 0x38, 0x48, 0x48, 0x48, 0x3C, 0x00, 0x00, /*"d",68*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x48, 0x78, 0x40, 0x38, 0x00, 0x00, /*"e",69*/

    0x00, 0x18, 0x24, 0x20, 0x20, 0x78, 0x20, 0x20, 0x20, 0x78, 0x00, 0x00, /*"f",70*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x48, 0x30, 0x40, 0x38, 0x44, 0x38, /*"g",71*/

    0x00, 0xC0, 0x40, 0x40, 0x40, 0x70, 0x48, 0x48, 0x48, 0xEC, 0x00, 0x00, /*"h",72*/

    0x00, 0x20, 0x20, 0x00, 0x00, 0x60, 0x20, 0x20, 0x20, 0x70, 0x00, 0x00, /*"i",73*/

    0x00, 0x10, 0x10, 0x00, 0x00, 0x30, 0x10, 0x10, 0x10, 0x10, 0x10, 0xE0, /*"j",74*/

    0x00, 0xC0, 0x40, 0x40, 0x40, 0x58, 0x50, 0x60, 0x50, 0xC8, 0x00, 0x00, /*"k",75*/

    0x00, 0xE0, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0xF8, 0x00, 0x00, /*"l",76*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0xA8, 0xA8, 0xA8, 0xA8, 0x00, 0x00, /*"m",77*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x48, 0x48, 0x48, 0xEC, 0x00, 0x00, /*"n",78*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x48, 0x48, 0x48, 0x30, 0x00, 0x00, /*"o",79*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x48, 0x48, 0x48, 0x70, 0x40, 0xE0, /*"p",80*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x48, 0x48, 0x48, 0x38, 0x08, 0x1C, /*"q",81*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xD8, 0x60, 0x40, 0x40, 0xE0, 0x00, 0x00, /*"r",82*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x40, 0x30, 0x08, 0x78, 0x00, 0x00, /*"s",83*/

    0x00, 0x00, 0x00, 0x20, 0x20, 0x78, 0x20, 0x20, 0x20, 0x38, 0x00, 0x00, /*"t",84*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xD8, 0x48, 0x48, 0x48, 0x3C, 0x00, 0x00, /*"u",85*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xD8, 0x50, 0x50, 0x20, 0x20, 0x00, 0x00, /*"v",86*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xA8, 0xA8, 0x70, 0x50, 0x50, 0x00, 0x00, /*"w",87*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xD8, 0x50, 0x20, 0x50, 0xD8, 0x00, 0x00, /*"x",88*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0xCC, 0x48, 0x48, 0x30, 0x10, 0x20, 0xC0, /*"y",89*/

    0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x10, 0x20, 0x20, 0x78, 0x00, 0x00, /*"z",90*/

    0x18, 0x10, 0x10, 0x10, 0x10, 0x30, 0x10, 0x10, 0x10, 0x10, 0x18, 0x00, /*"{",91*/

    0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, /*"|",92*/

    0x60, 0x20, 0x20, 0x20, 0x20, 0x10, 0x20, 0x20, 0x20, 0x20, 0x60, 0x00, /*"}",93*/

    0x68, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*"~",94*/
};

const esp_painter_basic_font_t esp_painter_basic_font_12 = {
    .bitmap = bitmap,
    .width = 6,
    .height = 12,
};

#endif
