# support in, out, in_out type
# support i2c_port, i2s_port settings
# support pa_gain, i2c_addr setting

Board: S3_Korvo_V2
i2c: {sda: 17, scl: 18}
i2s: {mclk: 16, bclk: 9, ws: 45, din: 10, dout: 8}
out: {codec: ES8311, pa: 48, pa_gain: 6, use_mclk: 1, pa_gain:6}
in: {codec: ES7210}
sdcard: {clk: 15, cmd: 7, d0: 4}
camera: {type: dvp, xclk: 40, pclk: 11, vsync: 21, href: 38, d0: 13, d1: 47, d2: 14, d3: 3, d4: 12, d5: 42, d6: 41, d7: 39}
lcd: {
    bus: spi, extend_io: tca9554, controller: st7789, spi_bus: 2,
    mirror_x: 1, mirror_y: 1, swap_xy: 0, color_inv: 0,
    width: 320, height: 240,
    ctrl: ext1, rst: ext2,
    cs: ext3, dc: 2, clk: 1, mosi: 0, cmd_bits: 8, param_bits: 8
}

Board: DUMMY_CODEC_BOARD
i2s: {mclk: 5, bclk: 6, ws: 16, din: -1, dout: 8}
out: {codec: DUMMY, pa: 15, i2c_port: -1}

Board: S3_Korvo_V4
i2c: {sda: 1, scl: 2}
i2s: {mclk: 42, bclk: 40, ws: 41, dout: 39}
i2s: {mclk: 20, bclk: 10, ws: 9, din: 11}
out: {codec: ES8311, pa: 38, use_mclk: 0, pa_gain:6}
in: {codec: ES7210, i2s_port: 1}
sdcard: {clk: 18, cmd: 17, d0: 16}

Board: LYRAT_MINI_V1
i2c: {sda: 18, scl: 23}
i2s: {mclk: 0, bclk: 5, ws: 25, dout: 26, din: 35}
i2s: {mclk: -1, bclk: 32, ws: 33, din: 36}
out: {codec: ES8311, pa: 21, use_mclk: 1, pa_gain:20}
in: {codec: ES7243, i2s_port: 0}
sdcard: {power: 13, clk: 14, cmd: 15, d0: 2}

Board: ESP32_KORVO_V1
i2c: {sda: 19, scl: 32}
i2s: {mclk: 0, bclk: 25, ws: 22, dout: 13, din: -1}
i2s: {mclk: 0, bclk: 27, ws: 26, dout: -1, din: 36}
out: {codec: ES8311, pa: 12, use_mclk: 1,  pa_gain:6}
in: {codec: ES7210, i2s_port: 1}
sdcard: {clk: 14, cmd: 15, d0: 2}

Board: ESP32_LYRAT_V43
i2c: {sda: 18, scl: 23}
i2s: {mclk: -1, bclk: 5, ws: 25, dout: 26, din: 35}
out: {codec: ES8388, pa: 21, use_mclk: 0, pa_gain:6}

Board: ESP32S3_BOX
i2c: {sda: 8, scl: 18}
i2s: {mclk: 2, bclk: 17, ws: 47, dout: 15, din: 16}
out: {codec: ES8311, pa: 46, use_mclk: 1, pa_gain:6}
in: {codec: ES7210}

Board: ESP32_S3_BOX_3
i2c: {sda: 8, scl: 18, i2c_addr: 24}
i2s: {mclk: 2, bclk: 17, ws: 45, din: 16, dout: 15}
out: {codec: ES8311, pa: 46, use_mclk: 1, pa_gain: 6}
in: {codec: ES7210}

Board: ESP32_P4_DEV_V14
i2c: {sda: 7, scl: 8}
i2s: {mclk: 13, bclk: 12, ws: 10, dout: 9, din: 11}
in_out: {codec: ES8311, pa: 53, use_mclk: 1, pa_gain:6}
sdcard: {clk: 43, cmd: 44, d0: 39, d1: 40, d2: 41, d3: 42}
camera: {type: mipi}
lcd: {
    bus: mipi, ldo_chan: 3, ldo_voltage: 2500, lane_num: 2,
    lane_bitrate: 1000, dpi_clk: 80, bit_depth: 16, fb_num: 2
    dsi_hsync: 1344,  dsi_vsync: 635,
    dsi_hbp: 160, dsi_hfp: 160,
    dsi_vbp: 23, dsi_vfp: 12,
    rst: 27, ctrl_pin: 26,
    width: 1024, height: 600,
}

Board: ESP32S3_EYE
i2c: {sda: 4, scl: 5}
i2s: {mclk: -1, bclk: 41, ws: 42, dout: -1, din: 2}
in: {codec: DUMMY, i2c_port: -1}
sdcard: {clk: 39, cmd: 38, d0: 40}
camera: {type: dvp, xclk: 15, pclk: 13, vsync: 6, href: 7, d0: 11, d1: 9, d2: 8, d3: 10, d4: 12, d5: 18, d6: 17, d7: 16}
