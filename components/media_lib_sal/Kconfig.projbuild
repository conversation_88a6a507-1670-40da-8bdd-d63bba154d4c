menu "ADF Library Configuration"

config MEDIA_PROTOCOL_LIB_ENABLE
    bool "Enable Media Protocol Library"
    default "y"

config MEDIA_LIB_MEM_AUTO_TRACE
    bool "Support trace memory automatically after media_lib_sal init"
    default "n"

config MEDIA_LIB_MEM_TRACE_DEPTH
    int
    prompt "Memory trace stack depth" if MEDIA_LIB_MEM_AUTO_TRACE
    depends on MEDIA_LIB_MEM_AUTO_TRACE
    default 3
    help
        Set memory trace depth

config MEDIA_LIB_MEM_TRACE_NUM
    int
    prompt "Memory trace number" if MEDIA_LIB_MEM_AUTO_TRACE
    depends on MEDIA_LIB_MEM_AUTO_TRACE
    default 1024
    help
        Set memory trace number

config MEDIA_LIB_MEM_TRACE_MODULE
    bool "Trace for module memory usage"
    depends on MEDIA_LIB_MEM_AUTO_TRACE
    default y

config MEDIA_LIB_MEM_TRACE_LEAKAGE
    depends on MEDIA_LIB_MEM_AUTO_TRACE
    bool "Trace for memory leakage"
    default y

config MEDIA_LIB_MEM_TRACE_SAVE_HISTORY
    bool "Trace to save memory history"
    depends on MEDIA_LIB_MEM_AUTO_TRACE
    default n

config MEDIA_LIB_MEM_SAVE_CACHE_SIZE
    int
    prompt "Cache buffer size to store save history" if MEDIA_LIB_MEM_TRACE_SAVE_HISTORY
    depends on MEDIA_LIB_MEM_TRACE_SAVE_HISTORY
    default 32768
    help
        Set cache size for memory history

config MEDIA_LIB_MEM_TRACE_SAVE_PATH
    string "Memory trace save path" if MEDIA_LIB_MEM_TRACE_SAVE_HISTORY
    depends on MEDIA_LIB_MEM_TRACE_SAVE_HISTORY
    default "/sdcard/trace.log"
    help
        Set memory trace save path

endmenu
