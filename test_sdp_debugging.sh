#!/bin/bash

echo "🔍 SDP Debugging Test Script"
echo "============================"

# 编译项目
echo "📦 Building project..."
go build -o door-station-esp32 main.go
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"

echo ""
echo "🎯 NEW SDP DEBUGGING FEATURES:"
echo "✅ Real-time SDP content analysis"
echo "✅ Video/Audio media line detection"
echo "✅ Codec verification (H.264, PCMA, Opus)"
echo "✅ Media direction analysis (sendonly/sendrecv)"
echo "✅ Automatic black screen diagnosis"
echo ""

echo "🔍 What the SDP Analysis Will Show:"
echo ""
echo "📋 For each SDP (Offer/Answer):"
echo "  ✅ Media lines (m=video, m=audio)"
echo "  🎥 Video codecs (H.264)"
echo "  🎵 Audio codecs (PCMA, Opus)"
echo "  📤 Media directions"
echo "  📊 Summary statistics"
echo ""

echo "🚨 Key Diagnostic Messages:"
echo ""
echo "GOOD - Video Working:"
echo "  [DEBUG] ✅ Found video media line: m=video 9 UDP/TLS/RTP/SAVPF 96"
echo "  [DEBUG] 🎥 H.264 codec: a=rtpmap:96 H264/90000"
echo "  [DEBUG] 📊 SDP Analysis Summary:"
echo "  [DEBUG]   Has Video: true"
echo "  [DEBUG]   Video Codecs: 1 found"
echo ""

echo "BAD - No Video (Black Screen):"
echo "  [WARN] 🚨 NO VIDEO FOUND IN SDP! This explains the black screen!"
echo "  [DEBUG]   Has Video: false"
echo "  [DEBUG]   Video Codecs: 0 found"
echo ""

echo "🚀 Test Steps:"
echo ""
echo "1. Start the door station:"
echo "   ./door-station-esp32 https://webrtc.espressif.com sdp_debug_test"
echo ""
echo "2. Connect to room:"
echo "   door-station> connect sdp_debug_test"
echo ""
echo "3. Watch for SDP analysis logs when offer is created"
echo ""
echo "4. Open browser and connect to see answer analysis:"
echo "   https://webrtc.espressif.com/sdp_debug_test"
echo ""

echo "🔍 Expected Log Sequence:"
echo ""
echo "Step 1 - Media Setup:"
echo "  [INFO] Media stream created with 1 video tracks and 1 audio tracks"
echo "  [INFO] Added video track: [track-id]"
echo "  [INFO] Added audio track: [track-id]"
echo ""

echo "Step 2 - Offer Creation:"
echo "  [DEBUG] 🎬 Creating offer with 1 audio tracks and 1 video tracks"
echo "  [DEBUG] 📋 SDP Offer created, analyzing content..."
echo "  [DEBUG] ✅ Found video media line: m=video ..."
echo "  [DEBUG] 🎥 H.264 codec: a=rtpmap:96 H264/90000"
echo "  [DEBUG] 📊 SDP Analysis Summary:"
echo "  [DEBUG]   Has Video: true"
echo ""

echo "Step 3 - Answer Analysis:"
echo "  [DEBUG] 📋 Received SDP Answer, analyzing content..."
echo "  [DEBUG] ✅ Found video media line: m=video ..."
echo "  [DEBUG]   Has Video: true"
echo ""

echo "🎯 Diagnosis Guide:"
echo ""
echo "IF YOU SEE:"
echo "  ✅ 'Has Video: true' in BOTH offer and answer"
echo "  ✅ H.264 codec found"
echo "  ✅ Video tracks added to PeerConnection"
echo "  ❌ BUT browser still shows black screen"
echo ""
echo "THEN the problem is likely:"
echo "  🔧 Browser H.264 decoding issues"
echo "  🔧 WebRTC data transmission problems"
echo "  🔧 Network/firewall blocking video data"
echo ""

echo "IF YOU SEE:"
echo "  ❌ 'Has Video: false' in offer"
echo "  ❌ No H.264 codec found"
echo "  ❌ No video tracks added"
echo ""
echo "THEN the problem is:"
echo "  🔧 Camera access issues"
echo "  🔧 MediaDevices configuration problems"
echo "  🔧 Video track creation failures"
echo ""

echo "🛠️ Next Steps Based on Results:"
echo ""
echo "Scenario A - SDP has video, browser shows black:"
echo "  1. Check browser console for WebRTC errors"
echo "  2. Test with different browsers"
echo "  3. Verify network connectivity"
echo "  4. Check H.264 hardware decoding support"
echo ""

echo "Scenario B - SDP missing video:"
echo "  1. Check camera permissions"
echo "  2. Verify mediadevices configuration"
echo "  3. Test camera access independently"
echo "  4. Check video constraints"
echo ""

echo "🎉 Ready to Debug!"
echo ""
echo "Run the test and look for the SDP analysis logs."
echo "The detailed SDP content analysis will tell us exactly"
echo "whether the video data is being included in the WebRTC"
echo "negotiation or not!"
echo ""
echo "This will definitively answer whether the black screen"
echo "is caused by:"
echo "  📹 Missing video in SDP (our problem)"
echo "  🌐 Browser decoding issues (browser problem)"
echo "  🔗 Network transmission issues (network problem)"
