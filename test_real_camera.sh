#!/bin/bash

# 真实摄像头捕获测试脚本

echo "=== 真实摄像头捕获测试 ==="
echo

# 检查FFmpeg是否安装
if ! command -v ffmpeg >/dev/null 2>&1; then
    echo "❌ FFmpeg未安装，请先安装FFmpeg:"
    echo "   macOS: brew install ffmpeg"
    echo "   Ubuntu: sudo apt install ffmpeg"
    echo "   Windows: 下载FFmpeg并添加到PATH"
    exit 1
fi

echo "✅ FFmpeg已安装: $(ffmpeg -version | head -1)"
echo

# 检测操作系统
OS=$(uname -s)
echo "🖥️  操作系统: $OS"

# 检查摄像头设备
echo "📹 检查可用的摄像头设备:"
if [[ "$OS" == "Darwin" ]]; then
    # macOS
    echo "macOS系统，使用avfoundation驱动"
    echo "可用设备列表:"
    ffmpeg -f avfoundation -list_devices true -i "" 2>&1 | grep -E "\[AVFoundation\].*video" | head -5
    
    # 测试默认摄像头
    echo
    echo "🧪 测试默认摄像头 (设备0):"
    timeout 10s ffmpeg -f avfoundation -i "0" -vframes 1 -f mjpeg -q:v 3 -s 320x240 -pix_fmt yuvj420p -y test_camera_0.jpg -loglevel error
    
elif [[ "$OS" == "Linux" ]]; then
    # Linux
    echo "Linux系统，使用v4l2驱动"
    if command -v v4l2-ctl >/dev/null 2>&1; then
        echo "可用设备列表:"
        v4l2-ctl --list-devices | head -10
    else
        echo "可用设备:"
        ls /dev/video* 2>/dev/null || echo "未找到摄像头设备"
    fi
    
    # 测试默认摄像头
    echo
    echo "🧪 测试默认摄像头 (/dev/video0):"
    timeout 10s ffmpeg -f v4l2 -i "/dev/video0" -vframes 1 -f mjpeg -q:v 3 -s 320x240 -pix_fmt yuvj420p -y test_camera_0.jpg -loglevel error
    
else
    echo "Windows系统，使用dshow驱动"
    echo "可用设备列表:"
    ffmpeg -f dshow -list_devices true -i dummy 2>&1 | grep -E "DirectShow.*video" | head -5
    
    # 测试常见摄像头名称
    echo
    echo "🧪 测试常见摄像头设备:"
    timeout 10s ffmpeg -f dshow -i "video=USB2.0 Camera" -vframes 1 -f mjpeg -q:v 3 -s 320x240 -pix_fmt yuvj420p -y test_camera_0.jpg -loglevel error
fi

# 检查测试结果
echo
if [ -f "test_camera_0.jpg" ]; then
    size=$(stat -f%z "test_camera_0.jpg" 2>/dev/null || stat -c%s "test_camera_0.jpg" 2>/dev/null)
    if [ "$size" -gt 1000 ]; then
        echo "✅ 摄像头测试成功: test_camera_0.jpg (${size} bytes)"
        
        # 检查JPEG头部
        if command -v hexdump >/dev/null 2>&1; then
            echo "📸 JPEG头部信息:"
            hexdump -C test_camera_0.jpg | head -2
        fi
        
        echo "🖼️  可以用以下命令查看捕获的图片:"
        echo "   open test_camera_0.jpg  (macOS)"
        echo "   xdg-open test_camera_0.jpg  (Linux)"
        echo "   start test_camera_0.jpg  (Windows)"
        
        CAMERA_WORKS=true
    else
        echo "❌ 摄像头测试失败: 文件太小 (${size} bytes)"
        CAMERA_WORKS=false
    fi
else
    echo "❌ 摄像头测试失败: 无法捕获图像"
    CAMERA_WORKS=false
fi

echo
echo "🚀 启动Go程序进行真实摄像头视频通话测试..."
echo

if [ "$CAMERA_WORKS" = true ]; then
    echo "✅ 摄像头工作正常，将使用真实摄像头数据"
    echo "预期效果:"
    echo "  - [MEDIA] 成功捕获摄像头帧: 30 (大小: xxxx bytes, 分辨率: 320x240)"
    echo "  - ESP32将接收到真实的摄像头画面"
    echo "  - 可以看到真实的环境和人物"
else
    echo "⚠️  摄像头无法正常工作，将使用备用方案"
    echo "预期效果:"
    echo "  - [MEDIA] 摄像头捕获失败，尝试备用方案"
    echo "  - [MEDIA] 生成高质量测试图像: 红色, 帧 30"
    echo "  - ESP32将看到彩色的测试图案"
fi

echo
echo "操作步骤:"
echo "1. 程序启动后，输入: join d0002"
echo "2. 启用监控: monitor"
echo "3. 在ESP32上加入房间: join d0002"
echo "4. 在ESP32上发起呼叫: b"
echo "5. 在程序中接受呼叫: accept"
echo "6. 观察日志和ESP32显示效果"
echo

# 清理之前的调试文件
rm -f debug/received_video.h264 debug/received_video.mjpeg debug/received_audio.pcma debug/monitor.log debug/camera_frame_*.jpg debug/sent_frame_*.jpg

echo "✅ 已清理调试文件"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 清理测试文件
rm -f test_camera_0.jpg

# 检查结果
echo "检查测试结果:"

# 检查发送的摄像头帧
if ls debug/camera_frame_*.jpg 1> /dev/null 2>&1; then
    echo "✅ 发现摄像头捕获帧:"
    for file in debug/camera_frame_*.jpg; do
        if [ -f "$file" ]; then
            size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
            echo "  📸 $file (${size} bytes) - 真实摄像头数据"
        fi
    done
    echo "🎉 成功使用真实摄像头进行视频通话！"
elif ls debug/sent_frame_*.jpg 1> /dev/null 2>&1; then
    echo "⚠️  使用了备用测试图像:"
    for file in debug/sent_frame_*.jpg; do
        if [ -f "$file" ]; then
            size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
            echo "  🎨 $file (${size} bytes) - 测试图像"
        fi
    done
    echo "💡 摄像头可能需要权限或配置调整"
else
    echo "❌ 没有找到发送的图像帧"
fi

# 检查接收的数据
if [ -f "debug/received_video.mjpeg" ]; then
    size=$(stat -f%z "debug/received_video.mjpeg" 2>/dev/null || stat -c%s "debug/received_video.mjpeg" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 接收MJPEG文件: ${size} bytes (ESP32摄像头数据)"
        echo "🎬 播放命令:"
        echo "   ffplay -f mjpeg debug/received_video.mjpeg"
    else
        echo "❌ 接收MJPEG文件: 0 bytes"
    fi
else
    echo "❌ 接收MJPEG文件: 不存在"
fi

echo
echo "摄像头权限说明:"
echo "如果摄像头捕获失败，可能需要:"
echo "1. macOS: 系统偏好设置 -> 安全性与隐私 -> 隐私 -> 摄像头"
echo "2. Linux: 检查用户是否在video组: sudo usermod -a -G video \$USER"
echo "3. Windows: 检查摄像头驱动和权限设置"
echo
echo "故障排除:"
echo "1. 确保摄像头没有被其他程序占用"
echo "2. 尝试重启摄像头相关服务"
echo "3. 检查FFmpeg版本是否支持你的摄像头驱动"
echo "4. 查看详细的FFmpeg错误日志"
