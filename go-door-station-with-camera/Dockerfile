# 多阶段构建Dockerfile for Door Station

# 构建阶段
FROM golang:1.19-bullseye AS builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    libx264-dev \
    libopus-dev \
    libvpx-dev \
    v4l-utils \
    && rm -rf /var/lib/apt/lists/*

# 复制Go模块文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o door-station main.go

# 运行时阶段
FROM debian:bullseye-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libx264-160 \
    libopus0 \
    libvpx6 \
    v4l-utils \
    alsa-utils \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r doorstation && useradd -r -g doorstation doorstation

# 创建必要的目录
RUN mkdir -p /var/log/doorstation && \
    chown doorstation:doorstation /var/log/doorstation

# 复制构建的应用
COPY --from=builder /app/door-station /usr/local/bin/door-station
RUN chmod +x /usr/local/bin/door-station

# 复制配置文件示例
COPY --from=builder /app/examples/config.yaml /etc/doorstation/config.yaml

# 设置用户
USER doorstation

# 设置环境变量
ENV PION_LOG_DEBUG=""
ENV GOMEMLIMIT=256MiB

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD pgrep door-station || exit 1

# 暴露端口 (WebRTC动态端口)
EXPOSE 8080

# 设置入口点
ENTRYPOINT ["door-station"]

# 默认参数
CMD ["--help"]

# 标签信息
LABEL maintainer="Door Station Team" \
      version="1.0.0" \
      description="Real Camera Door Station with WebRTC" \
      org.opencontainers.image.source="https://github.com/your-org/door-station" 