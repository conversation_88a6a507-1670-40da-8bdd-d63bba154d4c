# 🚪 基于真实摄像头的门禁主机方案

这是一个使用 [pion/mediadevices](https://github.com/pion/mediadevices) 实现真实摄像头推流的完整门禁主机解决方案。

## 🎯 方案特点

- ✅ **真实摄像头**：使用系统摄像头进行实时视频流推送
- ✅ **高质量编码**：支持H.264硬件/软件编码和Opus音频编码
- ✅ **AppRTC信令**：完全兼容ESP32分机端的信令协议
- ✅ **灵活配置**：支持自定义视频参数、设备选择等
- ✅ **跨平台**：支持Linux、macOS、Windows

## 🛠️ 系统要求

### 操作系统支持
- **Linux**: Ubuntu 20.04+, CentOS 8+
- **macOS**: 10.15+
- **Windows**: 10/11

### 依赖库安装

#### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装编译工具
sudo apt install build-essential pkg-config

# 安装视频编码库
sudo apt install libx264-dev libvpx-dev

# 安装音频编码库
sudo apt install libopus-dev

# 安装摄像头支持
sudo apt install v4l-utils

# 可选：安装GStreamer (用于高级视频处理)
sudo apt install libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev
```

#### macOS
```bash
# 使用Homebrew安装依赖
brew install x264 opus libvpx pkg-config

# 安装Go (如果还没有)
brew install go
```

#### Windows
```bash
# 使用chocolatey安装依赖 (以管理员身份运行)
choco install golang

# 手动下载并配置以下库：
# - x264: https://www.videolan.org/developers/x264.html  
# - opus: https://opus-codec.org/downloads/
# - 或使用预编译包
```

### Go环境
- Go 1.19 或更高版本

## 🚀 快速开始

### 1. 克隆和初始化

```bash
# 进入项目目录
cd go-door-station-with-camera

# 初始化Go模块和下载依赖
go mod tidy

# 验证依赖安装
go mod verify
```

### 2. 查看可用设备

```bash
# 列出可用的摄像头和麦克风设备
go run main.go --list-devices
```

### 3. 基本使用

```bash
# 使用默认配置启动
go run main.go https://webrtc.espressif.com test_room_123

# 自定义视频参数
go run main.go https://webrtc.espressif.com test_room_123 \
  --width=1280 --height=720 --fps=30 --bitrate=2000000

# 指定特定设备
go run main.go https://webrtc.espressif.com test_room_123 \
  --camera=/dev/video0 --mic=default
```

### 4. 编译独立可执行文件

```bash
# 编译
go build -o door-station main.go

# 运行
./door-station https://webrtc.espressif.com test_room_123
```

## 📊 配置参数详解

### 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--width` | 640 | 视频宽度 |
| `--height` | 480 | 视频高度 |
| `--fps` | 15 | 视频帧率 |
| `--bitrate` | 1000000 | 视频比特率(bps) |
| `--camera` | (默认) | 摄像头设备ID |
| `--mic` | (默认) | 麦克风设备ID |

### 设备标识符格式

#### Linux
```bash
# 摄像头设备
--camera=/dev/video0
--camera=/dev/video1

# 音频设备  
--mic=default
--mic=hw:0,0
```

#### macOS
```bash
# 摄像头设备 (通常是数字)
--camera=0
--camera=1

# 音频设备
--mic=default
```

#### Windows
```bash
# 摄像头设备
--camera=0
--camera=1

# 音频设备
--mic=default
```

## 🎥 视频质量优化

### 编码预设对比

| 预设 | 编码速度 | 文件大小 | 质量 | 推荐场景 |
|------|----------|----------|------|----------|
| `ultrafast` | 最快 | 最大 | 较低 | 低延迟要求 |
| `fast` | 快 | 大 | 中等 | 实时通信 |
| `medium` | 中等 | 中等 | 好 | **默认推荐** |
| `slow` | 慢 | 小 | 很好 | 高质量要求 |

### 不同场景的推荐配置

#### 🏠 家庭门禁 (低带宽)
```bash
go run main.go https://webrtc.espressif.com room_home \
  --width=640 --height=480 --fps=15 --bitrate=500000
```

#### 🏢 商业门禁 (标准质量)
```bash
go run main.go https://webrtc.espressif.com room_office \
  --width=1280 --height=720 --fps=20 --bitrate=1500000
```

#### 🏛️ 高安全门禁 (高质量)
```bash
go run main.go https://webrtc.espressif.com room_secure \
  --width=1920 --height=1080 --fps=30 --bitrate=3000000
```

## 🔧 故障排除

### 常见问题

#### 1. 摄像头设备不可用
```bash
# Linux: 检查设备权限
ls -la /dev/video*
sudo chmod 666 /dev/video0

# 检查是否被其他程序占用
lsof /dev/video0

# 验证摄像头功能
ffplay /dev/video0
```

#### 2. 编码器初始化失败
```bash
# 检查x264库安装
pkg-config --exists x264 && echo "x264 OK" || echo "x264 missing"

# 检查opus库安装  
pkg-config --exists opus && echo "opus OK" || echo "opus missing"

# Ubuntu重新安装
sudo apt install --reinstall libx264-dev libopus-dev
```

#### 3. WebRTC连接失败
```bash
# 检查网络连接
curl -I https://webrtc.espressif.com

# 检查防火墙设置
sudo ufw status

# 启用详细日志
export PION_LOG_DEBUG=all
go run main.go https://webrtc.espressif.com test_room
```

#### 4. 音频问题
```bash
# Linux: 检查ALSA设备
arecord -l

# 测试麦克风
arecord -d 5 test.wav && aplay test.wav

# 调整权限
sudo usermod -a -G audio $USER
```

### 性能优化

#### CPU优化
```bash
# 使用硬件编码器 (如果支持)
export LIBVA_DRIVER_NAME=i965  # Intel
export LIBVA_DRIVER_NAME=radeonsi  # AMD

# 降低CPU使用率
go run main.go ... --fps=10 --bitrate=800000
```

#### 内存优化
```bash
# 限制Go内存使用
export GOMEMLIMIT=256MiB
go run main.go ...
```

## 🔗 与ESP32分机端连接

### 步骤说明

1. **准备ESP32分机**
   ```bash
   # 修改ESP32代码，设置视频方向为接收
   .video_dir = ESP_PEER_MEDIA_DIR_RECV_ONLY
   
   # 烧录并启动分机
   idf.py build flash monitor
   ```

2. **启动门禁主机**
   ```bash
   # 使用相同的房间ID
   go run main.go https://webrtc.espressif.com intercom_room_001
   ```

3. **建立连接**
   ```bash
   # 在ESP32串口中加入房间
   join intercom_room_001
   
   # 观察日志确认连接建立
   ```

### 通信流程

```mermaid
sequenceDiagram
    participant Go as Go门禁主机
    participant Server as AppRTC服务器  
    participant ESP32 as ESP32分机

    Go->>Server: 加入房间 (initiator=true)
    ESP32->>Server: 加入房间 (initiator=false)
    
    Go->>ESP32: 发送CALL_REQUEST
    ESP32-->>Go: 发送CALL_ACCEPT
    
    Go->>ESP32: 发送SDP Offer (包含摄像头流)
    ESP32-->>Go: 发送SDP Answer
    
    Note over Go,ESP32: WebRTC连接建立
    Note over Go,ESP32: 开始摄像头视频传输
```

## 📁 项目结构

```
go-door-station-with-camera/
├── main.go              # 主程序
├── go.mod              # Go模块定义
├── go.sum              # 依赖版本锁定
├── README.md           # 本文档
├── examples/           # 示例配置
│   ├── config.yaml     # 配置文件示例
│   └── scripts/        # 启动脚本
├── docs/               # 详细文档
│   ├── installation.md # 安装指南
│   ├── configuration.md# 配置详解
│   └── troubleshooting.md# 故障排除
└── Dockerfile          # Docker部署
```

## 🐳 Docker部署

### 构建镜像
```bash
# 创建Dockerfile
cat > Dockerfile << 'EOF'
FROM golang:1.19-bullseye AS builder

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libx264-dev libopus-dev libvpx-dev \
    pkg-config build-essential

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o door-station main.go

FROM debian:bullseye-slim
RUN apt-get update && apt-get install -y \
    libx264-160 libopus0 libvpx6 \
    v4l-utils && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/door-station /usr/local/bin/
ENTRYPOINT ["door-station"]
EOF

# 构建
docker build -t door-station .
```

### 运行容器
```bash
# 基本运行
docker run --rm \
  --device=/dev/video0:/dev/video0 \
  door-station https://webrtc.espressif.com test_room

# 带音频设备
docker run --rm \
  --device=/dev/video0:/dev/video0 \
  --device=/dev/snd:/dev/snd \
  door-station https://webrtc.espressif.com test_room
```

## 🤝 集成和扩展

### 与其他系统集成

#### 1. 门锁控制系统
```go
// 扩展handleDoorOpen函数
func (ds *DoorStation) handleDoorOpen() {
    log.Println("🚪 Controlling physical door lock...")
    
    // GPIO控制 (树莓派)
    if err := controlGPIO(DOOR_RELAY_PIN, true); err != nil {
        log.Printf("Failed to control door: %v", err)
        return
    }
    
    // 延时2秒后关闭
    time.Sleep(2 * time.Second)
    controlGPIO(DOOR_RELAY_PIN, false)
}
```

#### 2. 数据库记录
```go
// 记录通话日志
func (ds *DoorStation) logCallEvent(event string) {
    record := CallLog{
        Timestamp: time.Now(),
        Event:     event,
        RoomID:    ds.config.RoomID,
    }
    db.Insert(record)
}
```

#### 3. 推送通知
```go
// 发送手机推送
func (ds *DoorStation) sendPushNotification(message string) {
    notificationService.Send(NotificationRequest{
        Title:   "门禁系统",
        Message: message,
        Type:    "door_event",
    })
}
```

## 📞 技术支持

### 性能指标
- **延迟**: < 200ms (局域网)
- **带宽**: 500KB/s - 3MB/s (可配置)
- **分辨率**: 支持最高4K
- **帧率**: 支持最高60fps
- **音频**: 48kHz立体声

### 已知限制
1. macOS上某些USB摄像头可能需要额外驱动
2. Windows需要Visual C++运行时库
3. 高分辨率下CPU使用率较高

### 获取帮助
- GitHub Issues: [项目链接]
- 技术文档: [docs目录]
- 示例代码: [examples目录]

---

**注意**: 这个方案需要与前面提供的ESP32分机代码配合使用，确保两端使用相同的房间ID和信令协议。 