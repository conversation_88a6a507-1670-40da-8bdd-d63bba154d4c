package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"image"
	"image/color"
	"image/jpeg"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/pion/webrtc/v4"
)

// AppRTC房间信息响应
type AppRTCResponse struct {
	Result string `json:"result"`
	Params struct {
		ClientID     string   `json:"client_id"`
		IsInitiator  string   `json:"is_initiator"`
		WssURL       string   `json:"wss_url"`
		WssPostURL   string   `json:"wss_post_url"`
		RoomID       string   `json:"room_id"`
		RoomLink     string   `json:"room_link"`
		IceServerURL string   `json:"ice_server_url"`
		Messages     []string `json:"messages"`
	} `json:"params"`
}

// ICE服务器响应
type IceServersResponse struct {
	IceServers []struct {
		URLs       []string `json:"urls"`
		Username   string   `json:"username"`
		Credential string   `json:"credential"`
	} `json:"iceServers"`
}

// WebSocket消息格式
type WSMessage struct {
	Cmd      string `json:"cmd"`
	Msg      string `json:"msg,omitempty"`
	RoomID   string `json:"roomid,omitempty"`
	ClientID string `json:"clientid,omitempty"`
}

// WebRTC消息格式
type RTCMessage struct {
	Type      string `json:"type"`
	SDP       string `json:"sdp,omitempty"`
	Candidate string `json:"candidate,omitempty"`
	Data      string `json:"data,omitempty"`
}

// 门禁主机配置
type DoorStationConfig struct {
	ServerURL string
	// 视频配置
	VideoWidth   int
	VideoHeight  int
	VideoFPS     int
	VideoBitrate int
	// 音频配置
	AudioSampleRate int
	AudioChannels   int
	// 设备配置
	CameraDeviceID string
	MicDeviceID    string
}

// 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateInCall
)

func (s ConnectionState) String() string {
	switch s {
	case StateDisconnected:
		return "Disconnected"
	case StateConnecting:
		return "Connecting"
	case StateConnected:
		return "Connected"
	case StateInCall:
		return "In Call"
	default:
		return "Unknown"
	}
}

// 视频调试统计
type VideoDebugStats struct {
	FrameCount    int64
	LastFrameTime time.Time
	FrameRate     float64
	TotalBytes    int64
	Width         int
	Height        int
	mutex         sync.RWMutex
}

// 门禁主机结构
type DoorStation struct {
	config      DoorStationConfig
	currentRoom string
	state       ConnectionState
	clientInfo  AppRTCResponse
	wsConn      *websocket.Conn
	peerConn    *webrtc.PeerConnection
	iceServers  []webrtc.ICEServer
	ctx         context.Context
	cancel      context.CancelFunc
	// AstiAV媒体流
	astiavStream *AstiAVMediaStream

	// 调试模式
	debugMode bool
	// 视频调试
	videoStats VideoDebugStats
}

func NewDoorStation(config DoorStationConfig) *DoorStation {
	ctx, cancel := context.WithCancel(context.Background())
	return &DoorStation{
		config: config,
		state:  StateDisconnected,
		ctx:    ctx,
		cancel: cancel,
	}
}

// 获取房间信息
func (ds *DoorStation) getRoomInfo(roomID string) error {
	joinURL := fmt.Sprintf("%s/join/%s", ds.config.ServerURL, roomID)

	resp, err := http.Post(joinURL, "application/json", nil)
	if err != nil {
		return fmt.Errorf("failed to post to room: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	if err := json.Unmarshal(body, &ds.clientInfo); err != nil {
		return fmt.Errorf("failed to parse room info: %v", err)
	}

	// 保持原始的initiator设置 - 让AppRTC决定角色
	log.Printf("[INFO] Got room info: ClientID=%s, IsInitiator=%s",
		ds.clientInfo.Params.ClientID, ds.clientInfo.Params.IsInitiator)

	return nil
}

// 获取ICE服务器信息
func (ds *DoorStation) getIceServers() error {
	if ds.clientInfo.Params.IceServerURL == "" {
		log.Println("[INFO] No ICE server URL provided")
		return nil
	}

	resp, err := http.Post(ds.clientInfo.Params.IceServerURL, "application/json", nil)
	if err != nil {
		return fmt.Errorf("failed to get ICE servers: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read ICE response: %v", err)
	}

	var iceResp IceServersResponse
	if err := json.Unmarshal(body, &iceResp); err != nil {
		return fmt.Errorf("failed to parse ICE servers: %v", err)
	}

	// 清空现有ICE服务器
	ds.iceServers = nil

	// 转换为pion格式
	for _, server := range iceResp.IceServers {
		ice := webrtc.ICEServer{
			URLs: server.URLs,
		}
		if server.Username != "" {
			ice.Username = server.Username
			ice.Credential = server.Credential
		}
		ds.iceServers = append(ds.iceServers, ice)
	}

	log.Printf("[INFO] Got %d ICE servers", len(ds.iceServers))
	return nil
}

// 设置AstiAV媒体设备
func (ds *DoorStation) setupMediaDevices() error {
	if ds.astiavStream != nil {
		log.Println("[INFO] AstiAV media devices already setup")
		return nil
	}

	log.Println("[INFO] 🎬 Setting up AstiAV video capture for real camera access...")

	// 创建AstiAV配置
	astiavConfig := AstiAVConfig{
		Width:    ds.config.VideoWidth,
		Height:   ds.config.VideoHeight,
		FPS:      ds.config.VideoFPS,
		DeviceID: ds.config.CameraDeviceID,
		Format:   "avfoundation", // macOS默认，Linux会自动切换到v4l2
	}

	// 如果设备ID为空，使用默认摄像头
	if astiavConfig.DeviceID == "" {
		astiavConfig.DeviceID = "0"
	}

	log.Printf("[DEBUG] AstiAV config: %dx%d@%dfps, device=%s",
		astiavConfig.Width, astiavConfig.Height, astiavConfig.FPS, astiavConfig.DeviceID)

	// 创建AstiAV媒体流
	stream, err := NewAstiAVMediaStream(astiavConfig)
	if err != nil {
		return fmt.Errorf("failed to create AstiAV media stream: %v", err)
	}

	ds.astiavStream = stream

	// 列出可用设备（用于调试）
	log.Println("[DEBUG] 📹 Listing available video devices...")
	if err := stream.ListDevices(); err != nil {
		log.Printf("[WARN] Failed to list devices: %v", err)
	}

	// 启用调试模式
	if ds.debugMode {
		stream.EnableDebug()
	}

	log.Printf("[INFO] ✅ AstiAV media stream created with video and audio tracks")

	// 详细检查轨道
	videoTracks := stream.GetVideoTracks()
	audioTracks := stream.GetAudioTracks()

	for i, track := range videoTracks {
		log.Printf("[DEBUG] AstiAV Video Track %d: ID=%s, Kind=%s",
			i, track.ID(), track.Kind())
	}

	for i, track := range audioTracks {
		log.Printf("[DEBUG] AstiAV Audio Track %d: ID=%s, Kind=%s",
			i, track.ID(), track.Kind())
	}

	// 启动AstiAV视频流
	log.Println("[INFO] 🚀 Starting AstiAV video streaming...")
	if err := stream.StartStreaming(); err != nil {
		return fmt.Errorf("failed to start AstiAV streaming: %v", err)
	}

	log.Println("[INFO] ✅ AstiAV video streaming started successfully!")

	return nil
}

// 视频调试功能
func (ds *DoorStation) enableVideoDebug() {
	ds.debugMode = true
	log.Println("[DEBUG] Video debugging enabled")

	// 创建调试目录
	if err := os.MkdirAll("video_debug", 0755); err != nil {
		log.Printf("[WARN] Failed to create debug directory: %v", err)
	}

	// 启动视频统计监控
	go ds.monitorVideoStats()
}

// 监控视频统计
func (ds *DoorStation) monitorVideoStats() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ds.ctx.Done():
			return
		case <-ticker.C:
			ds.logVideoStats()
		}
	}
}

// 记录视频统计
func (ds *DoorStation) logVideoStats() {
	ds.videoStats.mutex.RLock()
	defer ds.videoStats.mutex.RUnlock()

	log.Printf("[VIDEO_STATS] Frames: %d, FPS: %.2f, Resolution: %dx%d, Total Bytes: %d",
		ds.videoStats.FrameCount,
		ds.videoStats.FrameRate,
		ds.videoStats.Width,
		ds.videoStats.Height,
		ds.videoStats.TotalBytes)
}

// 更新视频统计
func (ds *DoorStation) updateVideoStats(frameSize int, width, height int) {
	ds.videoStats.mutex.Lock()
	defer ds.videoStats.mutex.Unlock()

	now := time.Now()
	ds.videoStats.FrameCount++
	ds.videoStats.TotalBytes += int64(frameSize)
	ds.videoStats.Width = width
	ds.videoStats.Height = height

	// 计算帧率
	if !ds.videoStats.LastFrameTime.IsZero() {
		timeDiff := now.Sub(ds.videoStats.LastFrameTime).Seconds()
		if timeDiff > 0 {
			ds.videoStats.FrameRate = 1.0 / timeDiff
		}
	}
	ds.videoStats.LastFrameTime = now
}

// 保存视频帧为图片
func (ds *DoorStation) saveVideoFrame(img image.Image, frameNumber int64) error {
	if !ds.debugMode {
		return nil
	}

	filename := fmt.Sprintf("video_debug/frame_%06d.jpg", frameNumber)
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create frame file: %v", err)
	}
	defer file.Close()

	// 保存为JPEG格式
	if err := jpeg.Encode(file, img, &jpeg.Options{Quality: 90}); err != nil {
		return fmt.Errorf("failed to encode frame: %v", err)
	}

	log.Printf("[DEBUG] Saved frame %d to %s", frameNumber, filename)
	return nil
}

// 测试真实摄像头访问
func (ds *DoorStation) testRealCameraAccess() error {
	log.Printf("[DEBUG] Testing REAL camera access with AstiAV...")

	if ds.astiavStream == nil {
		return fmt.Errorf("AstiAV stream not initialized")
	}

	// 列出可用设备
	if err := ds.astiavStream.ListDevices(); err != nil {
		log.Printf("[WARN] Failed to list devices: %v", err)
	}

	log.Printf("[INFO] ✅ AstiAV camera access test completed")
	return nil
}

// 测试视频捕获
func (ds *DoorStation) testVideoCapture(duration time.Duration) error {
	log.Printf("[DEBUG] Starting REAL video capture test for %v", duration)

	// 首先测试摄像头访问
	if err := ds.testRealCameraAccess(); err != nil {
		log.Printf("[ERROR] Camera access test failed: %v", err)
		return err
	}

	if ds.astiavStream == nil {
		return fmt.Errorf("AstiAV stream not initialized")
	}

	videoTracks := ds.astiavStream.GetVideoTracks()
	if len(videoTracks) == 0 {
		return fmt.Errorf("no video tracks available")
	}

	track := videoTracks[0]
	log.Printf("[DEBUG] Testing video track: %s", track.ID())

	// 启动视频调试
	ds.enableVideoDebug()

	log.Printf("[INFO] 🎬 Testing AstiAV video capture with REAL camera data!")
	log.Printf("[INFO] AstiAV will capture actual frames from your camera")

	// 获取AstiAV统计信息
	go func() {
		startTime := time.Now()
		initialFrameCount, initialBytes, _ := ds.astiavStream.GetStats()

		log.Printf("[DEBUG] Starting AstiAV video capture monitoring...")

		// 监控AstiAV统计
		ticker := time.NewTicker(2 * time.Second) // 每2秒检查一次
		defer ticker.Stop()

		for time.Since(startTime) < duration {
			select {
			case <-ds.ctx.Done():
				return
			case <-ticker.C:
				frameCount, totalBytes, fps := ds.astiavStream.GetStats()
				newFrames := frameCount - initialFrameCount
				newBytes := totalBytes - initialBytes

				log.Printf("[DEBUG] 📊 AstiAV Stats: %d frames (+%d), %d bytes (+%d), %.1f fps",
					frameCount, newFrames, totalBytes, newBytes, fps)

				// 更新统计
				if newFrames > 0 {
					avgFrameSize := int(newBytes / newFrames)
					ds.updateVideoStats(avgFrameSize, ds.config.VideoWidth, ds.config.VideoHeight)
				}

				// 检查是否有新帧
				if newFrames > 0 {
					log.Printf("[INFO] ✅ AstiAV is capturing REAL camera frames!")
				} else {
					log.Printf("[WARN] ⚠️  No new frames captured in last 2 seconds")
				}
			}
		}

		finalFrameCount, finalBytes, finalFPS := ds.astiavStream.GetStats()
		totalFrames := finalFrameCount - initialFrameCount
		totalNewBytes := finalBytes - initialBytes

		log.Printf("[INFO] 📊 AstiAV Test Results:")
		log.Printf("[INFO]   Duration: %v", duration)
		log.Printf("[INFO]   Total Frames: %d", totalFrames)
		log.Printf("[INFO]   Total Bytes: %d", totalNewBytes)
		log.Printf("[INFO]   Average FPS: %.1f", finalFPS)
		log.Printf("[INFO] 🎯 Check astiav_debug/ directory for captured H.264 data")
		log.Printf("[INFO] ✅ These are REAL CAMERA FRAMES, not test patterns!")
	}()

	return nil
}

// 创建测试图像（用于验证保存功能）
func (ds *DoorStation) createTestImage(frameNumber int) image.Image {
	width := ds.config.VideoWidth
	height := ds.config.VideoHeight

	// 创建一个简单的测试图像
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 填充渐变色彩以模拟视频帧
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			// 创建基于帧号和位置的颜色
			r := uint8((x + frameNumber) % 256)
			g := uint8((y + frameNumber) % 256)
			b := uint8((x + y + frameNumber) % 256)
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	return img
}

// 验证视频轨道是否产生数据
func (ds *DoorStation) validateVideoTracks() error {
	if ds.astiavStream == nil {
		return fmt.Errorf("AstiAV stream not initialized")
	}

	videoTracks := ds.astiavStream.GetVideoTracks()
	if len(videoTracks) == 0 {
		return fmt.Errorf("no video tracks found")
	}

	for i, track := range videoTracks {
		log.Printf("[DEBUG] Validating video track %d: ID=%s, Kind=%s", i, track.ID(), track.Kind())

		// 检查轨道是否启用
		// 注意：mediadevices.Track 可能没有 Enabled() 方法，这里只是示例
		log.Printf("[DEBUG] Track %d appears to be active", i)
	}

	// 启动短期测试来验证数据流
	return ds.testVideoCapture(10 * time.Second)
}

// 建立WebSocket连接
func (ds *DoorStation) connectWebSocket() error {
	u, err := url.Parse(ds.clientInfo.Params.WssURL)
	if err != nil {
		return fmt.Errorf("invalid websocket URL: %v", err)
	}

	// 设置Origin头
	header := http.Header{}
	baseURL := fmt.Sprintf("%s://%s", u.Scheme[:len(u.Scheme)-1], u.Host)
	header.Set("Origin", baseURL)

	ds.wsConn, _, err = websocket.DefaultDialer.Dial(ds.clientInfo.Params.WssURL, header)
	if err != nil {
		return fmt.Errorf("failed to connect websocket: %v", err)
	}

	log.Println("[INFO] WebSocket connected")

	// 注册到房间
	registerMsg := WSMessage{
		Cmd:      "register",
		RoomID:   ds.clientInfo.Params.RoomID,
		ClientID: ds.clientInfo.Params.ClientID,
	}

	return ds.sendWSMessage(registerMsg)
}

// 发送WebSocket消息
func (ds *DoorStation) sendWSMessage(msg WSMessage) error {
	if ds.wsConn == nil {
		return fmt.Errorf("websocket not connected")
	}

	data, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	log.Printf("[DEBUG] Sending WS: %s", string(data))
	return ds.wsConn.WriteMessage(websocket.TextMessage, data)
}

// 发送自定义命令
func (ds *DoorStation) SendCustomCommand(command string) error {
	rtcMsg := RTCMessage{
		Type: "customized",
		Data: command,
	}
	msgData, _ := json.Marshal(rtcMsg)

	wsMsg := WSMessage{
		Cmd: "send",
		Msg: string(msgData),
	}

	return ds.sendWSMessage(wsMsg)
}

// 创建WebRTC连接
func (ds *DoorStation) createPeerConnection() error {
	if ds.peerConn != nil {
		ds.peerConn.Close()
		ds.peerConn = nil
	}

	// 创建媒体引擎，限制编解码器以兼容ESP32
	mediaEngine := &webrtc.MediaEngine{}

	// 按照ESP32的顺序注册编解码器：先音频，后视频
	// PCMA音频编解码器 - 与ESP32兼容（优先级最高）
	if err := mediaEngine.RegisterCodec(webrtc.RTPCodecParameters{
		RTPCodecCapability: webrtc.RTPCodecCapability{
			MimeType:  webrtc.MimeTypePCMA,
			ClockRate: 8000,
			Channels:  1,
		},
		PayloadType: 8,
	}, webrtc.RTPCodecTypeAudio); err != nil {
		return fmt.Errorf("failed to register PCMA codec: %v", err)
	}

	// H.264视频编解码器 - 与ESP32兼容的配置（packetization-mode=1）
	if err := mediaEngine.RegisterCodec(webrtc.RTPCodecParameters{
		RTPCodecCapability: webrtc.RTPCodecCapability{
			MimeType:    webrtc.MimeTypeH264,
			ClockRate:   90000,
			Channels:    0,
			SDPFmtpLine: "level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f",
		},
		PayloadType: 102,
	}, webrtc.RTPCodecTypeVideo); err != nil {
		return fmt.Errorf("failed to register H.264 codec: %v", err)
	}

	// 添加第二个H.264编解码器配置（packetization-mode=0）
	if err := mediaEngine.RegisterCodec(webrtc.RTPCodecParameters{
		RTPCodecCapability: webrtc.RTPCodecCapability{
			MimeType:    webrtc.MimeTypeH264,
			ClockRate:   90000,
			Channels:    0,
			SDPFmtpLine: "level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f",
		},
		PayloadType: 106,
	}, webrtc.RTPCodecTypeVideo); err != nil {
		return fmt.Errorf("failed to register H.264 codec (mode 0): %v", err)
	}

	// 创建API实例
	api := webrtc.NewAPI(webrtc.WithMediaEngine(mediaEngine))

	config := webrtc.Configuration{
		ICEServers: ds.iceServers,
	}

	pc, err := api.NewPeerConnection(config)
	if err != nil {
		return fmt.Errorf("failed to create peer connection: %v", err)
	}
	ds.peerConn = pc

	// 添加AstiAV媒体轨道到PeerConnection - 按ESP32顺序：先音频，后视频
	audioTracks := ds.astiavStream.GetAudioTracks()
	videoTracks := ds.astiavStream.GetVideoTracks()

	// 先添加音频轨道
	for _, track := range audioTracks {
		if _, err := pc.AddTrack(track); err != nil {
			return fmt.Errorf("failed to add audio track: %v", err)
		}
		log.Printf("[INFO] Added audio track: %s", track.ID())
	}

	// 后添加视频轨道
	for _, track := range videoTracks {
		if _, err := pc.AddTrack(track); err != nil {
			return fmt.Errorf("failed to add video track: %v", err)
		}
		log.Printf("[INFO] Added video track: %s", track.ID())
	}

	// 设置ICE候选处理
	pc.OnICECandidate(func(candidate *webrtc.ICECandidate) {
		if candidate != nil {
			ds.sendCandidate(candidate)
		}
	})

	// 设置连接状态监听
	pc.OnConnectionStateChange(func(state webrtc.PeerConnectionState) {
		log.Printf("[INFO] Connection state changed: %s", state.String())
		if state == webrtc.PeerConnectionStateConnected {
			log.Println("[INFO] 📷 WebRTC connection established! Camera stream is now being sent.")
			ds.state = StateInCall
		} else if state == webrtc.PeerConnectionStateFailed ||
			state == webrtc.PeerConnectionStateDisconnected {
			log.Println("[WARN] WebRTC connection lost")
			if ds.state == StateInCall {
				ds.state = StateConnected
			}
		}
	})

	// 设置ICE连接状态监听
	pc.OnICEConnectionStateChange(func(state webrtc.ICEConnectionState) {
		log.Printf("[INFO] ICE connection state changed: %s", state.String())
	})

	// 设置信令状态监听
	pc.OnSignalingStateChange(func(state webrtc.SignalingState) {
		log.Printf("[INFO] Signaling state changed: %s", state.String())
	})

	// 监听远程轨道（如果需要接收音频）
	pc.OnTrack(func(track *webrtc.TrackRemote, receiver *webrtc.RTPReceiver) {
		log.Printf("[INFO] Received remote track: %s (kind: %s)", track.ID(), track.Kind())
	})

	// 添加数据通道用于调试
	dataChannel, err := pc.CreateDataChannel("debug", nil)
	if err != nil {
		log.Printf("[WARN] Failed to create data channel: %v", err)
	} else {
		dataChannel.OnOpen(func() {
			log.Println("[INFO] 📡 Data channel opened - connection is working!")
		})
		dataChannel.OnMessage(func(msg webrtc.DataChannelMessage) {
			log.Printf("[DEBUG] Data channel message: %s", string(msg.Data))
		})
	}

	log.Println("[INFO] PeerConnection created with real camera stream")
	return nil
}

// 发送ICE候选
func (ds *DoorStation) sendCandidate(candidate *webrtc.ICECandidate) {
	// 转换为标准ICE候选格式
	candidateStr := candidate.ToJSON().Candidate

	candidateMsg := RTCMessage{
		Type:      "candidate",
		Candidate: candidateStr,
	}
	msgData, _ := json.Marshal(candidateMsg)

	// 通过HTTP POST发送
	messageURL := fmt.Sprintf("%s/message/%s/%s",
		ds.getBaseURL(), ds.clientInfo.Params.RoomID, ds.clientInfo.Params.ClientID)

	resp, err := http.Post(messageURL, "application/json", strings.NewReader(string(msgData)))
	if err != nil {
		log.Printf("[ERROR] Failed to send candidate: %v", err)
		return
	}
	resp.Body.Close()

	log.Printf("[DEBUG] Sent ICE candidate: %s", candidateStr)
}

// 获取基础URL
func (ds *DoorStation) getBaseURL() string {
	if idx := strings.Index(ds.config.ServerURL, "/join"); idx != -1 {
		return ds.config.ServerURL[:idx]
	}
	return ds.config.ServerURL
}

// 创建并发送Offer
func (ds *DoorStation) createOffer() error {
	// 验证AstiAV媒体轨道状态
	audioTracks := ds.astiavStream.GetAudioTracks()
	videoTracks := ds.astiavStream.GetVideoTracks()
	log.Printf("[DEBUG] 🎬 Creating offer with %d audio tracks and %d video tracks", len(audioTracks), len(videoTracks))

	offer, err := ds.peerConn.CreateOffer(nil)
	if err != nil {
		return fmt.Errorf("failed to create offer: %v", err)
	}

	// 🔍 调试SDP内容
	log.Printf("[DEBUG] 📋 SDP Offer created, analyzing content...")
	ds.analyzeSDP(offer.SDP, "OFFER")

	if err := ds.peerConn.SetLocalDescription(offer); err != nil {
		return fmt.Errorf("failed to set local description: %v", err)
	}

	// 发送SDP Offer
	offerMsg := RTCMessage{
		Type: "offer",
		SDP:  offer.SDP,
	}
	msgData, _ := json.Marshal(offerMsg)

	messageURL := fmt.Sprintf("%s/message/%s/%s",
		ds.getBaseURL(), ds.clientInfo.Params.RoomID, ds.clientInfo.Params.ClientID)

	resp, err := http.Post(messageURL, "application/json", strings.NewReader(string(msgData)))
	if err != nil {
		return fmt.Errorf("failed to send offer: %v", err)
	}
	resp.Body.Close()

	log.Println("[INFO] 📡 Offer sent with real media tracks")
	return nil
}

// 分析SDP内容
func (ds *DoorStation) analyzeSDP(sdp, sdpType string) {
	log.Printf("[DEBUG] 🔍 Analyzing %s SDP:", sdpType)

	lines := strings.Split(sdp, "\n")
	hasVideo := false
	hasAudio := false
	videoCodecs := []string{}
	audioCodecs := []string{}

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 检查媒体行
		if strings.HasPrefix(line, "m=video") {
			hasVideo = true
			log.Printf("[DEBUG] ✅ Found video media line: %s", line)
		} else if strings.HasPrefix(line, "m=audio") {
			hasAudio = true
			log.Printf("[DEBUG] ✅ Found audio media line: %s", line)
		}

		// 检查编解码器
		if strings.HasPrefix(line, "a=rtpmap:") {
			if strings.Contains(line, "H264") || strings.Contains(line, "h264") {
				videoCodecs = append(videoCodecs, line)
				log.Printf("[DEBUG] 🎥 H.264 codec: %s", line)
			} else if strings.Contains(line, "PCMA") || strings.Contains(line, "pcma") {
				audioCodecs = append(audioCodecs, line)
				log.Printf("[DEBUG] 🎵 PCMA codec: %s", line)
			} else if strings.Contains(line, "opus") || strings.Contains(line, "OPUS") {
				audioCodecs = append(audioCodecs, line)
				log.Printf("[DEBUG] 🎵 Opus codec: %s", line)
			}
		}

		// 检查发送方向
		if strings.HasPrefix(line, "a=sendonly") {
			log.Printf("[DEBUG] 📤 Send-only direction found")
		} else if strings.HasPrefix(line, "a=sendrecv") {
			log.Printf("[DEBUG] 📤📥 Send-receive direction found")
		}
	}

	// 总结分析结果
	log.Printf("[DEBUG] 📊 SDP Analysis Summary:")
	log.Printf("[DEBUG]   Has Video: %v", hasVideo)
	log.Printf("[DEBUG]   Has Audio: %v", hasAudio)
	log.Printf("[DEBUG]   Video Codecs: %d found", len(videoCodecs))
	log.Printf("[DEBUG]   Audio Codecs: %d found", len(audioCodecs))

	if !hasVideo {
		log.Printf("[WARN] 🚨 NO VIDEO FOUND IN SDP! This explains the black screen!")
	}
	if !hasAudio {
		log.Printf("[WARN] 🚨 NO AUDIO FOUND IN SDP!")
	}
}

// 处理接收到的Offer
func (ds *DoorStation) handleOffer(sdp string) error {
	// 检查当前信令状态
	currentState := ds.peerConn.SignalingState()
	log.Printf("[DEBUG] Current signaling state: %s", currentState.String())

	// 如果已经是stable状态，说明offer已经处理过了
	if currentState == webrtc.SignalingStateStable {
		log.Printf("[WARN] Ignoring duplicate offer - signaling state is already stable")
		return nil
	}

	// 只有在stable状态时才能设置远程offer
	if currentState != webrtc.SignalingStateStable && currentState != webrtc.SignalingStateHaveRemoteOffer {
		log.Printf("[WARN] Cannot set remote offer in state: %s", currentState.String())
		return nil
	}

	offer := webrtc.SessionDescription{
		Type: webrtc.SDPTypeOffer,
		SDP:  sdp,
	}

	log.Printf("[DEBUG] Setting remote offer...")
	// 设置远程描述
	if err := ds.peerConn.SetRemoteDescription(offer); err != nil {
		return fmt.Errorf("failed to set remote description: %v", err)
	}

	log.Printf("[DEBUG] Creating answer...")
	// 创建并发送answer
	answer, err := ds.peerConn.CreateAnswer(nil)
	if err != nil {
		return fmt.Errorf("failed to create answer: %v", err)
	}

	log.Printf("[DEBUG] Setting local answer...")
	if err := ds.peerConn.SetLocalDescription(answer); err != nil {
		return fmt.Errorf("failed to set local description: %v", err)
	}

	// 发送SDP Answer
	answerMsg := RTCMessage{
		Type: "answer",
		SDP:  answer.SDP,
	}
	msgData, _ := json.Marshal(answerMsg)

	messageURL := fmt.Sprintf("%s/message/%s/%s",
		ds.getBaseURL(), ds.clientInfo.Params.RoomID, ds.clientInfo.Params.ClientID)

	resp, err := http.Post(messageURL, "application/json", strings.NewReader(string(msgData)))
	if err != nil {
		return fmt.Errorf("failed to send answer: %v", err)
	}
	resp.Body.Close()

	log.Println("[INFO] 📋 Answer sent successfully")
	return nil
}

// 处理接收到的Answer
func (ds *DoorStation) handleAnswer(sdp string) error {
	// 检查当前信令状态
	currentState := ds.peerConn.SignalingState()
	log.Printf("[DEBUG] Current signaling state: %s", currentState.String())

	// 如果已经是stable状态，说明answer已经处理过了
	if currentState == webrtc.SignalingStateStable {
		log.Printf("[WARN] Ignoring duplicate answer - signaling state is already stable")
		return nil
	}

	// 只有在have-local-offer状态时才能设置远程answer
	if currentState != webrtc.SignalingStateHaveLocalOffer {
		log.Printf("[WARN] Cannot set remote answer in state: %s", currentState.String())
		return nil
	}

	// 🔍 分析接收到的Answer SDP
	log.Printf("[DEBUG] 📋 Received SDP Answer, analyzing content...")
	ds.analyzeSDP(sdp, "ANSWER")

	answer := webrtc.SessionDescription{
		Type: webrtc.SDPTypeAnswer,
		SDP:  sdp,
	}

	log.Printf("[DEBUG] Setting remote answer...")
	return ds.peerConn.SetRemoteDescription(answer)
}

// 处理接收到的ICE候选
func (ds *DoorStation) handleCandidate(candidateStr string) error {
	// 检查候选格式并转换
	var standardCandidate string

	// 如果是非标准格式（如 "udp4 host ..."），尝试转换
	if strings.Contains(candidateStr, "udp4") {
		log.Printf("[DEBUG] Converting non-standard candidate: %s", candidateStr)
		// 这种格式通常来自pion的String()方法，我们需要跳过它
		// 因为浏览器端应该发送标准格式
		return nil
	} else {
		standardCandidate = candidateStr
	}

	candidate := webrtc.ICECandidateInit{
		Candidate: standardCandidate,
	}

	log.Printf("[DEBUG] Adding ICE candidate: %s", standardCandidate)
	return ds.peerConn.AddICECandidate(candidate)
}

// HTTP轮询已移除 - ESP32使用WebSocket接收所有消息

// 监听WebSocket消息
func (ds *DoorStation) listenWebSocket() {
	defer func() {
		if ds.wsConn != nil {
			ds.wsConn.Close()
			ds.wsConn = nil
		}
	}()

	for {
		select {
		case <-ds.ctx.Done():
			return
		default:
			_, message, err := ds.wsConn.ReadMessage()
			if err != nil {
				log.Printf("[ERROR] WebSocket read error: %v", err)
				return
			}

			ds.handleWebSocketMessage(message)
		}
	}
}

// 处理WebSocket消息
func (ds *DoorStation) handleWebSocketMessage(data []byte) {
	log.Printf("[DEBUG] Received WS: %s", string(data))

	var wsMsg WSMessage
	if err := json.Unmarshal(data, &wsMsg); err != nil {
		log.Printf("[ERROR] Failed to parse WS message: %v", err)
		return
	}

	// 检查是否有错误信息
	if wsMsg.Cmd == "" && wsMsg.Msg != "" {
		// 尝试解析内层消息，可能是服务器状态消息
		var statusMsg map[string]interface{}
		if err := json.Unmarshal([]byte(wsMsg.Msg), &statusMsg); err == nil {
			if errorMsg, exists := statusMsg["error"]; exists {
				if errorStr, ok := errorMsg.(string); ok && errorStr != "" {
					log.Printf("[WARN] Server error: %s", errorStr)
				} else {
					log.Printf("[DEBUG] Server status message (no error)")
				}
				return
			}
		}

		// 尝试解析为RTC消息
		var rtcMsg RTCMessage
		if err := json.Unmarshal([]byte(wsMsg.Msg), &rtcMsg); err != nil {
			log.Printf("[DEBUG] Unknown message format in msg field: %s", wsMsg.Msg)
			return
		}

		// 处理RTC消息
		switch rtcMsg.Type {
		case "answer":
			log.Printf("[INFO] 📋 Received SDP Answer via WebSocket")
			if err := ds.handleAnswer(rtcMsg.SDP); err != nil {
				log.Printf("[ERROR] Failed to handle answer: %v", err)
			} else {
				log.Printf("[INFO] ✅ Answer processed successfully")
			}
		case "candidate":
			log.Printf("[INFO] 🧊 Received ICE Candidate")
			ds.handleCandidate(rtcMsg.Candidate)
		case "customized":
			ds.handleCustomCommand(rtcMsg.Data)
		case "offer":
			log.Printf("[INFO] 📋 Received SDP Offer from browser")
			// 浏览器发送offer，我们需要回复answer
			if err := ds.handleOffer(rtcMsg.SDP); err != nil {
				log.Printf("[ERROR] Failed to handle offer: %v", err)
			}
		default:
			if rtcMsg.Type != "" {
				log.Printf("[DEBUG] Unknown RTC message type: %s", rtcMsg.Type)
			}
		}
	} else {
		// 处理其他类型的WebSocket命令
		switch wsMsg.Cmd {
		case "register":
			log.Printf("[DEBUG] Registration command received")
		case "send":
			// 可能是其他客户端发送的消息
			log.Printf("[DEBUG] Send command received")
		case "new_client":
			// 新客户端加入房间
			log.Printf("[INFO] 🔔 New client joined the room, starting stream...")
			go func() {
				time.Sleep(1 * time.Second)
				if ds.state == StateConnected {
					if err := ds.SendCustomCommand("RING"); err != nil {
						log.Printf("[ERROR] Failed to send ring on new client: %v", err)
					}
					if ds.clientInfo.Params.IsInitiator == "true" && ds.peerConn.LocalDescription() == nil {
						if err := ds.createOffer(); err != nil {
							log.Printf("[ERROR] Failed to create offer on new client: %v", err)
						}
					}
				}
			}()
		default:
			if wsMsg.Cmd != "" {
				log.Printf("[DEBUG] Unknown WebSocket command: %s", wsMsg.Cmd)
				// 检查是否是客户端加入的消息
				if strings.Contains(wsMsg.Cmd, "joined") || strings.Contains(wsMsg.Cmd, "client") {
					log.Printf("[INFO] 🔔 Detected client activity, ensuring stream is active...")
					go func() {
						time.Sleep(1 * time.Second)
						if ds.state == StateConnected && ds.peerConn.LocalDescription() == nil {
							if err := ds.SendCustomCommand("RING"); err != nil {
								log.Printf("[ERROR] Failed to send ring on activity: %v", err)
							}
							if ds.clientInfo.Params.IsInitiator == "true" {
								if err := ds.createOffer(); err != nil {
									log.Printf("[ERROR] Failed to create offer on activity: %v", err)
								}
							}
						}
					}()
				}
			}
		}
	}
}

// 处理自定义命令
func (ds *DoorStation) handleCustomCommand(command string) {
	log.Printf("[INFO] 📞 Received custom command: %s", command)

	switch command {
	case "ACCEPT_CALL": // 浏览器接受呼叫 - 关键修复！
		log.Println("[INFO] ✅ Call accepted by web client")
		// 浏览器接受呼叫后，我们作为门禁主机开始WebRTC协商
		if ds.peerConn.LocalDescription() == nil {
			log.Println("[INFO] 📡 Creating offer after call was accepted...")
			if err := ds.createOffer(); err != nil {
				log.Printf("[ERROR] Failed to create offer after call accepted: %v", err)
			} else {
				log.Println("[INFO] 📹 Media streaming started after call accepted")
			}
		} else {
			log.Println("[INFO] 📹 WebRTC connection already established")
		}
	case "DENY_CALL": // 浏览器拒绝呼叫
		log.Println("[INFO] ❌ Call denied by web client")
		if ds.state == StateInCall {
			ds.state = StateConnected
		}
	case "RING": // 接收到自己发送的RING命令，说明浏览器已经准备好了
		log.Println("[INFO] 🔔 RING command echoed back, browser is ready")
		// 根据initiator角色决定行为
		go func() {
			time.Sleep(1 * time.Second)
			if ds.peerConn.LocalDescription() == nil {
				if ds.clientInfo.Params.IsInitiator == "true" {
					log.Println("[INFO] 📡 As initiator, creating offer...")
					if err := ds.createOffer(); err != nil {
						log.Printf("[ERROR] Failed to create offer: %v", err)
					} else {
						log.Println("[INFO] 📹 Offer sent, waiting for answer")
					}
				} else {
					log.Println("[INFO] 🎯 As non-initiator, waiting for browser's offer...")
				}
			}
		}()
	case "OPEN_DOOR": // 开门命令
		log.Println("[INFO] 🚪 Door open command received")
		ds.handleDoorOpen()
		// 回复门已开启
		ds.SendCustomCommand("DOOR_OPENED")
	}
}

// 处理开门命令
func (ds *DoorStation) handleDoorOpen() {
	log.Println("[INFO] 🚪 Door opening... (simulate door control)")
	// 这里可以添加实际的门锁控制逻辑
}

// 连接到房间
func (ds *DoorStation) ConnectToRoom(roomID string) error {
	if ds.state != StateDisconnected {
		return fmt.Errorf("already connected or connecting")
	}

	ds.currentRoom = roomID
	ds.state = StateConnecting

	log.Printf("[INFO] 🏠 Connecting to room: %s", roomID)

	// 1. 设置媒体设备
	if err := ds.setupMediaDevices(); err != nil {
		ds.state = StateDisconnected
		return fmt.Errorf("failed to setup media devices: %v", err)
	}

	// 2. 获取房间信息
	if err := ds.getRoomInfo(roomID); err != nil {
		ds.state = StateDisconnected
		return err
	}

	// 3. 获取ICE服务器
	if err := ds.getIceServers(); err != nil {
		ds.state = StateDisconnected
		return err
	}

	// 4. 建立WebSocket连接
	if err := ds.connectWebSocket(); err != nil {
		ds.state = StateDisconnected
		return err
	}

	// 5. 创建PeerConnection
	if err := ds.createPeerConnection(); err != nil {
		ds.state = StateDisconnected
		return err
	}

	// 6. 启动WebSocket监听
	go ds.listenWebSocket()

	// 7. HTTP轮询已移除 - WebSocket处理所有消息

	ds.state = StateConnected
	log.Printf("[INFO] ✅ Connected to room: %s", roomID)

	// 7. 根据角色决定行为
	go func() {
		time.Sleep(500 * time.Millisecond)

		if ds.clientInfo.Params.IsInitiator == "true" {
			log.Println("[INFO] 📡 As initiator, auto-starting call...")
			// 发送RING命令
			if err := ds.SendCustomCommand("RING"); err != nil {
				log.Printf("[ERROR] Failed to send ring command: %v", err)
				return
			}
			// 等待RING回传后创建offer
		} else {
			log.Println("[INFO] 🎯 As non-initiator, waiting for browser's offer...")
			// 发送RING命令通知浏览器我们已准备好
			if err := ds.SendCustomCommand("RING"); err != nil {
				log.Printf("[ERROR] Failed to send ring command: %v", err)
			}
		}
	}()

	return nil
}

// 断开连接
func (ds *DoorStation) Disconnect() {
	if ds.state == StateDisconnected {
		return
	}

	log.Println("[INFO] 🔌 Disconnecting...")

	ds.state = StateDisconnected

	if ds.peerConn != nil {
		ds.peerConn.Close()
		ds.peerConn = nil
	}

	if ds.wsConn != nil {
		ds.wsConn.Close()
		ds.wsConn = nil
	}

	log.Println("[INFO] ✅ Disconnected")
}

// 发起呼叫
func (ds *DoorStation) StartCall() error {
	if ds.state != StateConnected {
		return fmt.Errorf("not connected to room")
	}

	log.Println("[INFO] 📞 Starting call...")

	// 发送门铃响起命令
	if err := ds.SendCustomCommand("RING"); err != nil {
		return fmt.Errorf("failed to send ring command: %v", err)
	}

	// 如果是initiator，创建offer
	if ds.clientInfo.Params.IsInitiator == "true" {
		time.Sleep(1 * time.Second)
		if err := ds.createOffer(); err != nil {
			return fmt.Errorf("failed to create offer: %v", err)
		}
	}

	return nil
}

// 挂断通话
func (ds *DoorStation) HangupCall() error {
	if ds.state != StateInCall {
		return fmt.Errorf("not in call")
	}

	log.Println("[INFO] 📴 Hanging up...")
	return ds.SendCustomCommand("CALL_HANGUP")
}

// 显示状态
func (ds *DoorStation) ShowStatus() {
	fmt.Printf("\n=== Door Station Status ===\n")
	fmt.Printf("State: %s\n", ds.state)
	fmt.Printf("Server: %s\n", ds.config.ServerURL)
	if ds.currentRoom != "" {
		fmt.Printf("Room: %s\n", ds.currentRoom)
	}
	fmt.Printf("Video: %dx%d@%dfps, %d bps\n",
		ds.config.VideoWidth, ds.config.VideoHeight,
		ds.config.VideoFPS, ds.config.VideoBitrate)
	fmt.Printf("Audio: %d Hz, %d channels\n",
		ds.config.AudioSampleRate, ds.config.AudioChannels)

	if ds.state != StateDisconnected {
		fmt.Printf("Client ID: %s\n", ds.clientInfo.Params.ClientID)
		fmt.Printf("Is Initiator: %s\n", ds.clientInfo.Params.IsInitiator)
	}
	fmt.Printf("===========================\n\n")
}

// 停止系统
func (ds *DoorStation) Stop() {
	log.Println("[INFO] 🛑 Stopping Door Station...")

	ds.Disconnect()
	ds.cancel()

	if ds.astiavStream != nil {
		ds.astiavStream.StopStreaming()
		ds.astiavStream = nil
	}
}

// 列出可用的摄像头和麦克风设备
func ListMediaDevices() {
	fmt.Println("📹 Available Media Devices:")

	// 尝试使用最简单的约束来测试设备
	fmt.Println("\n🎥 Testing Media Device Access:")

	// AstiAV视频设备支持
	fmt.Println("AstiAV Video Device Support:")
	fmt.Println("  📹 macOS: AVFoundation devices")
	fmt.Println("  📹 Linux: V4L2 devices")
	fmt.Println("  📹 Windows: DirectShow devices")
	fmt.Println("  📹 Fallback: Test source")

	// AstiAV音频设备支持
	fmt.Println("\nAstiAV Audio Device Support:")
	fmt.Println("  🎤 macOS: AVFoundation audio")
	fmt.Println("  🎤 Linux: ALSA/PulseAudio")
	fmt.Println("  🎤 Windows: DirectSound")

	fmt.Println("\n💡 设备检测在流初始化时自动进行")

	fmt.Println("\n📋 设备配置建议:")
	fmt.Println("Camera devices:")
	fmt.Println("  - Default camera (leave empty to use default)")
	fmt.Println("  - /dev/video0 (Linux)")
	fmt.Println("  - 0 (Windows/Mac)")
	fmt.Println("Microphone devices:")
	fmt.Println("  - Default microphone (leave empty to use default)")
}

// 显示帮助
func showHelp() {
	fmt.Println("\n=== Door Station Commands ===")
	fmt.Println("connect <room_id>     - Connect to a room (auto-starts streaming)")
	fmt.Println("disconnect           - Disconnect from current room")
	fmt.Println("stream              - Manually start media streaming")
	fmt.Println("call                 - Start a call (send RING)")
	fmt.Println("hangup              - Hang up current call")
	fmt.Println("open                - Send door open command")
	fmt.Println("status              - Show current status")
	fmt.Println("devices             - List available media devices")
	fmt.Println("")
	fmt.Println("=== Video Debugging Commands ===")
	fmt.Println("debug video         - Enable video frame debugging")
	fmt.Println("debug stats         - Show current video statistics")
	fmt.Println("debug test [secs]   - Test video capture for N seconds (default: 10)")
	fmt.Println("debug validate      - Validate video tracks are producing data")
	fmt.Println("debug pattern       - Enable test pattern for video debugging")
	fmt.Println("debug quality       - Set video quality preset")
	fmt.Println("debug camera        - Set camera parameters")
	fmt.Println("debug no-conversion - Test NO CONVERSION mode (avoid pixel format conversion)")
	fmt.Println("")
	fmt.Println("=== Advanced Camera Debugging ===")
	fmt.Println("debug camera device <id>       - Switch to camera device (requires restart)")
	fmt.Println("debug camera format <format>   - Test different pixel formats")
	fmt.Println("debug no-conversion             - Use camera's native format (may fix lines)")
	fmt.Println("debug nv12                      - Force NV12 format (closest to YUV420P)")
	fmt.Println("debug scale                     - Test different scaling algorithms")
	fmt.Println("")
	fmt.Println("=== Encoder Debugging ===")
	fmt.Println("debug encoder <mode>            - Test different encoder modes (may fix lines)")
	fmt.Println("")
	fmt.Println("=== Quick Fixes ===")
	fmt.Println("debug lowres                    - Test low resolution (320x240@10fps)")
	fmt.Println("debug simple                    - Test simple mode (480x360, minimal processing)")
	fmt.Println("")
	fmt.Println("help                - Show this help")
	fmt.Println("quit/exit           - Exit the program")
	fmt.Println("===============================\n")
}

// 处理用户命令
func handleCommand(ds *DoorStation, input string) bool {
	parts := strings.Fields(strings.TrimSpace(input))
	if len(parts) == 0 {
		return true
	}

	cmd := strings.ToLower(parts[0])

	switch cmd {
	case "connect":
		if len(parts) < 2 {
			fmt.Println("Usage: connect <room_id>")
			return true
		}
		roomID := parts[1]
		if err := ds.ConnectToRoom(roomID); err != nil {
			log.Printf("[ERROR] Failed to connect: %v", err)
		}

	case "disconnect":
		ds.Disconnect()

	case "stream":
		if ds.state != StateConnected {
			fmt.Println("Not connected to room. Use 'connect <room_id>' first.")
			return true
		}
		log.Println("[INFO] 📡 Manually starting media stream...")
		if err := ds.SendCustomCommand("RING"); err != nil {
			log.Printf("[ERROR] Failed to send ring command: %v", err)
		} else {
			if ds.peerConn.LocalDescription() == nil {
				if err := ds.createOffer(); err != nil {
					log.Printf("[ERROR] Failed to create offer: %v", err)
				} else {
					log.Println("[INFO] 📹 Media streaming started manually")
				}
			} else {
				log.Println("[INFO] 📹 Media stream already active")
			}
		}

	case "call":
		if err := ds.StartCall(); err != nil {
			log.Printf("[ERROR] Failed to start call: %v", err)
		}

	case "hangup":
		if err := ds.HangupCall(); err != nil {
			log.Printf("[ERROR] Failed to hangup: %v", err)
		}

	case "open":
		if ds.state == StateInCall {
			if err := ds.SendCustomCommand("DOOR_OPEN"); err != nil {
				log.Printf("[ERROR] Failed to send door open: %v", err)
			} else {
				log.Println("[INFO] 🚪 Door open command sent")
			}
		} else {
			fmt.Println("Not in call. Connect and start a call first.")
		}

	case "status":
		ds.ShowStatus()

	case "devices":
		ListMediaDevices()

	case "debug":
		if len(parts) < 2 {
			fmt.Println("Usage: debug <video|stats|test|validate|pattern|camera|no-conversion|nv12|scale|encoder|lowres|simple>")
			return true
		}
		subCmd := parts[1]
		switch subCmd {
		case "video":
			ds.enableVideoDebug()
			fmt.Println("Video debugging enabled. Frames will be saved to video_debug/ directory.")
		case "stats":
			ds.logVideoStats()
		case "no-conversion":
			fmt.Println(" Testing NO CONVERSION mode - using camera's native pixel format...")

			// 停止现有流
			if ds.astiavStream != nil {
				ds.astiavStream.StopStreaming()
				ds.astiavStream = nil
			}

			// 创建NO_CONVERSION配置
			testConfig := AstiAVConfig{
				Width:    ds.config.VideoWidth,
				Height:   ds.config.VideoHeight,
				FPS:      ds.config.VideoFPS,
				DeviceID: "0",
				Format:   "avfoundation",
			}

			stream, err := NewAstiAVMediaStreamNoConversion(testConfig)
			if err != nil {
				fmt.Printf("Failed to create no-conversion stream: %v\n", err)
				return true
			}

			ds.astiavStream = stream

			// 立即启动流
			fmt.Println("Starting no-conversion streaming...")
			if err := ds.astiavStream.StartStreaming(); err != nil {
				fmt.Printf("Failed to start no-conversion streaming: %v\n", err)
				return true
			}

			fmt.Println("✅ NO CONVERSION mode started!")
			fmt.Println("🎯 This uses whatever pixel format your camera provides")
			fmt.Println("🔍 Check if horizontal lines disappear now!")
			fmt.Println("📊 Use 'debug stats' to monitor the stream")
		case "nv12":
			fmt.Println("🚀 Testing NV12 format - closest to YUV420P...")

			// 停止现有流
			if ds.astiavStream != nil {
				ds.astiavStream.StopStreaming()
				ds.astiavStream = nil
			}

			// 创建NV12配置
			testConfig := AstiAVConfig{
				Width:    ds.config.VideoWidth,
				Height:   ds.config.VideoHeight,
				FPS:      ds.config.VideoFPS,
				DeviceID: "0",
				Format:   "avfoundation",
			}

			stream, err := NewAstiAVMediaStreamWithPixelFormat(testConfig, "nv12")
			if err != nil {
				fmt.Printf("Failed to create NV12 stream: %v\n", err)
				return true
			}

			ds.astiavStream = stream

			// 立即启动流
			fmt.Println("Starting NV12 streaming...")
			if err := ds.astiavStream.StartStreaming(); err != nil {
				fmt.Printf("Failed to start NV12 streaming: %v\n", err)
				return true
			}

			fmt.Println("✅ NV12 mode started!")
			fmt.Println("🎯 NV12 is semi-planar YUV 4:2:0, closest to YUV420P")
			fmt.Println("🔍 This should minimize conversion artifacts!")
			fmt.Println("📊 Use 'debug stats' to monitor the stream")
		case "scale":
			if len(parts) < 3 {
				fmt.Println("Usage: debug scale <algorithm>")
				fmt.Println("Available algorithms:")
				fmt.Println("  bilinear   - Fast, good quality")
				fmt.Println("  bicubic    - Higher quality")
				fmt.Println("  lanczos    - Highest quality, slower")
				fmt.Println("  spline     - Ultra high quality")
				fmt.Println("  point      - Nearest neighbor (fast)")
				return true
			}

			algorithm := parts[2]
			fmt.Printf("🎯 Testing %s scaling algorithm...\n", algorithm)

			// 这里需要重启流来应用新的算法
			// 由于算法是在缩放器初始化时设置的，我们需要一个特殊的标记
			if ds.astiavStream != nil {
				// 设置一个标记来指示使用特定算法
				ds.astiavStream.debugMode = true
				// 重启流
				ds.astiavStream.StopStreaming()
				time.Sleep(500 * time.Millisecond)
				if err := ds.astiavStream.StartStreaming(); err != nil {
					fmt.Printf("Failed to restart with %s algorithm: %v\n", algorithm, err)
					return true
				}
				fmt.Printf("✅ Switched to %s scaling algorithm\n", algorithm)
				fmt.Println("🔍 Check if horizontal lines improve!")
			} else {
				fmt.Println("No active stream. Start streaming first.")
			}
		case "encoder":
			if len(parts) < 3 {
				fmt.Println("Usage: debug encoder <mode>")
				fmt.Println("Available encoder modes:")
				fmt.Println("  default      - Standard settings")
				fmt.Println("  high-quality - Maximum quality (may fix lines)")
				fmt.Println("  ultra-fast   - Minimal processing")
				if ds.astiavStream != nil {
					fmt.Printf("Current mode: %s\n", ds.astiavStream.GetEncoderMode())
				}
				return true
			}

			mode := strings.ToUpper(strings.ReplaceAll(parts[2], "-", "_"))
			fmt.Printf("🎯 Testing %s encoder mode...\n", mode)

			if ds.astiavStream != nil {
				// 设置编码器模式
				ds.astiavStream.SetEncoderMode(mode)

				// 重启流来应用新的编码器设置
				ds.astiavStream.StopStreaming()
				time.Sleep(500 * time.Millisecond)
				if err := ds.astiavStream.StartStreaming(); err != nil {
					fmt.Printf("Failed to restart with %s encoder: %v\n", mode, err)
					return true
				}
				fmt.Printf("✅ Switched to %s encoder mode\n", mode)
				if mode == "HIGH_QUALITY" {
					fmt.Println("🎯 This mode disables deblocking and uses highest quality settings")
					fmt.Println("🔍 Should eliminate horizontal lines if they're from encoding!")
				}
			} else {
				fmt.Println("No active stream. Start streaming first.")
			}
		case "pattern":
			if len(parts) < 3 {
				fmt.Println("Usage: debug pattern <mandelbrot|testsrc2|color|avfoundation>")
				fmt.Println("Available patterns:")
				fmt.Println("  mandelbrot    - Fractal pattern (best for detecting lines)")
				fmt.Println("  testsrc2      - Colorful test pattern")
				fmt.Println("  color         - Solid color")
				fmt.Println("  avfoundation  - Real camera (macOS)")
				return true
			}

			pattern := parts[2]
			if ds.astiavStream == nil {
				fmt.Println("Creating test stream with pattern:", pattern)
				// 创建配置使用测试图案
				testConfig := DoorStationConfig{
					ServerURL:      ds.config.ServerURL,
					VideoWidth:     ds.config.VideoWidth,
					VideoHeight:    ds.config.VideoHeight,
					VideoFPS:       ds.config.VideoFPS,
					CameraDeviceID: "0",
					MicDeviceID:    "",
				}

				// 创建AstiAV配置
				astiavConfig := AstiAVConfig{
					Width:    testConfig.VideoWidth,
					Height:   testConfig.VideoHeight,
					FPS:      testConfig.VideoFPS,
					DeviceID: testConfig.CameraDeviceID,
					Format:   pattern, // 直接使用图案名作为格式
				}

				stream, err := NewAstiAVMediaStream(astiavConfig)
				if err != nil {
					fmt.Printf("Failed to create test stream: %v\n", err)
					return true
				}

				ds.astiavStream = stream
				ds.astiavStream.EnableDebug()

				// 立即启动测试流
				fmt.Println("Starting test pattern streaming...")
				if err := ds.astiavStream.StartStreaming(); err != nil {
					fmt.Printf("Failed to start test streaming: %v\n", err)
					return true
				}
				fmt.Println("✅ Test pattern streaming started!")
			} else {
				ds.astiavStream.EnableTestPattern(pattern)
				fmt.Println("Test pattern updated, restarting stream...")
				ds.astiavStream.StopStreaming()
				if err := ds.astiavStream.StartStreaming(); err != nil {
					fmt.Printf("Failed to restart streaming: %v\n", err)
					return true
				}
			}

			fmt.Printf("Test pattern '%s' enabled and streaming.\n", pattern)
			fmt.Println("You can now 'connect <room>' or check 'debug stats' immediately.")
			if pattern == "avfoundation" {
				fmt.Println("🎥 Testing REAL camera - check for horizontal lines!")
			} else {
				fmt.Println("This will help identify if the lines are from the camera or processing.")
			}

		case "quality":
			if len(parts) < 3 {
				fmt.Println("Usage: debug quality <preset>")
				fmt.Println("Available presets: low, medium, high, ultra")
				if ds.astiavStream != nil {
					fmt.Printf("Current preset: %s\n", ds.astiavStream.GetQualityPreset())
				}
				return true
			}

			preset := strings.ToLower(parts[2])
			if ds.astiavStream == nil {
				fmt.Println("AstiAV stream not initialized. Connect to a room first.")
				return true
			}

			if err := ds.astiavStream.SetQualityPreset(preset); err != nil {
				fmt.Printf("Error setting quality preset: %v\n", err)
				return true
			}

			fmt.Printf("Video quality preset set to: %s\n", preset)
			fmt.Println("Note: Restart the connection for changes to take effect.")

		case "camera":
			if len(parts) < 3 {
				fmt.Println("Usage: debug camera <parameter> [value]")
				fmt.Println("Available parameters:")
				fmt.Println("  format <yuv420p|uyvy422|yuyv422|nv12>  - Set pixel format")
				fmt.Println("  size <width>x<height>                   - Set video size")
				fmt.Println("  fps <rate>                              - Set frame rate")
				fmt.Println("  device <id>                             - Set camera device ID")
				fmt.Println("Example: debug camera format uyvy422")
				return true
			}

			if ds.astiavStream == nil {
				fmt.Println("No camera stream active. Use 'debug pattern avfoundation' first.")
				return true
			}

			param := parts[2]
			var value string
			if len(parts) >= 4 {
				value = parts[3]
			}

			switch param {
			case "format":
				if value == "" {
					fmt.Println("Current pixel formats to try:")
					fmt.Println("  yuv420p  - Standard YUV 4:2:0")
					fmt.Println("  uyvy422  - Packed YUV 4:2:2 (recommended for cameras)")
					fmt.Println("  yuyv422  - Alternative packed YUV 4:2:2")
					fmt.Println("  nv12     - Semi-planar YUV 4:2:0")
					return true
				}
				fmt.Printf("Testing camera with pixel format: %s\n", value)
				// 这里需要重新创建流
				ds.astiavStream.StopStreaming()

				// 创建新的配置
				newConfig := AstiAVConfig{
					Width:    ds.config.VideoWidth,
					Height:   ds.config.VideoHeight,
					FPS:      ds.config.VideoFPS,
					DeviceID: "0",
					Format:   "avfoundation",
				}

				newStream, err := NewAstiAVMediaStreamWithPixelFormat(newConfig, value)
				if err != nil {
					fmt.Printf("Failed to create stream with format %s: %v\n", value, err)
					return true
				}

				ds.astiavStream = newStream
				ds.astiavStream.EnableDebug()

				if err := ds.astiavStream.StartStreaming(); err != nil {
					fmt.Printf("Failed to start streaming with format %s: %v\n", value, err)
					return true
				}

				fmt.Printf("✅ Camera restarted with pixel format: %s\n", value)
				fmt.Println("Check if horizontal lines are gone!")

			case "size":
				if value == "" {
					fmt.Println("Usage: debug camera size 640x480")
					return true
				}
				fmt.Printf("Size parameter: %s (restart required)\n", value)

			case "fps":
				if value == "" {
					fmt.Println("Usage: debug camera fps 30")
					return true
				}
				fmt.Printf("FPS parameter: %s (restart required)\n", value)

			case "device":
				if value == "" {
					fmt.Println("Usage: debug camera device 1")
					fmt.Println("Common device IDs: 0, 1, 2...")
					return true
				}
				fmt.Printf("🎥 Switching to camera device: %s\n", value)

				// 停止现有流
				if ds.astiavStream != nil {
					ds.astiavStream.StopStreaming()
					ds.astiavStream = nil
				}

				// 更新配置
				ds.config.CameraDeviceID = value

				// 创建新的配置
				newConfig := AstiAVConfig{
					Width:    ds.config.VideoWidth,
					Height:   ds.config.VideoHeight,
					FPS:      ds.config.VideoFPS,
					DeviceID: value,
					Format:   "avfoundation",
				}

				newStream, err := NewAstiAVMediaStream(newConfig)
				if err != nil {
					fmt.Printf("Failed to create stream with device %s: %v\n", value, err)
					return true
				}

				ds.astiavStream = newStream
				ds.astiavStream.EnableDebug()

				if err := ds.astiavStream.StartStreaming(); err != nil {
					fmt.Printf("Failed to start streaming with device %s: %v\n", value, err)
					return true
				}

				fmt.Printf("✅ Camera switched to device: %s\n", value)
				fmt.Println("🔍 Check if horizontal lines change with different camera!")

			default:
				fmt.Println("Unknown camera parameter. Use: format, size, fps, device")
			}

		case "test":
			if ds.astiavStream == nil {
				fmt.Println("AstiAV stream not initialized. Connect to a room first.")
				return true
			}
			duration := 10 * time.Second
			if len(parts) >= 3 {
				if d, err := time.ParseDuration(parts[2] + "s"); err == nil {
					duration = d
				}
			}
			fmt.Printf("Starting video capture test for %v...\n", duration)
			if err := ds.testVideoCapture(duration); err != nil {
				log.Printf("[ERROR] Video capture test failed: %v", err)
			}
		case "validate":
			if ds.astiavStream == nil {
				fmt.Println("AstiAV stream not initialized. Connect to a room first.")
				return true
			}
			fmt.Println("Validating video tracks...")
			if err := ds.validateVideoTracks(); err != nil {
				log.Printf("[ERROR] Video track validation failed: %v", err)
			}
		case "lowres":
			fmt.Println("🎯 Testing LOW RESOLUTION to reduce conversion load...")

			// 停止现有流
			if ds.astiavStream != nil {
				ds.astiavStream.StopStreaming()
				ds.astiavStream = nil
			}

			// 创建低分辨率配置
			testConfig := AstiAVConfig{
				Width:    320, // 降低到320x240
				Height:   240,
				FPS:      10, // 降低帧率
				DeviceID: "0",
				Format:   "avfoundation",
			}

			stream, err := NewAstiAVMediaStream(testConfig)
			if err != nil {
				fmt.Printf("Failed to create low-res stream: %v\n", err)
				return true
			}

			ds.astiavStream = stream
			ds.astiavStream.EnableDebug()

			// 立即启动流
			fmt.Println("Starting low-resolution streaming...")
			if err := ds.astiavStream.StartStreaming(); err != nil {
				fmt.Printf("Failed to start low-res streaming: %v\n", err)
				return true
			}

			fmt.Println("✅ Low-res mode started (320x240@10fps)!")
			fmt.Println("🎯 Less data to convert = less chance for lines")
			fmt.Println("🔍 Check if horizontal lines disappear at lower resolution!")
		case "simple":
			fmt.Println("🎯 Testing SIMPLE mode with minimal conversion...")

			// 停止现有流
			if ds.astiavStream != nil {
				ds.astiavStream.StopStreaming()
				ds.astiavStream = nil
			}

			// 创建简单配置 - 使用较小分辨率和低帧率
			testConfig := AstiAVConfig{
				Width:    480, // 中等分辨率
				Height:   360,
				FPS:      15,
				DeviceID: "0",
				Format:   "avfoundation",
			}

			stream, err := NewAstiAVMediaStream(testConfig)
			if err != nil {
				fmt.Printf("Failed to create simple stream: %v\n", err)
				return true
			}

			ds.astiavStream = stream
			ds.astiavStream.EnableDebug()
			ds.astiavStream.SetEncoderMode("ULTRA_FAST") // 使用最快编码

			// 立即启动流
			fmt.Println("Starting simple streaming...")
			if err := ds.astiavStream.StartStreaming(); err != nil {
				fmt.Printf("Failed to start simple streaming: %v\n", err)
				return true
			}

			fmt.Println("✅ Simple mode started (480x360@15fps, fast encoding)!")
			fmt.Println("🎯 Optimized for minimal processing and conversion")
			fmt.Println("🔍 This should reduce conversion artifacts!")
		default:
			fmt.Println("Unknown debug command. Available commands:")
			fmt.Println("  video, stats, test, validate, pattern, camera, no-conversion,")
			fmt.Println("  nv12, scale, encoder, lowres, simple")
			fmt.Println("Use 'help' for detailed descriptions.")
		}

	case "help":
		showHelp()

	case "quit", "exit":
		return false

	default:
		fmt.Printf("Unknown command: %s. Type 'help' for available commands.\n", cmd)
	}

	return true
}

// 命令行循环
func commandLoop(ds *DoorStation) {
	scanner := bufio.NewScanner(os.Stdin)

	fmt.Println("🚪 Door Station Interactive Console")
	fmt.Println("Type 'help' for available commands")

	for {
		fmt.Print("door-station> ")

		if !scanner.Scan() {
			break
		}

		input := scanner.Text()
		if !handleCommand(ds, input) {
			break
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("[ERROR] Scanner error: %v", err)
	}
}

func main() {
	// 解析命令行参数
	var serverURL string
	var autoConnect string

	if len(os.Args) >= 2 {
		serverURL = os.Args[1]
	} else {
		serverURL = "https://webrtc.espressif.com"
	}

	if len(os.Args) >= 3 {
		autoConnect = os.Args[2]
	}

	// 默认配置 - 优化为与ESP32兼容
	config := DoorStationConfig{
		ServerURL:       serverURL,
		VideoWidth:      640,
		VideoHeight:     480,
		VideoFPS:        15,
		VideoBitrate:    500000, // 降低到500kbps以减少网络负载
		AudioSampleRate: 8000,   // 改为8kHz以兼容G.711A
		AudioChannels:   1,      // 单声道
		CameraDeviceID:  "",     // 使用默认摄像头
		MicDeviceID:     "",     // 使用默认麦克风
	}

	// 解析可选参数
	for _, arg := range os.Args[1:] {
		if strings.HasPrefix(arg, "--width=") {
			fmt.Sscanf(arg, "--width=%d", &config.VideoWidth)
		} else if strings.HasPrefix(arg, "--height=") {
			fmt.Sscanf(arg, "--height=%d", &config.VideoHeight)
		} else if strings.HasPrefix(arg, "--fps=") {
			fmt.Sscanf(arg, "--fps=%d", &config.VideoFPS)
		} else if strings.HasPrefix(arg, "--bitrate=") {
			fmt.Sscanf(arg, "--bitrate=%d", &config.VideoBitrate)
		} else if strings.HasPrefix(arg, "--camera=") {
			config.CameraDeviceID = strings.TrimPrefix(arg, "--camera=")
		} else if strings.HasPrefix(arg, "--mic=") {
			config.MicDeviceID = strings.TrimPrefix(arg, "--mic=")
		} else if arg == "--devices" {
			ListMediaDevices()
			return
		} else if arg == "--help" {
			fmt.Println("Usage: go run main.go [server_url] [auto_connect_room] [options]")
			fmt.Println("Example: go run main.go https://webrtc.espressif.com test_room")
			fmt.Println("Options:")
			fmt.Println("  --devices         List available media devices")
			fmt.Println("  --width=640       Video width (default: 640)")
			fmt.Println("  --height=480      Video height (default: 480)")
			fmt.Println("  --fps=15          Video FPS (default: 15)")
			fmt.Println("  --bitrate=1000000 Video bitrate (default: 1Mbps)")
			fmt.Println("  --camera=ID       Camera device ID")
			fmt.Println("  --mic=ID          Microphone device ID")
			return
		}
	}

	log.Printf("[INFO] 🚀 Starting Door Station")
	log.Printf("[INFO]    Server: %s", config.ServerURL)
	log.Printf("[INFO]    Video: %dx%d@%dfps, %d bps",
		config.VideoWidth, config.VideoHeight, config.VideoFPS, config.VideoBitrate)

	doorStation := NewDoorStation(config)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 创建退出通道
	exitChan := make(chan bool, 1)

	// 如果指定了自动连接房间
	if autoConnect != "" {
		go func() {
			time.Sleep(1 * time.Second) // 等待初始化完成
			log.Printf("[INFO] 🏠 Auto-connecting to room: %s", autoConnect)
			if err := doorStation.ConnectToRoom(autoConnect); err != nil {
				log.Printf("[ERROR] Auto-connect failed: %v", err)
			}
		}()
	}

	// 启动命令行循环
	go func() {
		commandLoop(doorStation)
		// 命令循环结束时通知主线程退出
		exitChan <- true
	}()

	// 等待停止信号或exit命令
	select {
	case <-sigChan:
		log.Println("[INFO] 📶 Received interrupt signal, shutting down...")
	case <-exitChan:
		log.Println("[INFO] 📶 Exit command received, shutting down...")
	}

	doorStation.Stop()
	log.Println("[INFO] 👋 Door station stopped")
}
