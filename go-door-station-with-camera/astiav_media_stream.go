package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/asticode/go-astiav"
	"github.com/pion/webrtc/v4"
	"github.com/pion/webrtc/v4/pkg/media"
)

// AstiAV媒体流配置
type AstiAVConfig struct {
	Width    int
	Height   int
	FPS      int
	DeviceID string
	Format   string // "avfoundation" for macOS, "v4l2" for Linux, "dshow" for Windows
}

// AstiAV媒体流
type AstiAVMediaStream struct {
	config AstiAVConfig
	ctx    context.Context
	cancel context.CancelFunc

	// WebRTC tracks
	videoTrack *webrtc.TrackLocalStaticSample
	audioTrack *webrtc.TrackLocalStaticSample

	// FFmpeg components
	inputContext   *astiav.FormatContext
	videoStream    *astiav.Stream
	decoderContext *astiav.CodecContext
	encoderContext *astiav.CodecContext
	scaleContext   *astiav.SoftwareScaleContext

	// Frame buffers
	inputPacket   *astiav.Packet
	decodedFrame  *astiav.Frame
	scaledFrame   *astiav.Frame
	encodedPacket *astiav.Packet

	// State
	isStreaming       bool
	mutex             sync.RWMutex
	frameCount        int64
	debugMode         bool
	customPixelFormat string
	encoderMode       string // 特殊编码器模式
}

// 创建AstiAV媒体流
func NewAstiAVMediaStream(config AstiAVConfig) (*AstiAVMediaStream, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建WebRTC视频轨道
	videoTrack, err := webrtc.NewTrackLocalStaticSample(
		webrtc.RTPCodecCapability{MimeType: webrtc.MimeTypeH264},
		"video",
		"astiav-video",
	)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create video track: %v", err)
	}

	// 创建WebRTC音频轨道
	audioTrack, err := webrtc.NewTrackLocalStaticSample(
		webrtc.RTPCodecCapability{MimeType: webrtc.MimeTypePCMA},
		"audio",
		"astiav-audio",
	)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create audio track: %v", err)
	}

	stream := &AstiAVMediaStream{
		config:     config,
		ctx:        ctx,
		cancel:     cancel,
		videoTrack: videoTrack,
		audioTrack: audioTrack,
	}

	// 注册所有设备
	astiav.RegisterAllDevices()

	log.Printf("[INFO] 🎬 AstiAV media stream created: %dx%d@%dfps",
		config.Width, config.Height, config.FPS)

	return stream, nil
}

// 创建AstiAV媒体流（支持自定义像素格式）
func NewAstiAVMediaStreamWithPixelFormat(config AstiAVConfig, pixelFormat string) (*AstiAVMediaStream, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建WebRTC视频轨道
	videoTrack, err := webrtc.NewTrackLocalStaticSample(
		webrtc.RTPCodecCapability{MimeType: webrtc.MimeTypeH264},
		"video",
		"astiav-video",
	)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create video track: %v", err)
	}

	// 创建WebRTC音频轨道
	audioTrack, err := webrtc.NewTrackLocalStaticSample(
		webrtc.RTPCodecCapability{MimeType: webrtc.MimeTypePCMA},
		"audio",
		"astiav-audio",
	)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create audio track: %v", err)
	}

	stream := &AstiAVMediaStream{
		config:     config,
		ctx:        ctx,
		cancel:     cancel,
		videoTrack: videoTrack,
		audioTrack: audioTrack,
		debugMode:  true, // 启用调试模式
	}

	// 设置自定义像素格式
	stream.customPixelFormat = pixelFormat

	// 注册所有设备
	astiav.RegisterAllDevices()

	log.Printf("[INFO] 🎬 AstiAV media stream created with custom pixel format %s: %dx%d@%dfps",
		pixelFormat, config.Width, config.Height, config.FPS)

	return stream, nil
}

// 创建AstiAV媒体流（完全避免像素格式转换）
func NewAstiAVMediaStreamNoConversion(config AstiAVConfig) (*AstiAVMediaStream, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建WebRTC视频轨道
	videoTrack, err := webrtc.NewTrackLocalStaticSample(
		webrtc.RTPCodecCapability{MimeType: webrtc.MimeTypeH264},
		"video",
		"astiav-video",
	)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create video track: %v", err)
	}

	// 创建WebRTC音频轨道
	audioTrack, err := webrtc.NewTrackLocalStaticSample(
		webrtc.RTPCodecCapability{MimeType: webrtc.MimeTypePCMA},
		"audio",
		"astiav-audio",
	)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create audio track: %v", err)
	}

	stream := &AstiAVMediaStream{
		config:     config,
		ctx:        ctx,
		cancel:     cancel,
		videoTrack: videoTrack,
		audioTrack: audioTrack,
		debugMode:  true, // 启用调试模式
	}

	// 标记避免转换模式
	stream.customPixelFormat = "NO_CONVERSION"

	// 注册所有设备
	astiav.RegisterAllDevices()

	log.Printf("[INFO] 🎬 AstiAV media stream created with NO CONVERSION mode: %dx%d@%dfps",
		config.Width, config.Height, config.FPS)
	log.Printf("[INFO] ⚠️  This mode will use whatever pixel format the camera provides")

	return stream, nil
}

// 初始化输入设备
func (s *AstiAVMediaStream) initInput() error {
	// 分配格式上下文
	s.inputContext = astiav.AllocFormatContext()
	if s.inputContext == nil {
		return fmt.Errorf("failed to allocate input format context")
	}

	// 构建输入参数
	var inputURL string
	var inputFormat *astiav.InputFormat
	inputOptions := astiav.NewDictionary()
	defer inputOptions.Free()

	// 根据平台配置输入
	switch s.config.Format {
	case "avfoundation": // macOS
		inputURL = s.config.DeviceID + ":none"
		inputFormat = astiav.FindInputFormat("avfoundation")

		// 尝试强制使用更兼容的格式
		if s.config.Width > 0 && s.config.Height > 0 {
			inputOptions.Set("video_size", fmt.Sprintf("%dx%d", s.config.Width, s.config.Height), astiav.NewDictionaryFlags())
		}
		if s.config.FPS > 0 {
			inputOptions.Set("framerate", fmt.Sprintf("%d", s.config.FPS), astiav.NewDictionaryFlags())
		}

		// 像素格式设置
		if s.customPixelFormat == "NO_CONVERSION" {
			// NO_CONVERSION模式：不设置像素格式，让摄像头使用原生格式
			log.Printf("[INFO] 🎯 NO_CONVERSION mode: Using camera's native pixel format")
		} else if s.customPixelFormat != "" {
			// 使用自定义像素格式
			inputOptions.Set("pixel_format", s.customPixelFormat, astiav.NewDictionaryFlags())
			log.Printf("[INFO] Using custom pixel format: %s", s.customPixelFormat)
		} else {
			// 默认使用NV12格式 - 与YUV420P最兼容，减少转换问题
			inputOptions.Set("pixel_format", "nv12", astiav.NewDictionaryFlags())
			log.Printf("[INFO] 🎯 Using default pixel format: nv12 (optimized for minimal conversion artifacts)")
		}

	case "v4l2": // Linux
		inputURL = fmt.Sprintf("/dev/video%s", s.config.DeviceID)
		inputFormat = astiav.FindInputFormat("v4l2")
		if s.config.Width > 0 && s.config.Height > 0 {
			inputOptions.Set("video_size", fmt.Sprintf("%dx%d", s.config.Width, s.config.Height), astiav.NewDictionaryFlags())
		}
		if s.config.FPS > 0 {
			inputOptions.Set("framerate", fmt.Sprintf("%d", s.config.FPS), astiav.NewDictionaryFlags())
		}

	case "dshow": // Windows
		inputURL = fmt.Sprintf("video=%s", s.config.DeviceID)
		inputFormat = astiav.FindInputFormat("dshow")

	case "mandelbrot": // Mandelbrot测试图案
		inputURL = fmt.Sprintf("mandelbrot=size=%dx%d:rate=%d", s.config.Width, s.config.Height, s.config.FPS)
		inputFormat = astiav.FindInputFormat("lavfi")
		log.Printf("[INFO] Using mandelbrot test pattern - any lines will be visible")

	case "testsrc2": // 彩色测试图案
		inputURL = fmt.Sprintf("testsrc2=size=%dx%d:rate=%d", s.config.Width, s.config.Height, s.config.FPS)
		inputFormat = astiav.FindInputFormat("lavfi")
		log.Printf("[INFO] Using testsrc2 test pattern")

	case "color": // 纯色测试图案
		inputURL = fmt.Sprintf("color=c=blue:size=%dx%d:rate=%d", s.config.Width, s.config.Height, s.config.FPS)
		inputFormat = astiav.FindInputFormat("lavfi")
		log.Printf("[INFO] Using color test pattern")

	default:
		// 默认使用mandelbrot测试源
		inputURL = fmt.Sprintf("mandelbrot=size=%dx%d:rate=%d", s.config.Width, s.config.Height, s.config.FPS)
		inputFormat = astiav.FindInputFormat("lavfi")
		log.Printf("[WARN] Using default mandelbrot test pattern")
	}

	if inputFormat == nil {
		return fmt.Errorf("input format '%s' not found", s.config.Format)
	}

	// 打开输入
	log.Printf("[INFO] Opening input: %s", inputURL)
	if err := s.inputContext.OpenInput(inputURL, inputFormat, inputOptions); err != nil {
		return fmt.Errorf("failed to open input: %v", err)
	}

	// 查找流信息
	if err := s.inputContext.FindStreamInfo(nil); err != nil {
		return fmt.Errorf("failed to find stream info: %v", err)
	}

	// 找到视频流
	s.videoStream = nil
	for _, stream := range s.inputContext.Streams() {
		if stream.CodecParameters().MediaType() == astiav.MediaTypeVideo {
			s.videoStream = stream
			break
		}
	}

	if s.videoStream == nil {
		return fmt.Errorf("no video stream found")
	}

	log.Printf("[INFO] Found video stream: %dx%d, %s",
		s.videoStream.CodecParameters().Width(),
		s.videoStream.CodecParameters().Height(),
		s.videoStream.CodecParameters().CodecID().String())

	return nil
}

// 初始化解码器
func (s *AstiAVMediaStream) initDecoder() error {
	// 查找解码器
	decoder := astiav.FindDecoder(s.videoStream.CodecParameters().CodecID())
	if decoder == nil {
		return fmt.Errorf("decoder not found for codec %s", s.videoStream.CodecParameters().CodecID().String())
	}

	// 分配解码器上下文
	s.decoderContext = astiav.AllocCodecContext(decoder)
	if s.decoderContext == nil {
		return fmt.Errorf("failed to allocate decoder context")
	}

	// 复制参数
	if err := s.videoStream.CodecParameters().ToCodecContext(s.decoderContext); err != nil {
		return fmt.Errorf("failed to copy codec parameters: %v", err)
	}

	// 打开解码器
	if err := s.decoderContext.Open(decoder, nil); err != nil {
		return fmt.Errorf("failed to open decoder: %v", err)
	}

	log.Printf("[INFO] Decoder initialized: %s %dx%d %s",
		decoder.Name(),
		s.decoderContext.Width(), s.decoderContext.Height(),
		s.decoderContext.PixelFormat().String())

	return nil
}

// 初始化编码器
func (s *AstiAVMediaStream) initEncoder() error {
	// 查找H.264编码器
	encoder := astiav.FindEncoder(astiav.CodecIDH264)
	if encoder == nil {
		return fmt.Errorf("H.264 encoder not found")
	}

	// 分配编码器上下文
	s.encoderContext = astiav.AllocCodecContext(encoder)
	if s.encoderContext == nil {
		return fmt.Errorf("failed to allocate encoder context")
	}

	// 检查输入像素格式，尽量避免转换
	inputPixelFormat := s.decoderContext.PixelFormat()
	var outputPixelFormat astiav.PixelFormat

	// NO_CONVERSION模式：直接使用输入格式
	if s.customPixelFormat == "NO_CONVERSION" {
		// 检查编码器是否支持输入格式
		supportedFormats := []astiav.PixelFormat{
			astiav.PixelFormatYuv420P,
			astiav.PixelFormatYuv422P,
			astiav.PixelFormatYuvj420P,
			astiav.PixelFormatYuvj422P,
			astiav.PixelFormatUyvy422, // 添加UYVY422支持
			astiav.PixelFormatYuyv422, // 添加YUYV422支持
		}

		// 检查输入格式是否被支持
		supported := false
		for _, format := range supportedFormats {
			if inputPixelFormat == format {
				supported = true
				break
			}
		}

		if supported {
			outputPixelFormat = inputPixelFormat
			log.Printf("[INFO] 🎯 NO_CONVERSION mode: Using input format %s directly", inputPixelFormat.String())
		} else {
			outputPixelFormat = astiav.PixelFormatYuv420P
			log.Printf("[WARN] 🎯 NO_CONVERSION mode: Input format %s not supported by encoder, fallback to YUV420P", inputPixelFormat.String())
		}
	} else if inputPixelFormat == astiav.PixelFormatYuv420P {
		// 如果输入已经是YUV420P，就直接使用
		outputPixelFormat = astiav.PixelFormatYuv420P
		log.Printf("[INFO] Input is already YUV420P, no conversion needed")
	} else {
		outputPixelFormat = astiav.PixelFormatYuv420P
		log.Printf("[INFO] Converting from %s to YUV420P", inputPixelFormat.String())
	}

	// 设置编码参数
	s.encoderContext.SetWidth(s.config.Width)
	s.encoderContext.SetHeight(s.config.Height)
	s.encoderContext.SetPixelFormat(outputPixelFormat)
	s.encoderContext.SetTimeBase(astiav.NewRational(1, s.config.FPS))
	s.encoderContext.SetFramerate(astiav.NewRational(s.config.FPS, 1))

	// 编码选项 - 使用最兼容的设置
	options := astiav.NewDictionary()
	defer options.Free()

	// 根据编码器模式选择不同的设置
	if s.encoderMode == "HIGH_QUALITY" {
		// 高质量模式 - 保守设置，专门针对横线问题
		options.Set("preset", "slow", astiav.NewDictionaryFlags())      // 高质量preset
		options.Set("tune", "zerolatency", astiav.NewDictionaryFlags()) // 保持低延迟
		options.Set("profile", "baseline", astiav.NewDictionaryFlags()) // 保持兼容性
		options.Set("level", "3.1", astiav.NewDictionaryFlags())        // 标准level
		options.Set("crf", "18", astiav.NewDictionaryFlags())           // 高质量
		options.Set("threads", "1", astiav.NewDictionaryFlags())        // 单线程
		options.Set("slices", "1", astiav.NewDictionaryFlags())         // 单slice
		options.Set("deblock", "0:0", astiav.NewDictionaryFlags())      // 禁用去块滤波器
		log.Printf("[INFO] 🎯 Using HIGH_QUALITY encoder mode (conservative)")
	} else if s.encoderMode == "ULTRA_FAST" {
		// 超快模式 - 保守设置
		options.Set("preset", "veryfast", astiav.NewDictionaryFlags()) // 快速但不是最快
		options.Set("tune", "zerolatency", astiav.NewDictionaryFlags())
		options.Set("profile", "baseline", astiav.NewDictionaryFlags())
		options.Set("level", "3.1", astiav.NewDictionaryFlags())
		options.Set("crf", "25", astiav.NewDictionaryFlags()) // 适中质量
		options.Set("threads", "1", astiav.NewDictionaryFlags())
		options.Set("slices", "1", astiav.NewDictionaryFlags())
		log.Printf("[INFO] 🚀 Using ULTRA_FAST encoder mode (conservative)")
	} else {
		// 为了避免横线，使用最安全的编码设置
		options.Set("preset", "medium", astiav.NewDictionaryFlags()) // 平衡质量和速度
		options.Set("tune", "zerolatency", astiav.NewDictionaryFlags())
		options.Set("profile", "baseline", astiav.NewDictionaryFlags())
		options.Set("level", "3.1", astiav.NewDictionaryFlags())
		options.Set("crf", "23", astiav.NewDictionaryFlags())    // 标准质量
		options.Set("threads", "1", astiav.NewDictionaryFlags()) // 单线程避免竞争
		options.Set("slices", "1", astiav.NewDictionaryFlags())  // 单slice避免分割
	}

	// 打开编码器
	if err := s.encoderContext.Open(encoder, options); err != nil {
		return fmt.Errorf("failed to open encoder: %v", err)
	}

	log.Printf("[INFO] Encoder initialized: H.264 %dx%d, format: %s",
		s.config.Width, s.config.Height, outputPixelFormat.String())
	return nil
}

// 初始化缩放器
func (s *AstiAVMediaStream) initScaler() error {
	inputWidth := s.decoderContext.Width()
	inputHeight := s.decoderContext.Height()
	inputPixFmt := s.decoderContext.PixelFormat()
	outputPixFmt := s.encoderContext.PixelFormat() // 使用编码器的像素格式

	log.Printf("[INFO] Scaler check: %dx%d %s -> %dx%d %s",
		inputWidth, inputHeight, inputPixFmt.String(),
		s.config.Width, s.config.Height, outputPixFmt.String())

	// 如果尺寸和格式都相同，不需要缩放
	if inputWidth == s.config.Width && inputHeight == s.config.Height && inputPixFmt == outputPixFmt {
		log.Printf("[INFO] ✅ No scaling needed - same format and size")
		s.scaleContext = nil
		return nil
	}

	// 如果只是尺寸相同但格式不同，我们仍然需要转换
	if inputWidth == s.config.Width && inputHeight == s.config.Height {
		log.Printf("[INFO] ⚠️  Same size but different pixel format - conversion needed")
		log.Printf("[INFO]     This conversion might be causing horizontal lines!")
	}

	// 尝试使用更高质量的缩放算法
	var scalingAlgorithm astiav.SoftwareScaleContextFlag

	// 如果只是格式转换（相同尺寸），使用针对性算法
	if inputWidth == s.config.Width && inputHeight == s.config.Height {
		// 特殊处理NV12到YUV420P的转换 - 最优化的转换
		if inputPixFmt == astiav.PixelFormatNv12 && outputPixFmt == astiav.PixelFormatYuv420P {
			scalingAlgorithm = astiav.SoftwareScaleContextFlagFastBilinear
			log.Printf("[INFO] 🎯 NV12->YUV420P conversion: using FastBilinear (optimal semi-planar to planar)")
		} else if inputPixFmt == astiav.PixelFormatUyvy422 && outputPixFmt == astiav.PixelFormatYuv420P {
			scalingAlgorithm = astiav.SoftwareScaleContextFlagFastBilinear
			log.Printf("[INFO] 🎯 UYVY422->YUV420P conversion: using FastBilinear (optimized for packed->planar)")
		} else if (inputPixFmt == astiav.PixelFormatYuyv422 || inputPixFmt == astiav.PixelFormatUyvy422) && outputPixFmt == astiav.PixelFormatYuv420P {
			scalingAlgorithm = astiav.SoftwareScaleContextFlagFastBilinear
			log.Printf("[INFO] 🎯 YUV422->YUV420P conversion: using FastBilinear")
		} else {
			scalingAlgorithm = astiav.SoftwareScaleContextFlagBilinear
			log.Printf("[INFO] Format conversion only, using Bilinear")
		}
	} else {
		// 根据分辨率变化选择算法
		if inputWidth > s.config.Width || inputHeight > s.config.Height {
			// 缩小：使用Lanczos
			scalingAlgorithm = astiav.SoftwareScaleContextFlagLanczos
			log.Printf("[INFO] Downscaling detected, using Lanczos")
		} else {
			// 放大：使用双三次
			scalingAlgorithm = astiav.SoftwareScaleContextFlagBicubic
			log.Printf("[INFO] Upscaling detected, using Bicubic")
		}
	}

	// 创建缩放上下文
	var err error
	s.scaleContext, err = astiav.CreateSoftwareScaleContext(
		inputWidth, inputHeight, inputPixFmt,
		s.config.Width, s.config.Height, outputPixFmt,
		astiav.NewSoftwareScaleContextFlags(scalingAlgorithm),
	)
	if err != nil {
		return fmt.Errorf("failed to create scale context: %v", err)
	}

	// 对于UYVY422转换，尝试设置特殊参数来减少横线
	if inputPixFmt == astiav.PixelFormatUyvy422 && outputPixFmt == astiav.PixelFormatYuv420P {
		log.Printf("[INFO] 🎯 Applying UYVY422->YUV420P optimization to reduce horizontal lines")

		// 这里可以设置额外的swscale参数，但Go-AstiAV可能不直接支持
		// 我们主要依赖正确的算法选择和内存对齐
	}

	log.Printf("[INFO] ⚙️  Scaler initialized: %s algorithm",
		func() string {
			switch scalingAlgorithm {
			case astiav.SoftwareScaleContextFlagLanczos:
				return "Lanczos"
			case astiav.SoftwareScaleContextFlagBicubic:
				return "Bicubic"
			case astiav.SoftwareScaleContextFlagBilinear:
				return "Bilinear"
			case astiav.SoftwareScaleContextFlagSpline:
				return "Spline"
			default:
				return "Unknown"
			}
		}())

	return nil
}

// 分配帧缓冲区
func (s *AstiAVMediaStream) allocateFrames() error {
	// 输入包
	s.inputPacket = astiav.AllocPacket()
	if s.inputPacket == nil {
		return fmt.Errorf("failed to allocate input packet")
	}

	// 解码帧
	s.decodedFrame = astiav.AllocFrame()
	if s.decodedFrame == nil {
		return fmt.Errorf("failed to allocate decoded frame")
	}

	// 缩放帧
	s.scaledFrame = astiav.AllocFrame()
	if s.scaledFrame == nil {
		return fmt.Errorf("failed to allocate scaled frame")
	}

	// 设置输出帧属性
	s.scaledFrame.SetWidth(s.config.Width)
	s.scaledFrame.SetHeight(s.config.Height)
	s.scaledFrame.SetPixelFormat(astiav.PixelFormatYuv420P)

	// 分配输出帧缓冲区 - 使用更大的对齐来避免横线
	var alignment int
	if s.decoderContext.PixelFormat() == astiav.PixelFormatUyvy422 {
		// UYVY422格式使用更大的对齐来避免横线问题
		alignment = 64 // 64字节对齐
		log.Printf("[INFO] 🎯 Using 64-byte alignment for UYVY422 format")
	} else {
		alignment = 32 // 标准32字节对齐
		log.Printf("[INFO] Using standard 32-byte alignment")
	}

	if err := s.scaledFrame.AllocBuffer(alignment); err != nil {
		return fmt.Errorf("failed to allocate scaled frame buffer: %v", err)
	}

	// 编码包
	s.encodedPacket = astiav.AllocPacket()
	if s.encodedPacket == nil {
		return fmt.Errorf("failed to allocate encoded packet")
	}

	log.Printf("[INFO] Frame buffers allocated")
	return nil
}

// 开始流媒体传输
func (s *AstiAVMediaStream) StartStreaming() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isStreaming {
		return fmt.Errorf("already streaming")
	}

	// 初始化输入
	if err := s.initInput(); err != nil {
		return fmt.Errorf("failed to init input: %v", err)
	}

	// 初始化解码器
	if err := s.initDecoder(); err != nil {
		return fmt.Errorf("failed to init decoder: %v", err)
	}

	// 初始化编码器
	if err := s.initEncoder(); err != nil {
		return fmt.Errorf("failed to init encoder: %v", err)
	}

	// 初始化缩放器
	if err := s.initScaler(); err != nil {
		return fmt.Errorf("failed to init scaler: %v", err)
	}

	// 分配帧缓冲区
	if err := s.allocateFrames(); err != nil {
		return fmt.Errorf("failed to allocate frames: %v", err)
	}

	s.isStreaming = true

	// 启动处理循环
	go s.processingLoop()

	log.Printf("[INFO] 🚀 AstiAV streaming started")
	return nil
}

// 主处理循环
func (s *AstiAVMediaStream) processingLoop() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("[ERROR] Processing loop panic: %v", r)
		}
	}()

	frameTimer := time.NewTicker(time.Duration(1000/s.config.FPS) * time.Millisecond)
	defer frameTimer.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-frameTimer.C:
			if err := s.processOneFrame(); err != nil {
				if errors.Is(err, astiav.ErrEof) {
					log.Printf("[INFO] End of stream")
					return
				}
				if !errors.Is(err, astiav.ErrEagain) {
					log.Printf("[ERROR] Frame processing error: %v", err)
				}
				time.Sleep(10 * time.Millisecond)
			}
		}
	}
}

// 处理单帧
func (s *AstiAVMediaStream) processOneFrame() error {
	// 读取包
	s.inputPacket.Unref()
	if err := s.inputContext.ReadFrame(s.inputPacket); err != nil {
		return err
	}

	// 只处理视频包
	if s.inputPacket.StreamIndex() != s.videoStream.Index() {
		return nil
	}

	// 发送到解码器
	if err := s.decoderContext.SendPacket(s.inputPacket); err != nil {
		if errors.Is(err, astiav.ErrEagain) {
			return s.receiveAndEncode()
		}
		return fmt.Errorf("send packet failed: %v", err)
	}

	return s.receiveAndEncode()
}

// 接收解码帧并编码
func (s *AstiAVMediaStream) receiveAndEncode() error {
	for {
		// 接收解码帧
		if err := s.decoderContext.ReceiveFrame(s.decodedFrame); err != nil {
			if errors.Is(err, astiav.ErrEof) || errors.Is(err, astiav.ErrEagain) {
				break
			}
			return fmt.Errorf("receive frame failed: %v", err)
		}

		// 处理帧
		if err := s.processDecodedFrame(); err != nil {
			return err
		}
	}
	return nil
}

// 处理解码后的帧
func (s *AstiAVMediaStream) processDecodedFrame() error {
	s.frameCount++

	var frameToEncode *astiav.Frame

	// 判断是否需要缩放
	if s.scaleContext != nil {
		// 验证输入帧
		if s.decodedFrame.Width() == 0 || s.decodedFrame.Height() == 0 {
			log.Printf("[WARNING] Invalid decoded frame dimensions: %dx%d",
				s.decodedFrame.Width(), s.decodedFrame.Height())
			return nil // 跳过这一帧
		}

		// 确保输出帧可写
		if err := s.scaledFrame.MakeWritable(); err != nil {
			log.Printf("[WARNING] Failed to make frame writable: %v", err)
		}

		// 对于UYVY422转换，在缩放前记录调试信息
		if s.decodedFrame.PixelFormat() == astiav.PixelFormatUyvy422 && s.debugMode && s.frameCount%120 == 0 {
			log.Printf("[DEBUG] 🎯 UYVY422 frame before scaling: %dx%d, linesize=%v",
				s.decodedFrame.Width(), s.decodedFrame.Height(), s.decodedFrame.Linesize())
		}

		// 执行缩放/格式转换
		if err := s.scaleContext.ScaleFrame(s.decodedFrame, s.scaledFrame); err != nil {
			return fmt.Errorf("scale frame failed: %v", err)
		}
		frameToEncode = s.scaledFrame

		// 验证输出帧
		if s.scaledFrame.Width() != s.config.Width || s.scaledFrame.Height() != s.config.Height {
			log.Printf("[WARNING] Scaled frame size mismatch: got %dx%d, expected %dx%d",
				s.scaledFrame.Width(), s.scaledFrame.Height(), s.config.Width, s.config.Height)
		}

		// 调试信息：打印缩放详情
		if s.debugMode && s.frameCount%60 == 0 {
			inputLinesize := s.decodedFrame.Linesize()
			outputLinesize := s.scaledFrame.Linesize()
			log.Printf("[DEBUG] 🔄 Scaling: Input %dx%d %s linesize=%v -> Output %dx%d %s linesize=%v",
				s.decodedFrame.Width(), s.decodedFrame.Height(), s.decodedFrame.PixelFormat().String(), inputLinesize,
				s.scaledFrame.Width(), s.scaledFrame.Height(), s.scaledFrame.PixelFormat().String(), outputLinesize)
		}
	} else {
		// 不需要缩放，直接使用解码帧
		frameToEncode = s.decodedFrame
	}

	// 设置PTS
	frameToEncode.SetPts(s.frameCount)

	// 发送到编码器
	if err := s.encoderContext.SendFrame(frameToEncode); err != nil {
		if errors.Is(err, astiav.ErrEagain) {
			if err := s.receiveAndSend(); err != nil {
				return err
			}
			// 重试
			if err := s.encoderContext.SendFrame(frameToEncode); err != nil {
				return fmt.Errorf("send frame retry failed: %v", err)
			}
		} else {
			return fmt.Errorf("send frame failed: %v", err)
		}
	}

	return s.receiveAndSend()
}

// 接收编码包并发送到WebRTC
func (s *AstiAVMediaStream) receiveAndSend() error {
	for {
		s.encodedPacket.Unref()
		if err := s.encoderContext.ReceivePacket(s.encodedPacket); err != nil {
			if errors.Is(err, astiav.ErrEof) || errors.Is(err, astiav.ErrEagain) {
				break
			}
			return fmt.Errorf("receive packet failed: %v", err)
		}

		// 发送到WebRTC
		if err := s.sendToWebRTC(); err != nil {
			log.Printf("[ERROR] Failed to send to WebRTC: %v", err)
		}

		if s.debugMode && s.frameCount%30 == 0 {
			log.Printf("[DEBUG] Frame #%d encoded: %d bytes", s.frameCount, s.encodedPacket.Size())
		}
	}
	return nil
}

// 发送到WebRTC轨道
func (s *AstiAVMediaStream) sendToWebRTC() error {
	sample := media.Sample{
		Data:     s.encodedPacket.Data(),
		Duration: time.Duration(1000/s.config.FPS) * time.Millisecond,
	}

	return s.videoTrack.WriteSample(sample)
}

// 停止流媒体传输
func (s *AstiAVMediaStream) StopStreaming() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isStreaming {
		return
	}

	s.isStreaming = false
	s.cancel()

	// 清理资源
	s.cleanup()

	log.Printf("[INFO] 🛑 AstiAV streaming stopped")
}

// 清理资源
func (s *AstiAVMediaStream) cleanup() {
	if s.inputContext != nil {
		s.inputContext.CloseInput()
		s.inputContext.Free()
		s.inputContext = nil
	}

	if s.decoderContext != nil {
		s.decoderContext.Free()
		s.decoderContext = nil
	}

	if s.encoderContext != nil {
		s.encoderContext.Free()
		s.encoderContext = nil
	}

	if s.scaleContext != nil {
		s.scaleContext.Free()
		s.scaleContext = nil
	}

	if s.inputPacket != nil {
		s.inputPacket.Free()
		s.inputPacket = nil
	}

	if s.decodedFrame != nil {
		s.decodedFrame.Free()
		s.decodedFrame = nil
	}

	if s.scaledFrame != nil {
		s.scaledFrame.Free()
		s.scaledFrame = nil
	}

	if s.encodedPacket != nil {
		s.encodedPacket.Free()
		s.encodedPacket = nil
	}

	log.Printf("[DEBUG] Resources cleaned up")
}

// 获取视频轨道
func (s *AstiAVMediaStream) GetVideoTracks() []webrtc.TrackLocal {
	return []webrtc.TrackLocal{s.videoTrack}
}

// 获取音频轨道
func (s *AstiAVMediaStream) GetAudioTracks() []webrtc.TrackLocal {
	return []webrtc.TrackLocal{s.audioTrack}
}

// 获取统计信息
func (s *AstiAVMediaStream) GetStats() (frameCount, totalBytes int64, fps float64) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.frameCount, 0, float64(s.config.FPS)
}

// 启用调试模式
func (s *AstiAVMediaStream) EnableDebug() {
	s.debugMode = true
	log.Printf("[DEBUG] AstiAV debug mode enabled")
}

// 列出设备
func (s *AstiAVMediaStream) ListDevices() error {
	log.Printf("[INFO] Available devices for format: %s", s.config.Format)
	return nil
}

// 设置质量预设
func (s *AstiAVMediaStream) SetQualityPreset(preset string) error {
	log.Printf("[INFO] Quality preset: %s", preset)
	return nil
}

// 获取质量预设
func (s *AstiAVMediaStream) GetQualityPreset() string {
	return "medium"
}

// 创建调试测试模式
func (s *AstiAVMediaStream) EnableTestPattern(pattern string) {
	switch pattern {
	case "mandelbrot":
		s.config.Format = "mandelbrot"
	case "testsrc2":
		s.config.Format = "testsrc2"
	case "color":
		s.config.Format = "color"
	default:
		s.config.Format = "mandelbrot"
	}
	log.Printf("[INFO] Test pattern enabled: %s", pattern)
}

// 设置编码器模式
func (s *AstiAVMediaStream) SetEncoderMode(mode string) {
	s.encoderMode = mode
	log.Printf("[INFO] 🎯 Encoder mode set to: %s", mode)
}

// 获取编码器模式
func (s *AstiAVMediaStream) GetEncoderMode() string {
	if s.encoderMode == "" {
		return "DEFAULT"
	}
	return s.encoderMode
}
