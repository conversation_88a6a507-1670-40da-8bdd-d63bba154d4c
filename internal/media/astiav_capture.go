package media

import (
	"context"
	"fmt"
	"log"
	"runtime"
	"sync"
	"time"

	"github.com/asticode/go-astiav"
)

// AstiAVCameraConfig AstiAV摄像头配置
type AstiAVCameraConfig struct {
	Width    int
	Height   int
	FPS      int
	DeviceID string
	Format   string // "avfoundation" for macOS, "v4l2" for Linux, "dshow" for Windows
}

// AstiAVCamera AstiAV摄像头捕获器
type AstiAVCamera struct {
	config AstiAVCameraConfig
	ctx    context.Context
	cancel context.CancelFunc

	// FFmpeg components
	inputContext   *astiav.FormatContext
	videoStream    *astiav.Stream
	decoderContext *astiav.CodecContext
	encoderContext *astiav.CodecContext
	scaleContext   *astiav.SoftwareScaleContext

	// Frame buffers
	inputPacket   *astiav.Packet
	decodedFrame  *astiav.Frame
	scaledFrame   *astiav.Frame
	encodedPacket *astiav.Packet

	// State
	isCapturing bool
	mutex       sync.RWMutex
	frameCount  int64
	debugMode   bool

	// Frame channel for output
	frameChannel chan []byte
}

// NewAstiAVCamera 创建AstiAV摄像头捕获器
func NewAstiAVCamera(config AstiAVCameraConfig) (*AstiAVCamera, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 自动检测平台和设备
	if config.Format == "" {
		switch runtime.GOOS {
		case "darwin":
			config.Format = "avfoundation"
		case "linux":
			config.Format = "v4l2"
		case "windows":
			config.Format = "dshow"
		default:
			cancel()
			return nil, fmt.Errorf("unsupported platform: %s", runtime.GOOS)
		}
	}

	// 设置默认设备ID
	if config.DeviceID == "" {
		if config.Format == "v4l2" {
			config.DeviceID = "/dev/video0"
		} else {
			config.DeviceID = "0"
		}
	}

	// 设置默认分辨率和帧率
	if config.Width == 0 {
		config.Width = 320
	}
	if config.Height == 0 {
		config.Height = 240
	}
	if config.FPS == 0 {
		config.FPS = 10
	}

	camera := &AstiAVCamera{
		config:       config,
		ctx:          ctx,
		cancel:       cancel,
		frameChannel: make(chan []byte, 10), // 缓冲10帧
	}

	// 注册所有设备
	astiav.RegisterAllDevices()

	log.Printf("[ASTIAV] 摄像头捕获器已创建: %dx%d@%dfps, 设备: %s (%s)",
		config.Width, config.Height, config.FPS, config.DeviceID, config.Format)

	return camera, nil
}

// Start 启动摄像头捕获
func (c *AstiAVCamera) Start() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isCapturing {
		return fmt.Errorf("camera is already capturing")
	}

	log.Printf("[ASTIAV] 启动摄像头捕获...")

	// 初始化输入
	if err := c.initInput(); err != nil {
		return fmt.Errorf("failed to init input: %v", err)
	}

	// 初始化解码器
	if err := c.initDecoder(); err != nil {
		c.cleanup()
		return fmt.Errorf("failed to init decoder: %v", err)
	}

	// 初始化编码器
	if err := c.initEncoder(); err != nil {
		c.cleanup()
		return fmt.Errorf("failed to init encoder: %v", err)
	}

	// 初始化缩放器（如果需要）
	if err := c.initScaler(); err != nil {
		c.cleanup()
		return fmt.Errorf("failed to init scaler: %v", err)
	}

	// 分配帧和包
	c.inputPacket = astiav.AllocPacket()
	c.decodedFrame = astiav.AllocFrame()
	c.scaledFrame = astiav.AllocFrame()
	c.encodedPacket = astiav.AllocPacket()

	c.isCapturing = true

	// 启动捕获循环
	go c.captureLoop()

	log.Printf("[ASTIAV] ✅ 摄像头捕获已启动")
	return nil
}

// Stop 停止摄像头捕获
func (c *AstiAVCamera) Stop() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.isCapturing {
		return
	}

	log.Printf("[ASTIAV] 停止摄像头捕获...")

	c.isCapturing = false
	c.cancel()

	// 清理资源
	c.cleanup()

	// 关闭帧通道
	close(c.frameChannel)

	log.Printf("[ASTIAV] ✅ 摄像头捕获已停止")
}

// GetFrameChannel 获取帧数据通道
func (c *AstiAVCamera) GetFrameChannel() <-chan []byte {
	return c.frameChannel
}

// IsCapturing 检查是否正在捕获
func (c *AstiAVCamera) IsCapturing() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.isCapturing
}

// GetStats 获取统计信息
func (c *AstiAVCamera) GetStats() (frameCount int64, isCapturing bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.frameCount, c.isCapturing
}

// EnableDebug 启用调试模式
func (c *AstiAVCamera) EnableDebug() {
	c.debugMode = true
	log.Printf("[ASTIAV] 调试模式已启用")
}

// ListDevices 列出可用设备
func (c *AstiAVCamera) ListDevices() error {
	log.Printf("[ASTIAV] 列出可用设备 (%s):", c.config.Format)

	switch c.config.Format {
	case "avfoundation":
		log.Printf("[ASTIAV] macOS AVFoundation 设备:")
		log.Printf("[ASTIAV]   0 - 默认摄像头")
		log.Printf("[ASTIAV]   1 - 第二个摄像头（如果有）")
		log.Printf("[ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i \"\"")

	case "v4l2":
		log.Printf("[ASTIAV] Linux V4L2 设备:")
		log.Printf("[ASTIAV]   /dev/video0 - 默认摄像头")
		log.Printf("[ASTIAV]   /dev/video1 - 第二个摄像头（如果有）")
		log.Printf("[ASTIAV] 使用命令查看详细设备: v4l2-ctl --list-devices")

	case "dshow":
		log.Printf("[ASTIAV] Windows DirectShow 设备:")
		log.Printf("[ASTIAV]   USB2.0 Camera - 常见USB摄像头")
		log.Printf("[ASTIAV]   Integrated Camera - 集成摄像头")
		log.Printf("[ASTIAV] 使用命令查看详细设备: ffmpeg -f dshow -list_devices true -i dummy")

	default:
		return fmt.Errorf("unsupported format: %s", c.config.Format)
	}

	return nil
}

// initInput 初始化输入设备
func (c *AstiAVCamera) initInput() error {
	// 分配格式上下文
	c.inputContext = astiav.AllocFormatContext()
	if c.inputContext == nil {
		return fmt.Errorf("failed to allocate input format context")
	}

	// 构建输入参数
	var inputURL string
	var inputFormat *astiav.InputFormat
	inputOptions := astiav.NewDictionary()
	defer inputOptions.Free()

	// 根据平台配置输入
	switch c.config.Format {
	case "avfoundation": // macOS
		inputURL = c.config.DeviceID + ":none" // 只要视频，不要音频
		inputFormat = astiav.FindInputFormat("avfoundation")

		// 设置视频参数
		if c.config.Width > 0 && c.config.Height > 0 {
			inputOptions.Set("video_size", fmt.Sprintf("%dx%d", c.config.Width, c.config.Height), astiav.NewDictionaryFlags())
		}
		if c.config.FPS > 0 {
			inputOptions.Set("framerate", fmt.Sprintf("%d", c.config.FPS), astiav.NewDictionaryFlags())
		}

		// 使用兼容的像素格式
		inputOptions.Set("pixel_format", "uyvy422", astiav.NewDictionaryFlags())

	case "v4l2": // Linux
		inputURL = c.config.DeviceID
		inputFormat = astiav.FindInputFormat("v4l2")
		if c.config.Width > 0 && c.config.Height > 0 {
			inputOptions.Set("video_size", fmt.Sprintf("%dx%d", c.config.Width, c.config.Height), astiav.NewDictionaryFlags())
		}
		if c.config.FPS > 0 {
			inputOptions.Set("framerate", fmt.Sprintf("%d", c.config.FPS), astiav.NewDictionaryFlags())
		}

	case "dshow": // Windows
		inputURL = fmt.Sprintf("video=%s", c.config.DeviceID)
		inputFormat = astiav.FindInputFormat("dshow")

	default:
		return fmt.Errorf("unsupported format: %s", c.config.Format)
	}

	if inputFormat == nil {
		return fmt.Errorf("input format '%s' not found", c.config.Format)
	}

	// 打开输入
	log.Printf("[ASTIAV] 打开输入: %s", inputURL)
	if err := c.inputContext.OpenInput(inputURL, inputFormat, inputOptions); err != nil {
		return fmt.Errorf("failed to open input: %v", err)
	}

	// 查找流信息
	if err := c.inputContext.FindStreamInfo(nil); err != nil {
		return fmt.Errorf("failed to find stream info: %v", err)
	}

	// 找到视频流
	c.videoStream = nil
	for _, stream := range c.inputContext.Streams() {
		if stream.CodecParameters().MediaType() == astiav.MediaTypeVideo {
			c.videoStream = stream
			break
		}
	}

	if c.videoStream == nil {
		return fmt.Errorf("no video stream found")
	}

	log.Printf("[ASTIAV] 找到视频流: %dx%d, %s",
		c.videoStream.CodecParameters().Width(),
		c.videoStream.CodecParameters().Height(),
		c.videoStream.CodecParameters().CodecID().String())

	return nil
}
