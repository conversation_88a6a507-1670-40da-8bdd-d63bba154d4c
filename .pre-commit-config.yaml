# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

repos:
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.6
    hooks:
      - id: codespell

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: check-executables-have-shebangs
      - id: check-merge-conflict
      - id: double-quote-string-fixer
      - id: end-of-file-fixer
        types_or: [c, c++]
      - id: mixed-line-ending
        args: ['-f=lf']
        types_or: [c, c++]
      - id: no-commit-to-branch
        name: Do not use more than one slash in the branch name
        args: ['--pattern', '^[^/]*/[^/]*/']
      - id: no-commit-to-branch
        name: Do not use uppercase letters in the branch name
        args: ['--pattern', '^[^A-Z]*[A-Z]']
      - id: trailing-whitespace
        types_or: [c, c++]
