#!/bin/bash

# 优雅退出测试脚本

echo "=== 优雅退出测试 ==="
echo

echo "🔧 修复内容:"
echo "1. ✅ 改进了优雅关闭流程"
echo "2. ✅ 先停止CLI界面"
echo "3. ✅ 再关闭视频通话对等端（包括AstiAV摄像头）"
echo "4. ✅ 等待资源完全释放"
echo "5. ✅ 避免goroutine泄漏和资源竞争"
echo

echo "🚀 启动程序测试优雅退出..."
echo

echo "测试步骤:"
echo "1. 程序启动后，输入: join d0002"
echo "2. 启用监控: monitor"
echo "3. 等待几秒钟让AstiAV摄像头开始工作"
echo "4. 按 Ctrl+C 退出程序"
echo

echo "预期效果:"
echo "✅ 收到信号: interrupt"
echo "✅ 正在关闭应用程序..."
echo "✅ 正在关闭视频通话对等端..."
echo "✅ [ASTIAV] 停止摄像头捕获..."
echo "✅ [ASTIAV] ✅ 摄像头捕获已停止"
echo "✅ 等待资源释放..."
echo "✅ 应用程序已安全关闭"
echo "✅ 没有panic或goroutine错误"
echo

echo "如果看到以上日志且没有错误堆栈，说明优雅退出成功！"
echo

# 清理调试文件
rm -f debug/*.jpg debug/*.mjpeg debug/*.pcma debug/*.h264 debug/*.log

echo "✅ 已清理调试文件"
echo

# 启动程序
./videocall-peer

echo
echo "=== 退出测试完成 ==="

# 检查是否有core dump或错误文件
if ls core* 1> /dev/null 2>&1; then
    echo "❌ 发现core dump文件，程序可能崩溃了"
    ls -la core*
else
    echo "✅ 没有core dump文件，程序正常退出"
fi

echo
echo "优雅退出修复说明:"
echo "- 信号处理: 捕获SIGINT和SIGTERM信号"
echo "- 分步关闭: 先停止CLI，再关闭peer"
echo "- 资源清理: AstiAV摄像头正确停止和清理"
echo "- 等待机制: 给资源释放足够的时间"
echo "- 避免竞争: 防止goroutine和资源竞争条件"
echo
echo "现在Ctrl+C退出应该不会再有panic错误了！"
