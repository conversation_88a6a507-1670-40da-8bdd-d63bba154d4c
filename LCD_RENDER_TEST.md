# LCD渲染测试程序

## 🎯 **目的**

这个测试程序用于验证ESP32P4的`av_render` LCD渲染功能是否正常工作，绕过WebRTC和JPEG解码，直接测试LCD显示功能。

## 📁 **新增文件**

1. `solutions/videocall_demo/main/test_lcd_render.c` - 主要测试代码
2. `solutions/videocall_demo/main/test_lcd_render.h` - 头文件
3. 修改了 `solutions/videocall_demo/main/main.c` - 添加控制台命令
4. 修改了 `solutions/videocall_demo/main/CMakeLists.txt` - 添加编译文件

## 🔧 **编译方法**

```bash
cd solutions/videocall_demo
idf.py build
idf.py flash monitor
```

## 🚀 **使用方法**

1. **启动设备**：
   ```
   esp> 
   ```

2. **连接WiFi**（如果需要）：
   ```
   esp> wifi YourSSID YourPassword
   ```

3. **启动LCD测试**：
   ```
   esp> lcd_test
   ```

## 🎨 **测试图案**

程序会循环显示以下8种测试图案，每个图案显示3秒：

1. **纯红色** - 测试红色通道
2. **纯绿色** - 测试绿色通道  
3. **纯蓝色** - 测试蓝色通道
4. **纯白色** - 测试背光和所有颜色通道
5. **水平条纹** - 7种颜色的水平条纹
6. **垂直条纹** - 7种颜色的垂直条纹
7. **棋盘图案** - 黑白棋盘格，测试分辨率
8. **颜色渐变** - 红绿蓝渐变，测试颜色过渡

## 📊 **预期结果**

### ✅ **如果LCD渲染正常**
- 屏幕会显示清晰的彩色图案
- 每3秒自动切换到下一个图案
- 控制台输出类似：
  ```
  I (xxx) LCD_TEST: 显示图案 0: 纯红色 (帧 0)
  I (xxx) LCD_TEST: LCD渲染成功
  I (xxx) LCD_RENDER: fps: 1
  ```

### ❌ **如果LCD渲染有问题**
- 屏幕仍然黑屏
- 控制台可能输出错误信息：
  ```
  E (xxx) LCD_TEST: LCD渲染失败: -1
  E (xxx) LCD_RENDER: xxx
  ```

## 🔍 **技术细节**

### **测试原理**
1. **绕过WebRTC** - 不使用网络传输
2. **绕过JPEG解码** - 直接生成RGB565数据
3. **直接调用av_render** - 测试LCD渲染层
4. **多种图案** - 全面测试显示功能

### **RGB565格式**
- 每像素2字节
- 红色：5位 (0xF800)
- 绿色：6位 (0x07E0)  
- 蓝色：5位 (0x001F)
- 1024x600分辨率 = 1,228,800字节

### **关键函数**
- `av_render_alloc_lcd_render()` - 创建LCD渲染器
- `video_render_set_frame_info()` - 设置帧信息
- `video_render_write()` - 写入图像数据

## 🐛 **故障排除**

### **如果编译失败**
- 检查是否正确添加了所有文件
- 确认CMakeLists.txt已更新
- 清理并重新编译：`idf.py fullclean && idf.py build`

### **如果LCD测试失败**
1. **检查LCD初始化**：
   - 确认`board_lcd_init()`成功
   - 检查LCD句柄是否有效

2. **检查内存分配**：
   - 确认有足够的内存分配图像缓冲区
   - 检查PSRAM配置

3. **检查渲染器配置**：
   - 确认DSI面板配置正确
   - 检查帧格式设置

## 📝 **日志分析**

### **正常日志示例**
```
I (xxx) LCD_TEST: === LCD渲染测试开始 ===
I (xxx) LCD_TEST: LCD render initialized successfully
I (xxx) LCD_TEST: 图像缓冲区分配成功: 1228800 bytes
I (xxx) LCD_TEST: 显示图案 0: 纯红色 (帧 0)
I (xxx) LCD_TEST: LCD渲染成功
I (xxx) LCD_RENDER: fps: 1
```

### **异常日志示例**
```
E (xxx) LCD_TEST: Failed to get LCD handle
E (xxx) LCD_TEST: LCD渲染器初始化失败
```

## 🎯 **测试目标**

这个测试程序可以帮助确定：

1. **LCD硬件是否正常** - 如果能看到图案，说明LCD硬件工作
2. **av_render是否正常** - 如果渲染成功，说明软件层正常
3. **颜色通道是否正常** - 通过不同颜色的图案测试
4. **分辨率是否正确** - 通过棋盘图案测试像素精度
5. **背光是否开启** - 通过白色图案测试

如果这个测试显示正常，那么问题就在WebRTC或JPEG解码环节；如果这个测试也黑屏，那么问题在LCD渲染层或硬件层。
