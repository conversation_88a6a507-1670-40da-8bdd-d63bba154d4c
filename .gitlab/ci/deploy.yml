push_to_github:
  stage: deploy
  rules:
    - if:
        '($CI_COMMIT_REF_NAME == "main" ||
        $CI_COMMIT_BRANCH =~ /^release\/v/ ||
        $CI_COMMIT_TAG =~ /^v\d+\.\d+(\.\d+)?($|-)/) &&
        $CI_PIPELINE_SOURCE != "schedule"'
  when: on_success
  image: $CI_DOCKER_REGISTRY/esp32-ci-env
  variables:
    GIT_STRATEGY: clone
  before_script:
    - echo "skip default before_script"
  script:
    - source ${CI_PROJECT_DIR}/tools/ci/utils.sh
    - add_github_ssh_keys
    - git fetch --unshallow
    - git remote remove github &>/dev/null || true
    - git remote add github ${TARGET_GITHUB_REPO}
    - push_to_github
