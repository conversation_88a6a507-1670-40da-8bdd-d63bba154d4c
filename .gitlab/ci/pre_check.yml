.pre_check_template:
  stage: pre_check
  image: "${CI_DOCKER_REGISTRY}/esp-env-v5.4:1"
  tags:
    - host_test
  dependencies: []

.check_branch_script: &check_branch_script |
  if [[ "$BOT_LABEL_SKIP_BRANCH_CHECK" = "true"  || "$CI_MERGE_REQUEST_LABELS" =~ skip_branch_check ]]; then
    echo "Skip branch name check."
  else
    ${CHECK_TOOLS_PATH}/check_commit_format branch-name
  fi

.check_commit_script: &check_commit_script |
  if [[ "$BOT_LABEL_SKIP_COMMIT_CHECK" = "true"  || "$CI_MERGE_REQUEST_LABELS" =~ skip_commit_check ]]; then
    echo "Skip commit message check."
  else
    ${CHECK_TOOLS_PATH}/check_commit_format commit-message
  fi

.check_code_script: &check_code_script |
  if [[ "$BOT_LABEL_SKIP_CODE_CHECK" = "true" || "$CI_MERGE_REQUEST_LABELS" =~ skip_code_check ]]; then
    echo "Skip code style check."
  else
    ${CHECK_TOOLS_PATH}/check_commit_format --verbose --format code-style;
    if [ -d "${CHECK_REPO_PATH}/formatted" ] && [ "$(ls -A ${CHECK_REPO_PATH}/formatted)" ]; then
      git diff -- . ':(exclude).gitmodules' > ${CHECK_REPO_PATH}/patch/code_format.patch
      exit 1
    fi
  fi

check_commit_format:
  image: "$MULTIMEDIA_DOCKER_REGISTRY/pre_commit:1"
  stage: pre_check
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_LABELS !~ /^(?:[^,\n\r]+,)*skip_check(?:,[^,\n\r]+)*$/i'
  script:
    - git clone --depth 1 $CHECK_FORMAT_TOOL_REPO
    - cd check-format-tool/bin
    - export CHECK_REPO_PATH=${CI_PROJECT_DIR}
    - ./install.sh
    - . ./export.sh
    - cd ${CHECK_REPO_PATH};
    - *check_branch_script
    - *check_commit_script
    - *check_code_script
  allow_failure: true
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/patch
      - ${CI_PROJECT_DIR}/formatted
    when: always
    expire_in: 4 days

check_pre_commit:
  extends:
    - .rules:build:protected-merge-requests
    - .pre_check_template
  before_script:
    - pip install pre-commit
  script:
    - |
      # merged results pipelines, by default
      if [[ -n $CI_MERGE_REQUEST_SOURCE_BRANCH_SHA ]]; then
        git fetch origin $CI_MERGE_REQUEST_DIFF_BASE_SHA --depth=1 ${GIT_FETCH_EXTRA_FLAGS}
        git fetch origin $CI_MERGE_REQUEST_SOURCE_BRANCH_SHA --depth=1 ${GIT_FETCH_EXTRA_FLAGS}
        export GIT_DIFF_OUTPUT=$(git diff --name-only $CI_MERGE_REQUEST_DIFF_BASE_SHA $CI_MERGE_REQUEST_SOURCE_BRANCH_SHA)
      # merge request pipelines, when the mr got conflicts
      elif [[ -n $CI_MERGE_REQUEST_DIFF_BASE_SHA ]]; then
        git fetch origin $CI_MERGE_REQUEST_DIFF_BASE_SHA --depth=1 ${GIT_FETCH_EXTRA_FLAGS}
        git fetch origin $CI_COMMIT_SHA --depth=1 ${GIT_FETCH_EXTRA_FLAGS}
        export GIT_DIFF_OUTPUT=$(git diff --name-only $CI_MERGE_REQUEST_DIFF_BASE_SHA $CI_COMMIT_SHA)
      # other pipelines, like the protected branches pipelines
      elif [[ "$CI_COMMIT_BEFORE_SHA" != "0000000000000000000000000000000000000000" ]]; then
        git fetch origin $CI_COMMIT_BEFORE_SHA --depth=1 ${GIT_FETCH_EXTRA_FLAGS}
        git fetch origin $CI_COMMIT_SHA --depth=1 ${GIT_FETCH_EXTRA_FLAGS}
        export GIT_DIFF_OUTPUT=$(git diff --name-only $CI_COMMIT_BEFORE_SHA $CI_COMMIT_SHA)
      else
        # pipeline source could be web, scheduler, etc.
        git fetch origin $CI_COMMIT_SHA --depth=2 ${GIT_FETCH_EXTRA_FLAGS}
        export GIT_DIFF_OUTPUT=$(git diff --name-only $CI_COMMIT_SHA~1 $CI_COMMIT_SHA)
      fi
    - MODIFIED_FILES=$(echo $GIT_DIFF_OUTPUT | xargs)
    - echo "MODIFIED_FILES=$MODIFIED_FILES" >> pipeline.env
    - pre-commit install --allow-missing-config -t pre-commit -t commit-msg
    - pre-commit run --files $MODIFIED_FILES
  artifacts:
    reports:
      dotenv: pipeline.env
    paths:
      - pipeline.env
    expire_in: 1 week
    when: always
