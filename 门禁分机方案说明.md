# 门禁-分机方案实现指南

## 方案概述

本方案基于ESP WebRTC项目，实现门禁主机与分机的视频通话系统：
- **门禁主机**：发送视频流，接收音频
- **分机设备**：接收视频流，双向音频通信

## 核心修改要点

### 1. 媒体传输方向配置

```c
// doorbell_demo (门铃 - 发送视频)
.audio_dir = ESP_PEER_MEDIA_DIR_SEND_RECV,  // 双向音频
.video_dir = ESP_PEER_MEDIA_DIR_SEND_ONLY,  // 只发送视频

// 分机方案 (接收视频)  
.audio_dir = ESP_PEER_MEDIA_DIR_SEND_RECV,  // 双向音频
.video_dir = ESP_PEER_MEDIA_DIR_RECV_ONLY,  // 只接收视频 ⭐关键修改
```

### 2. 自定义命令协议

```c
// 门禁-分机通信命令
#define INTERCOM_CALL_REQUEST_CMD    "CALL_REQUEST"      // 门禁主机请求呼叫
#define INTERCOM_CALL_ACCEPT_CMD     "CALL_ACCEPT"       // 分机接听
#define INTERCOM_CALL_REJECT_CMD     "CALL_REJECT"       // 分机拒绝
#define INTERCOM_CALL_HANGUP_CMD     "CALL_HANGUP"       // 挂断通话
#define INTERCOM_DOOR_OPEN_CMD       "DOOR_OPEN"         // 开门命令
```

### 3. 状态管理

```c
typedef enum {
    INTERCOM_STATE_IDLE,           // 空闲状态
    INTERCOM_STATE_INCOMING_CALL,  // 来电状态  
    INTERCOM_STATE_CONNECTED,      // 通话中
} intercom_state_t;
```

## ESP32分机端实现

基于`doorbell_demo`修改，主要文件结构：

```
intercom_extension/
├── main/
│   ├── main.c              // 主程序
│   ├── webrtc.c           // WebRTC配置(关键修改)
│   ├── media_sys.c        // 媒体系统
│   ├── settings.h         // 配置文件
│   └── CMakeLists.txt
├── sdkconfig.defaults
└── README.md
```

### 关键代码修改

在`webrtc.c`中的核心配置：

```c
esp_webrtc_cfg_t cfg = {
    .peer_cfg = {
        .audio_info = {
            .codec = ESP_PEER_AUDIO_CODEC_OPUS,
            .sample_rate = 16000,
            .channel = 2,
        },
        .video_info = {
            .codec = ESP_PEER_VIDEO_CODEC_H264,
            .width = 640,
            .height = 480,
            .fps = 15,
        },
        .audio_dir = ESP_PEER_MEDIA_DIR_SEND_RECV,  // 双向音频
        .video_dir = ESP_PEER_MEDIA_DIR_RECV_ONLY,  // 只接收视频 ⭐
        .on_custom_data = intercom_on_cmd,
        .enable_data_channel = true,
        .no_auto_reconnect = true,
    },
    .signaling_cfg = {
        .signal_url = url,
    },
    .peer_impl = esp_peer_get_default_impl(),
    .signaling_impl = esp_signaling_get_apprtc_impl(),
};
```

## AppRTC信令协议详解

### 1. 连接建立流程

```mermaid
sequenceDiagram
    participant Door as 门禁主机
    participant Server as AppRTC服务器
    participant Ext as 分机

    Door->>Server: POST /join/{room_id}
    Server-->>Door: 房间信息(client_id, wss_url)
    
    Ext->>Server: POST /join/{room_id}  
    Server-->>Ext: 房间信息
    
    Door->>Server: WebSocket连接
    Door->>Server: {"cmd":"register","roomid":"xxx","clientid":"xxx"}
    
    Ext->>Server: WebSocket连接
    Ext->>Server: Register消息
    
    Door->>Ext: CALL_REQUEST命令
    Ext-->>Door: CALL_ACCEPT/CALL_REJECT
    
    Door->>Ext: SDP Offer (视频发送)
    Ext-->>Door: SDP Answer (视频接收)
    
    Note over Door,Ext: WebRTC连接建立，开始视频传输
```

### 2. 消息格式

**WebSocket消息：**
```json
// 注册房间
{"cmd":"register","roomid":"intercom_room","clientid":"client_123"}

// 发送自定义命令
{"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"CALL_REQUEST\"}"}

// 发送SDP
{"cmd":"send","msg":"{\"type\":\"offer\",\"sdp\":\"v=0...\"}"}
```

**HTTP POST消息：**
```json
// SDP交换 - POST到 {server}/message/{room_id}/{client_id}
{"type":"offer","sdp":"v=0\r\no=- 123..."}
{"type":"answer","sdp":"v=0\r\no=- 456..."}

// ICE候选 - POST到同一URL
{"type":"candidate","candidate":"candidate:1 1 UDP 2130706431..."}
```

## Go语言门禁主机实现

### 依赖安装

```bash
# 初始化Go模块
go mod init door-station-simulator

# 安装依赖
go get github.com/gorilla/websocket@v1.5.0
go get github.com/pion/webrtc/v3@v3.2.21
```

### 使用方法

```bash
# 编译运行
go run door_station_simulator.go https://webrtc.espressif.com intercom_test_room

# 或者编译后运行
go build -o door_station door_station_simulator.go
./door_station https://webrtc.espressif.com intercom_test_room
```

### 主要功能

1. **自动连接**：连接到AppRTC服务器并注册房间
2. **WebRTC建立**：创建PeerConnection并发送视频流
3. **命令处理**：发送和接收自定义命令
4. **视频推流**：向分机发送H.264视频流

## 完整部署步骤

### 1. 准备分机设备（ESP32）

```bash
# 1. 复制doorbell_demo
cp -r solutions/doorbell_demo solutions/intercom_extension

# 2. 修改配置
# 编辑 solutions/intercom_extension/main/webrtc.c
# 将 .video_dir = ESP_PEER_MEDIA_DIR_SEND_ONLY 
# 改为 .video_dir = ESP_PEER_MEDIA_DIR_RECV_ONLY

# 3. 修改WiFi配置
# 编辑 solutions/intercom_extension/main/settings.h
# 设置WIFI_SSID和WIFI_PASSWORD

# 4. 编译烧录
cd solutions/intercom_extension
idf.py build flash monitor
```

### 2. 启动门禁主机（Go程序）

```bash
# 1. 编译Go程序
cd /path/to/go/code
go mod tidy
go build door_station_simulator.go

# 2. 运行门禁主机
./door_station_simulator https://webrtc.espressif.com test_room_123
```

### 3. 启动分机设备

```bash
# 在ESP32串口中执行
join test_room_123
```

### 4. 测试通话流程

1. **门禁主机发起呼叫**：自动发送`CALL_REQUEST`
2. **分机响应**：
   - 自动接听：分机发送`CALL_ACCEPT`
   - 手动接听：按下按键或CLI命令
3. **建立连接**：WebRTC连接建立，开始视频传输
4. **结束通话**：发送`CALL_HANGUP`

## 扩展功能

### 1. 实际摄像头集成

```go
// 在Go代码中集成实际摄像头
func (ds *DoorStation) startRealVideoStream() {
    // 使用gstreamer或ffmpeg读取摄像头
    // 将H.264数据写入videoTrack
}
```

### 2. 开门控制

```c
// 在分机端添加开门控制
void handle_door_open_command() {
    // 控制继电器或其他开门设备
    gpio_set_level(DOOR_RELAY_PIN, 1);
    vTaskDelay(pdMS_TO_TICKS(2000));  // 开门2秒
    gpio_set_level(DOOR_RELAY_PIN, 0);
}
```

### 3. 视频录制

```c
// 在分机端添加录制功能
int start_video_recording(char *filename);
int stop_video_recording(void);
```

## 注意事项

1. **网络配置**：确保两端在同一网络或可相互访问
2. **防火墙**：开放WebRTC所需端口（通常是动态分配）
3. **STUN/TURN**：复杂网络环境可能需要配置TURN服务器
4. **性能优化**：根据网络带宽调整视频参数
5. **延迟优化**：优化编码参数降低延迟

## 故障排除

### 常见问题

1. **连接失败**：检查网络和服务器URL
2. **无视频**：检查媒体方向配置
3. **音频问题**：检查音频编解码器配置
4. **命令不响应**：检查自定义命令处理函数

### 调试方法

```c
// 启用WebRTC调试日志
esp_log_level_set("webrtc", ESP_LOG_DEBUG);
esp_log_level_set("APPRTC_SIG", ESP_LOG_DEBUG);
```

```go
// Go代码中增加详细日志
log.SetLevel(log.DebugLevel)
```

这个方案提供了完整的门禁-分机视频通话解决方案，可以根据实际需求进行定制和扩展。 