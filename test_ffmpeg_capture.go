package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"os/exec"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"
)

func main() {
	fmt.Println("🎥 FFmpeg Video Capture Test")
	fmt.Println("============================")

	// 检查FFmpeg是否安装
	if err := checkFFmpegInstallation(); err != nil {
		log.Fatalf("[ERROR] %v", err)
	}

	// 创建FFmpeg配置
	config := FFmpegConfig{
		Width:    640,
		Height:   480,
		FPS:      15,
		DeviceID: "0",            // 默认摄像头
		Format:   "avfoundation", // macOS
	}

	// 显示菜单
	showMenu()

	// 创建FFmpeg捕获器
	capture := NewFFmpegVideoCapture(config)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	scanner := bufio.NewScanner(os.Stdin)

	for {
		fmt.Print("ffmpeg-test> ")

		select {
		case <-sigChan:
			fmt.Println("\n[INFO] 📶 Received interrupt signal, shutting down...")
			capture.Stop()
			return
		default:
			if !scanner.Scan() {
				break
			}

			input := strings.TrimSpace(scanner.Text())
			if input == "" {
				continue
			}

			parts := strings.Fields(input)
			cmd := strings.ToLower(parts[0])

			switch cmd {
			case "list", "devices":
				fmt.Println("[INFO] 📹 Listing available video devices...")
				if err := capture.ListDevices(); err != nil {
					log.Printf("[ERROR] Failed to list devices: %v", err)
				}

			case "start":
				if capture.IsRunning() {
					fmt.Println("[WARN] Capture is already running")
					continue
				}

				fmt.Printf("[INFO] 🎬 Starting video capture: %dx%d@%dfps\n",
					config.Width, config.Height, config.FPS)

				if err := capture.Start(); err != nil {
					log.Printf("[ERROR] Failed to start capture: %v", err)
				} else {
					fmt.Println("[INFO] ✅ Video capture started successfully!")
					fmt.Println("[INFO] 💾 Frames will be saved to ffmpeg_debug/ directory")
					fmt.Println("[INFO] 📊 Check console for frame statistics")
				}

			case "stop":
				if !capture.IsRunning() {
					fmt.Println("[WARN] Capture is not running")
					continue
				}

				fmt.Println("[INFO] 🛑 Stopping video capture...")
				capture.Stop()
				fmt.Println("[INFO] ✅ Video capture stopped")

			case "status":
				fmt.Printf("📊 Capture Status:\n")
				fmt.Printf("  Running: %v\n", capture.IsRunning())
				fmt.Printf("  Resolution: %dx%d\n", config.Width, config.Height)
				fmt.Printf("  FPS: %d\n", config.FPS)
				fmt.Printf("  Device: %s\n", config.DeviceID)

			case "config":
				if len(parts) < 3 {
					fmt.Println("Usage: config <width|height|fps|device> <value>")
					continue
				}

				if capture.IsRunning() {
					fmt.Println("[WARN] Stop capture first before changing config")
					continue
				}

				param := strings.ToLower(parts[1])
				value := parts[2]

				switch param {
				case "width":
					if w, err := strconv.Atoi(value); err == nil && w > 0 {
						config.Width = w
						fmt.Printf("[INFO] Width set to %d\n", w)
					} else {
						fmt.Println("[ERROR] Invalid width value")
					}
				case "height":
					if h, err := strconv.Atoi(value); err == nil && h > 0 {
						config.Height = h
						fmt.Printf("[INFO] Height set to %d\n", h)
					} else {
						fmt.Println("[ERROR] Invalid height value")
					}
				case "fps":
					if f, err := strconv.Atoi(value); err == nil && f > 0 {
						config.FPS = f
						fmt.Printf("[INFO] FPS set to %d\n", f)
					} else {
						fmt.Println("[ERROR] Invalid FPS value")
					}
				case "device":
					config.DeviceID = value
					fmt.Printf("[INFO] Device set to %s\n", value)
				default:
					fmt.Println("[ERROR] Unknown config parameter")
				}

				// 重新创建捕获器
				capture = NewFFmpegVideoCapture(config)

			case "test":
				duration := 10 * time.Second
				if len(parts) >= 2 {
					if d, err := strconv.Atoi(parts[1]); err == nil && d > 0 {
						duration = time.Duration(d) * time.Second
					}
				}

				fmt.Printf("[INFO] 🧪 Running %v test capture...\n", duration)

				if err := capture.Start(); err != nil {
					log.Printf("[ERROR] Failed to start test: %v", err)
					continue
				}

				// 等待指定时间
				time.Sleep(duration)

				capture.Stop()
				fmt.Println("[INFO] ✅ Test completed")
				fmt.Println("[INFO] 📁 Check ffmpeg_debug/ directory for captured frames")

			case "help":
				showMenu()

			case "quit", "exit":
				if capture.IsRunning() {
					capture.Stop()
				}
				fmt.Println("[INFO] 👋 Goodbye!")
				return

			default:
				fmt.Printf("[ERROR] Unknown command: %s. Type 'help' for available commands.\n", cmd)
			}
		}
	}
}

func checkFFmpegInstallation() error {
	fmt.Println("[INFO] 🔍 Checking FFmpeg installation...")

	// 检查FFmpeg是否可用
	if _, err := exec.LookPath("ffmpeg"); err != nil {
		return fmt.Errorf("FFmpeg not found. Please install FFmpeg first:\n" +
			"  macOS: brew install ffmpeg\n" +
			"  Ubuntu: sudo apt install ffmpeg\n" +
			"  Windows: Download from https://ffmpeg.org/")
	}

	fmt.Println("[INFO] ✅ FFmpeg found")
	return nil
}

func showMenu() {
	fmt.Println("\n=== FFmpeg Video Capture Commands ===")
	fmt.Println("list/devices     - List available video devices")
	fmt.Println("start           - Start video capture")
	fmt.Println("stop            - Stop video capture")
	fmt.Println("status          - Show current status")
	fmt.Println("config <param> <value> - Configure capture settings")
	fmt.Println("                 params: width, height, fps, device")
	fmt.Println("test [seconds]  - Run test capture for N seconds (default: 10)")
	fmt.Println("help            - Show this menu")
	fmt.Println("quit/exit       - Exit program")
	fmt.Println("=====================================")
	fmt.Println("")
	fmt.Println("💡 Tips:")
	fmt.Println("1. Run 'list' first to see available cameras")
	fmt.Println("2. Use 'config device 0' to select camera 0")
	fmt.Println("3. Run 'test 15' to capture for 15 seconds")
	fmt.Println("4. Check ffmpeg_debug/ for saved frames")
	fmt.Println("")
}
