# 简单LCD测试指南

## 🎯 **目的**

由于编译环境的问题，我们提供一个更简单的方法来测试LCD渲染功能。

## 🔧 **方法1：修改现有WebRTC代码进行测试**

### 步骤1：修改media_sys.c，添加测试函数

在`solutions/videocall_demo/main/media_sys.c`文件末尾添加以下代码：

```c
// 添加到文件末尾
#include "esp_timer.h"

// 简单的LCD测试函数
int test_lcd_render_simple(void)
{
    ESP_LOGI("LCD_TEST", "=== 开始简单LCD测试 ===");
    
    if (player_sys.player == NULL) {
        ESP_LOGE("LCD_TEST", "Player system not initialized");
        return -1;
    }

    // 创建RGB565测试数据
    const int width = 1024;
    const int height = 600;
    const int buffer_size = width * height * 2; // RGB565 = 2 bytes per pixel
    
    uint16_t *test_buffer = (uint16_t *)malloc(buffer_size);
    if (test_buffer == NULL) {
        ESP_LOGE("LCD_TEST", "Failed to allocate test buffer");
        return -1;
    }

    // 测试不同颜色
    uint16_t colors[] = {
        0xF800, // 红色
        0x07E0, // 绿色  
        0x001F, // 蓝色
        0xFFFF, // 白色
    };
    
    const char* color_names[] = {"红色", "绿色", "蓝色", "白色"};

    for (int i = 0; i < 4; i++) {
        ESP_LOGI("LCD_TEST", "测试颜色: %s", color_names[i]);
        
        // 填充纯色
        for (int j = 0; j < width * height; j++) {
            test_buffer[j] = colors[i];
        }

        // 创建视频帧数据
        av_render_video_frame_t video_frame = {
            .data = (uint8_t *)test_buffer,
            .size = buffer_size,
            .pts = esp_timer_get_time(),
        };

        // 发送到播放器
        int ret = av_render_write_video(player_sys.player, &video_frame);
        if (ret == 0) {
            ESP_LOGI("LCD_TEST", "视频帧发送成功");
        } else {
            ESP_LOGE("LCD_TEST", "视频帧发送失败: %d", ret);
        }

        // 等待3秒
        vTaskDelay(pdMS_TO_TICKS(3000));
    }

    free(test_buffer);
    ESP_LOGI("LCD_TEST", "=== LCD测试完成 ===");
    return 0;
}
```

### 步骤2：在main.c中添加测试命令

在`solutions/videocall_demo/main/main.c`中添加：

```c
// 在其他CLI函数后面添加
static int lcd_simple_test_cli(int argc, char **argv)
{
    ESP_LOGI(TAG, "启动简单LCD测试...");
    test_lcd_render_simple();
    return 0;
}

// 在cmds数组中添加新命令
{
    .command = "lcd_simple",
    .help = "Simple LCD render test\r\n",
    .func = lcd_simple_test_cli,
},
```

### 步骤3：编译和测试

```bash
# 在ESP-IDF环境中
cd solutions/videocall_demo
idf.py build
idf.py flash monitor
```

### 步骤4：运行测试

```
esp> lcd_simple
```

## 🔧 **方法2：直接在WebRTC连接中测试**

如果上面的方法不行，可以临时修改WebRTC接收到的数据：

### 修改video_decoder.c

在`components/av_render/src/video_decoder.c`的解码函数中，临时替换解码数据为测试图案：

```c
// 在video_decoder_decode函数中，解码成功后添加测试代码
static int video_decoder_decode(video_decoder_handle_t decoder, uint8_t *data, int size, av_render_video_frame_t *frame)
{
    // ... 原有代码 ...
    
    // 临时测试：用纯色替换解码数据
    static int test_color_index = 0;
    uint16_t test_colors[] = {0xF800, 0x07E0, 0x001F, 0xFFFF}; // 红绿蓝白
    
    if (frame->data && frame->size >= 1024 * 600 * 2) {
        uint16_t *rgb_data = (uint16_t *)frame->data;
        uint16_t test_color = test_colors[test_color_index % 4];
        
        // 每100帧切换一次颜色
        static int frame_count = 0;
        if (++frame_count >= 100) {
            frame_count = 0;
            test_color_index++;
            ESP_LOGI("VID_DEC", "切换测试颜色: %d", test_color_index % 4);
        }
        
        // 填充测试颜色
        for (int i = 0; i < 1024 * 600; i++) {
            rgb_data[i] = test_color;
        }
    }
    
    return ret;
}
```

## 🎯 **预期结果**

### ✅ **如果LCD正常工作**
- 屏幕会显示纯色（红、绿、蓝、白）
- 每3秒或每100帧切换颜色
- 控制台输出成功信息

### ❌ **如果LCD有问题**
- 屏幕仍然黑屏
- 控制台可能输出错误信息

## 🔍 **故障排除**

### **如果方法1失败**
1. 检查`player_sys.player`是否已初始化
2. 确认`av_render_write_video`函数是否存在
3. 检查内存分配是否成功

### **如果方法2失败**
1. 确认修改的位置是否正确
2. 检查解码数据的格式和大小
3. 验证RGB565数据是否正确填充

## 📝 **调试信息**

关键日志信息：
- `LCD_TEST: 测试颜色: xxx` - 测试开始
- `LCD_TEST: 视频帧发送成功` - 数据发送成功
- `LCD_RENDER: fps: x` - LCD渲染帧率
- `VID_DEC: 切换测试颜色: x` - 颜色切换

## 🎯 **测试目标**

这些简单测试可以帮助确定：

1. **av_render系统是否工作** - 通过方法1测试
2. **LCD渲染管道是否正常** - 通过方法2测试  
3. **颜色显示是否正确** - 通过不同颜色测试
4. **数据流是否到达LCD** - 通过日志确认

如果这些简单测试都能正常显示颜色，那么问题就在JPEG解码或WebRTC数据传输环节。如果仍然黑屏，那么问题在LCD渲染系统本身。
