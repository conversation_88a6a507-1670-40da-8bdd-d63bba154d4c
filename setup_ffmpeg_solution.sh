#!/bin/bash

echo "🎥 FFmpeg Video Capture Solution Setup"
echo "======================================"

# 检测操作系统
OS="unknown"
if [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macOS"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="Linux"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    OS="Windows"
fi

echo "🖥️  Detected OS: $OS"

# 检查FFmpeg是否已安装
echo ""
echo "🔍 Checking FFmpeg installation..."
if command -v ffmpeg &> /dev/null; then
    echo "✅ FFmpeg is already installed"
    ffmpeg -version | head -1
else
    echo "❌ FFmpeg not found"
    echo ""
    echo "📦 Installing FFmpeg..."
    
    case $OS in
        "macOS")
            if command -v brew &> /dev/null; then
                echo "Installing via Homebrew..."
                brew install ffmpeg
            else
                echo "❌ Homebrew not found. Please install Homebrew first:"
                echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                echo "   Then run: brew install ffmpeg"
                exit 1
            fi
            ;;
        "Linux")
            if command -v apt &> /dev/null; then
                echo "Installing via apt..."
                sudo apt update
                sudo apt install -y ffmpeg
            elif command -v yum &> /dev/null; then
                echo "Installing via yum..."
                sudo yum install -y ffmpeg
            else
                echo "❌ Package manager not found. Please install FFmpeg manually:"
                echo "   Ubuntu/Debian: sudo apt install ffmpeg"
                echo "   CentOS/RHEL: sudo yum install ffmpeg"
                exit 1
            fi
            ;;
        "Windows")
            echo "❌ Please install FFmpeg manually on Windows:"
            echo "   1. Download from: https://ffmpeg.org/download.html"
            echo "   2. Extract to C:\\ffmpeg"
            echo "   3. Add C:\\ffmpeg\\bin to your PATH"
            exit 1
            ;;
        *)
            echo "❌ Unsupported OS. Please install FFmpeg manually."
            exit 1
            ;;
    esac
fi

echo ""
echo "🧪 Testing FFmpeg camera access..."

# 编译简单测试程序
echo "📦 Building test program..."
go build -o ffmpeg_test simple_ffmpeg_test.go

if [ $? -ne 0 ]; then
    echo "❌ Failed to build test program"
    exit 1
fi

echo "✅ Test program built successfully"

echo ""
echo "🎬 Running camera test..."
./ffmpeg_test

echo ""
echo "📊 Test Results Analysis:"
echo "========================"

# 检查输出文件
if [ -f "test_output.mp4" ]; then
    echo "✅ Video file created: test_output.mp4"
    
    # 获取文件大小
    if [[ "$OS" == "macOS" ]]; then
        size=$(stat -f%z test_output.mp4)
    else
        size=$(stat -c%s test_output.mp4)
    fi
    
    echo "   File size: $size bytes"
    
    if [ $size -gt 1000 ]; then
        echo "   ✅ File size looks good (>1KB)"
    else
        echo "   ⚠️  File size is very small, might be empty"
    fi
else
    echo "❌ Video file not created"
fi

if [ -f "test_frame.jpg" ]; then
    echo "✅ Frame image created: test_frame.jpg"
    
    # 获取文件大小
    if [[ "$OS" == "macOS" ]]; then
        size=$(stat -f%z test_frame.jpg)
    else
        size=$(stat -c%s test_frame.jpg)
    fi
    
    echo "   File size: $size bytes"
    
    if [ $size -gt 1000 ]; then
        echo "   ✅ Image size looks good (>1KB)"
        echo "   💡 You can open this file to see if camera is working"
    else
        echo "   ⚠️  Image size is very small, might be corrupted"
    fi
else
    echo "❌ Frame image not created"
fi

echo ""
echo "🎯 Next Steps:"
echo "============="

if [ -f "test_output.mp4" ] && [ -f "test_frame.jpg" ]; then
    echo "🎉 SUCCESS! FFmpeg can access your camera!"
    echo ""
    echo "✅ Camera is working with FFmpeg"
    echo "✅ Video capture is functional"
    echo "✅ Frame capture is functional"
    echo ""
    echo "🔧 Now we can integrate FFmpeg into the WebRTC door station:"
    echo "1. Replace pion/mediadevices with FFmpeg capture"
    echo "2. Use FFmpeg to get real camera frames"
    echo "3. Encode frames to H.264 for WebRTC"
    echo "4. Send real video data to browser"
    echo ""
    echo "📁 Files created:"
    echo "   - test_output.mp4 (10-second video)"
    echo "   - test_frame.jpg (single frame)"
    echo ""
    echo "💡 Open these files to verify camera is working correctly!"
    
else
    echo "❌ CAMERA ACCESS FAILED"
    echo ""
    echo "🔍 Possible issues:"
    echo "1. Camera permissions not granted"
    echo "2. Camera in use by another application"
    echo "3. No camera device available"
    echo "4. FFmpeg configuration issues"
    echo ""
    echo "🛠️  Troubleshooting:"
    echo "1. Check camera permissions in System Preferences (macOS)"
    echo "2. Close other apps that might use camera (Zoom, Skype, etc.)"
    echo "3. Try different camera device IDs (0, 1, 2...)"
    echo "4. Check FFmpeg device list output above"
fi

echo ""
echo "🚀 Ready for WebRTC Integration!"
echo ""
echo "The FFmpeg solution will solve the black screen issue by:"
echo "✅ Directly accessing camera hardware"
echo "✅ Getting real video frames (not test patterns)"
echo "✅ Proper H.264 encoding for WebRTC"
echo "✅ Reliable cross-platform camera support"
