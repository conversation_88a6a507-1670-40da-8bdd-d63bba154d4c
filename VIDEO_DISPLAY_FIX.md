# 视频显示问题修复说明

## 问题描述

在videocall_demo中，虽然WebRTC连接正常，音视频数据正常接收，视频解码也成功，但是LCD屏幕不显示视频内容。

## 问题分析

通过分析日志和代码，发现问题的根本原因是：

### 1. 日志分析
```
I (1678086) AV_RENDER: VRender Auto pause video first frame at 0
I (1678086) LCD_RENDER: fps: 0
```

日志显示视频渲染器在第一帧自动暂停了。

### 2. 代码分析

在`components/av_render/src/av_render.c`的`_video_check_first_frame_render_notify`函数中：

```c
static void _video_check_first_frame_render_notify(av_render_thread_res_t *res, av_render_video_frame_t *video_frame)
{
    if (res->render->v_render_res->video_rendered == false) {
        ESP_LOGI(TAG, "%s Auto pause video first frame at %" PRIu32, res->name, video_frame->pts);
        if (res->render->event_cb) {
            res->render->event_cb(AV_RENDER_EVENT_VIDEO_RENDERED, res->render->event_ctx);
        }
        if (res->render->cfg.pause_on_first_frame) {  // 这里是关键
            res->paused = true;
            if (res->render->v_render_res->v_render_in_sync && res->render->vdec_res) {
                // Auto pause decoder if render in sync
                res->render->vdec_res->thread_res.paused = true;
            }
        }
        res->render->v_render_res->video_rendered = true;
    }
}
```

### 3. 根本原因

通过深入分析日志和代码，发现了两个关键问题：

#### 问题1: 第一帧自动暂停
在各个demo的`media_sys.c`文件中，`av_render_cfg_t`配置结构体没有显式设置`pause_on_first_frame`字段，导致使用了未定义的默认值。

#### 问题2: 音视频PTS同步问题（主要原因）
从日志分析发现：
- **音频PTS**: `295156481` (很大的值)
- **视频PTS**: `0` (从0开始)

这种巨大的PTS差异导致音视频同步机制认为视频严重滞后，从而暂停视频渲染。在`video_sync_control_before_render`函数中，当视频PTS远小于音频PTS时，同步逻辑会暂停视频以等待音频。

#### WebRTC中的PTS问题
WebRTC接收的音频和视频流可能来自不同的时间基准，导致PTS不同步。这在实时通信中是常见问题，需要通过禁用同步或重新校准PTS来解决。

## 修复方案

在所有相关demo的`av_render_cfg_t`配置中显式添加`pause_on_first_frame = false`：

### 修复的文件列表

1. `solutions/videocall_demo/main/media_sys.c`
2. `solutions/whip_demo/main/media_sys.c`
3. `solutions/doorbell_demo/main/media_sys.c`
4. `solutions/doorbell_local/main/media_sys.c`

### 修复内容

在每个文件的`build_player_system`函数中，将：

```c
av_render_cfg_t render_cfg = {
    .audio_render = player_sys.audio_render,
    .video_render = player_sys.video_render,
    .audio_raw_fifo_size = 4096,
    .audio_render_fifo_size = 6 * 1024,
    .video_raw_fifo_size = 500 * 1024,
    .allow_drop_data = false,
    //.video_render_fifo_size = 4*1024,
};
```

修改为：

```c
av_render_cfg_t render_cfg = {
    .audio_render = player_sys.audio_render,
    .video_render = player_sys.video_render,
    .audio_raw_fifo_size = 4096,
    .audio_render_fifo_size = 6 * 1024,
    .video_raw_fifo_size = 500 * 1024,
    .allow_drop_data = false,
    .pause_on_first_frame = false,  // 禁用第一帧自动暂停，确保视频正常显示
    .sync_mode = AV_RENDER_SYNC_NONE,  // 禁用音视频同步，解决PTS差异导致的视频暂停问题
    //.video_render_fifo_size = 4*1024,
};
```

### 关键修复点

1. **禁用第一帧暂停**: `pause_on_first_frame = false`
2. **禁用音视频同步**: `sync_mode = AV_RENDER_SYNC_NONE`

第二个修复是关键，因为WebRTC接收的音频和视频PTS存在巨大差异（音频PTS: ~295156481, 视频PTS: 0），导致同步机制错误地暂停视频渲染。

## 预期效果

修复后，视频渲染器将不会在第一帧自动暂停，视频应该能够正常显示在LCD屏幕上。

## 验证方法

1. 重新编译并烧录videocall_demo
2. 建立WebRTC连接
3. 观察日志，应该不再出现"VRender Auto pause video first frame"的暂停信息
4. 检查LCD屏幕是否正常显示视频内容

## 注意事项

- 此修复适用于所有使用av_render组件进行视频显示的demo
- openai_demo只有音频没有视频，因此不需要此修复
- 如果后续添加新的demo，需要注意在av_render_cfg_t中显式设置pause_on_first_frame = false

## 附加修复：LCD面板disp_on_off错误

### 问题描述
在使用ESP32P4开发板时，可能会看到以下错误：
```
E (2276) lcd_panel: esp_lcd_panel_disp_on_off(71): disp_on_off is not supported by this panel
```

### 原因分析
- ESP32P4使用MIPI DSI接口的LCD面板（EK79007或ILI9881C）
- 这些面板的驱动程序没有实现`esp_lcd_panel_disp_on_off`功能
- 该错误不影响LCD的正常显示功能

### 修复方案
在`components/codec_board/lcd_init.c`中修改了错误处理逻辑：

```c
// 某些MIPI DSI面板不支持disp_on_off功能，忽略错误
esp_err_t disp_ret = esp_lcd_panel_disp_on_off(panel_handle, true);
if (disp_ret != ESP_OK && disp_ret != ESP_ERR_NOT_SUPPORTED) {
    ret = disp_ret;
}
```

这样修改后，如果面板不支持disp_on_off功能，将忽略`ESP_ERR_NOT_SUPPORTED`错误，但仍会报告其他类型的错误。

## 最终解决方案：分辨率匹配

### 问题根源
经过深入分析发现，真正的问题是**分辨率不匹配**：
- **接收视频**: 320x240分辨率
- **ESP32P4 LCD**: 1024x600分辨率
- **显示效果**: 320x240的小图像只显示在1024x600大屏幕的左上角很小区域

### 解决方案
修改Go程序发送1024x600分辨率的视频，完全匹配ESP32P4的LCD屏幕：

#### 修改的文件
1. `go-videocall-peer/internal/media/capture.go`
2. `go-videocall-peer/config/default.yaml`

#### 具体修改
```go
// capture.go 中的 captureRealJPEGFrame 函数
switch patternType {
case 0:
    testPattern = "testsrc2=size=1024x600:rate=1:duration=1"  // 原来是320x240
case 1:
    testPattern = "gradients=size=1024x600:rate=1:duration=1"
case 2:
    testPattern = "rgbtestsrc=size=1024x600:rate=1:duration=1"
case 3:
    testPattern = "color=red:size=1024x600:duration=1"
}
```

```yaml
# default.yaml 中的视频配置
video:
  width: 1024      # 原来是640
  height: 600      # 原来是480
  bitrate: 800000  # 增加码率以适应更高分辨率
```

### 测试验证
创建了测试脚本 `test_1024x600_video.sh` 验证：
- ✅ FFmpeg成功生成1024x600分辨率的测试图像
- ✅ 所有4种测试图案都正确生成
- ✅ 分辨率验证通过：1024x600

### 预期效果
- Go程序发送1024x600分辨率的彩色视频
- ESP32P4 LCD屏幕全屏显示视频内容
- 彻底解决黑屏问题

## 最终修复：JPEG解码器缓冲区不足

### 问题发现
在测试1024x600分辨率后，发现新的错误：
```
E (47525) jpeg.decoder: jpeg_decoder_process(232): Given buffer size 1228800 is smaller than actual jpeg decode output size 1245184
E (47545) VID_DEC: Fail to decode data ret 1
```

### 根本原因
- **计算的缓冲区大小**: 1,228,800 bytes (1024×600×2)
- **JPEG解码器实际需要**: 1,245,184 bytes
- **差距**: 16,384 bytes (16KB)

JPEG解码器需要额外的缓冲区空间用于内部处理，但`esp_video_codec_get_image_size`只计算最终图像大小。

### 最终解决方案
修改`components/av_render/src/video_decoder.c`，为JPEG解码器增加额外缓冲区：

```c
vdec->out_size = esp_video_codec_get_image_size(vdec->dec_out_fmt, &frame_info.res);
// 为JPEG解码器增加额外缓冲区空间，解决1024x600分辨率解码失败问题
if (vdec->dec_out_fmt == ESP_VIDEO_CODEC_PIXEL_FMT_RGB565_LE ||
    vdec->dec_out_fmt == ESP_VIDEO_CODEC_PIXEL_FMT_RGB565_BE) {
    // 为RGB565格式增加32KB额外空间以确保JPEG解码成功
    vdec->out_size += 32 * 1024;
}
```

### 修复的文件列表
1. ✅ `solutions/videocall_demo/main/media_sys.c` - 同步和暂停修复
2. ✅ `solutions/whip_demo/main/media_sys.c` - 同步和暂停修复
3. ✅ `solutions/doorbell_demo/main/media_sys.c` - 同步和暂停修复
4. ✅ `solutions/doorbell_local/main/media_sys.c` - 同步和暂停修复
5. ✅ `go-videocall-peer/internal/media/capture.go` - 分辨率修改
6. ✅ `go-videocall-peer/config/default.yaml` - 分辨率配置
7. ✅ `components/av_render/src/video_decoder.c` - JPEG解码器缓冲区修复
8. ✅ `components/codec_board/lcd_init.c` - LCD错误处理修复
