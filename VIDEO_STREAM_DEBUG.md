# 🎥 视频流调试指南

## 🔍 黑屏问题诊断

浏览器显示黑屏可能的原因：

### 1. 视频流获取问题
- **摄像头权限**：macOS可能需要授权摄像头访问
- **设备占用**：其他应用可能正在使用摄像头
- **编码器问题**：H.264编码器可能有问题

### 2. WebRTC传输问题
- **编解码器不匹配**：浏览器不支持我们的H.264配置
- **网络问题**：ICE连接失败
- **SDP协商问题**：offer/answer不兼容

### 3. 浏览器端问题
- **解码器问题**：浏览器无法解码H.264流
- **渲染问题**：video元素没有正确显示

## 🔧 调试步骤

### 步骤1: 检查摄像头访问
```bash
# 启动程序并观察媒体设备日志
./door-station-esp32 https://webrtc.espressif.com video_debug_test
```

**关键日志**：
```
[DEBUG] Video constraints: 640x480@30fps
[DEBUG] Audio constraints: 48000Hz, 2 channels
[INFO] Media stream created with 1 video tracks and 1 audio tracks
[DEBUG] Video Track 0: ID=xxx, Kind=video
[DEBUG] Audio Track 0: ID=xxx, Kind=audio
```

### 步骤2: 检查WebRTC连接
**成功的连接日志**：
```
[INFO] Signaling state changed: have-local-offer
[INFO] ICE connection state changed: checking
[INFO] ICE connection state changed: connected
[INFO] Connection state changed: connected
[INFO] 📡 Data channel opened - connection is working!
```

### 步骤3: 检查浏览器控制台
打开浏览器开发者工具，查看：
1. **Console错误**：是否有JavaScript错误
2. **Network面板**：WebSocket和HTTP请求是否正常
3. **Media面板**：是否接收到视频流

## 🚀 测试方案

### 方案1: 基础连接测试
```bash
# 1. 启动Go程序（作为initiator）
./door-station-esp32 https://webrtc.espressif.com video_debug_test

# 2. 连接房间
door-station> connect video_debug_test

# 3. 打开浏览器
# https://webrtc.espressif.com/video_debug_test
```

### 方案2: 简化编解码器测试
如果基础测试失败，可能需要：
1. **禁用H.264**：只使用VP8/VP9
2. **降低分辨率**：使用320x240
3. **降低帧率**：使用15fps

## 🎯 预期日志分析

### 正常的视频流日志：
```
[DEBUG] Video constraints: 640x480@30fps
[INFO] Media stream created with 1 video tracks and 1 audio tracks
[DEBUG] Video Track 0: ID=xxx, Kind=video
[INFO] Added video track: xxx
[INFO] Offer sent with real media tracks
[INFO] ICE connection state changed: connected
[INFO] 📡 Data channel opened - connection is working!
```

### 问题诊断：
- **没有Video Track**：摄像头获取失败
- **ICE never connects**：网络连接问题
- **No data channel open**：WebRTC连接未建立
- **Offer sent but no answer**：协议问题

## 💡 常见解决方案

### 1. macOS摄像头权限
```bash
# 检查系统偏好设置 > 安全性与隐私 > 摄像头
# 确保终端或Go程序有摄像头访问权限
```

### 2. 强制使用特定摄像头
```bash
# 在配置中指定摄像头设备ID
# 或者使用系统默认摄像头
```

### 3. 降级编解码器
如果H.264有问题，可以尝试：
- VP8 (更广泛支持)
- VP9 (现代浏览器)
- 降低编码质量

## 🔍 高级调试

### 启用详细日志
在Go程序中添加更多调试信息：
- RTP包发送统计
- 编码器状态
- 网络统计

### 浏览器调试
```javascript
// 在浏览器控制台中检查
pc.getStats().then(stats => {
    stats.forEach(report => {
        if (report.type === 'inbound-rtp' && report.kind === 'video') {
            console.log('Video stats:', report);
        }
    });
});
```

## 🎉 成功标志

视频流正常工作的标志：
- ✅ 摄像头成功获取
- ✅ 视频轨道添加到PeerConnection
- ✅ ICE连接建立
- ✅ 数据通道打开
- ✅ 浏览器video元素显示画面
- ✅ 浏览器控制台无错误

如果所有步骤都正常但仍然黑屏，问题可能在于：
1. **编解码器兼容性**
2. **浏览器的视频渲染**
3. **网络带宽限制**

让我们按照这个指南逐步调试！
