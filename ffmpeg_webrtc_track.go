package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/pion/webrtc/v4"
	"github.com/pion/webrtc/v4/pkg/media"
)

// FFmpeg WebRTC视频轨道
type FFmpegWebRTCTrack struct {
	track       *webrtc.TrackLocalStaticSample
	capture     *FFmpegVideoCapture
	isStreaming bool
	mutex       sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
	encoder     *H264Encoder
}

// H.264编码器 (简化版本)
type H264Encoder struct {
	width  int
	height int
	fps    int
}

// 创建H.264编码器
func NewH264Encoder(width, height, fps int) *H264Encoder {
	return &H264Encoder{
		width:  width,
		height: height,
		fps:    fps,
	}
}

// 编码YUV数据为H.264 (这里使用模拟数据，实际需要集成libx264)
func (e *H264Encoder) Encode(yuvData []byte) ([]byte, error) {
	// 这里应该使用真正的H.264编码器
	// 为了演示，我们返回一个简单的H.264 NAL单元
	
	// H.264 SPS (Sequence Parameter Set) - 简化版本
	sps := []byte{
		0x00, 0x00, 0x00, 0x01, // NAL start code
		0x67, // NAL header (SPS)
		0x42, 0x00, 0x1e, // profile_idc, constraint flags, level_idc
		0xff, 0xe1, 0x00, 0x18, // more SPS data...
	}
	
	// H.264 PPS (Picture Parameter Set) - 简化版本
	pps := []byte{
		0x00, 0x00, 0x00, 0x01, // NAL start code
		0x68, // NAL header (PPS)
		0xce, 0x3c, 0x80, // PPS data...
	}
	
	// H.264 IDR frame - 简化版本
	idr := []byte{
		0x00, 0x00, 0x00, 0x01, // NAL start code
		0x65, // NAL header (IDR slice)
	}
	
	// 添加一些模拟的压缩视频数据
	frameData := make([]byte, len(yuvData)/10) // 压缩比约10:1
	for i := range frameData {
		frameData[i] = byte(i % 256)
	}
	
	// 组合完整的H.264帧
	result := make([]byte, 0, len(sps)+len(pps)+len(idr)+len(frameData))
	result = append(result, sps...)
	result = append(result, pps...)
	result = append(result, idr...)
	result = append(result, frameData...)
	
	return result, nil
}

// 创建FFmpeg WebRTC轨道
func NewFFmpegWebRTCTrack(config FFmpegConfig) (*FFmpegWebRTCTrack, error) {
	// 创建WebRTC视频轨道
	track, err := webrtc.NewTrackLocalStaticSample(
		webrtc.RTPCodecCapability{MimeType: webrtc.MimeTypeH264},
		"video",
		"ffmpeg-video",
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create video track: %v", err)
	}
	
	// 创建FFmpeg捕获器
	capture := NewFFmpegVideoCapture(config)
	
	// 创建H.264编码器
	encoder := NewH264Encoder(config.Width, config.Height, config.FPS)
	
	ctx, cancel := context.WithCancel(context.Background())
	
	ffmpegTrack := &FFmpegWebRTCTrack{
		track:   track,
		capture: capture,
		ctx:     ctx,
		cancel:  cancel,
		encoder: encoder,
	}
	
	// 设置帧回调
	capture.SetFrameCallback(ffmpegTrack.onFrame)
	
	return ffmpegTrack, nil
}

// 处理接收到的帧
func (f *FFmpegWebRTCTrack) onFrame(yuvData []byte) {
	f.mutex.RLock()
	streaming := f.isStreaming
	f.mutex.RUnlock()
	
	if !streaming {
		return
	}
	
	// 编码为H.264
	h264Data, err := f.encoder.Encode(yuvData)
	if err != nil {
		log.Printf("[ERROR] Failed to encode frame: %v", err)
		return
	}
	
	// 发送到WebRTC轨道
	sample := media.Sample{
		Data:     h264Data,
		Duration: time.Second / time.Duration(f.encoder.fps),
	}
	
	if err := f.track.WriteSample(sample); err != nil {
		log.Printf("[ERROR] Failed to write sample: %v", err)
	}
}

// 获取WebRTC轨道
func (f *FFmpegWebRTCTrack) GetTrack() *webrtc.TrackLocalStaticSample {
	return f.track
}

// 开始流式传输
func (f *FFmpegWebRTCTrack) StartStreaming() error {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	
	if f.isStreaming {
		return fmt.Errorf("already streaming")
	}
	
	log.Println("[INFO] 🎬 Starting FFmpeg video streaming...")
	
	// 启动FFmpeg捕获
	if err := f.capture.Start(); err != nil {
		return fmt.Errorf("failed to start capture: %v", err)
	}
	
	f.isStreaming = true
	log.Println("[INFO] ✅ FFmpeg video streaming started")
	
	return nil
}

// 停止流式传输
func (f *FFmpegWebRTCTrack) StopStreaming() {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	
	if !f.isStreaming {
		return
	}
	
	log.Println("[INFO] 🛑 Stopping FFmpeg video streaming...")
	
	f.isStreaming = false
	f.capture.Stop()
	f.cancel()
	
	log.Println("[INFO] ✅ FFmpeg video streaming stopped")
}

// 列出可用设备
func (f *FFmpegWebRTCTrack) ListDevices() error {
	return f.capture.ListDevices()
}

// 检查是否正在流式传输
func (f *FFmpegWebRTCTrack) IsStreaming() bool {
	f.mutex.RLock()
	defer f.mutex.RUnlock()
	return f.isStreaming
}
