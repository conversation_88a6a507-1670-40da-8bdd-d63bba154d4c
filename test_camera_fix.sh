#!/bin/bash

# 摄像头捕获修复测试脚本

echo "=== 摄像头捕获修复测试 ==="
echo

echo "🔧 修复内容:"
echo "1. ✅ 控制帧率，避免过快读取摄像头"
echo "2. ✅ 智能错误处理，减少日志刷屏"
echo "3. ✅ 根据错误类型调整等待时间"
echo "4. ✅ 错误计数和频率控制"
echo

echo "📹 摄像头配置:"
echo "- 帧率控制: 15 FPS (66ms间隔)"
echo "- 错误处理: 5秒内错误汇总显示"
echo "- 资源不可用: 50ms等待重试"
echo "- 其他错误: 200ms等待重试"
echo

echo "🚀 启动程序测试..."
echo

echo "预期改进:"
echo "✅ 不再有连续的错误日志刷屏"
echo "✅ 摄像头读取速度合理"
echo "✅ 错误日志每5秒汇总一次"
echo "✅ 成功捕获时重置错误计数"
echo

# 清理调试文件
rm -f debug/*.jpg debug/*.mjpeg debug/*.pcma debug/*.h264 debug/*.log

echo "✅ 已清理调试文件"
echo

echo "请按以下步骤测试:"
echo "1. 程序启动后，输入: join d0002"
echo "2. 启用监控: monitor"
echo "3. 在ESP32上: join d0002"
echo "4. 在ESP32上: b"
echo "5. 在程序中: accept"
echo

echo "观察日志变化:"
echo "- 应该看到: [ASTIAV] 捕获循环已启动"
echo "- 如果有错误，应该是汇总显示，不是连续刷屏"
echo "- 成功时应该看到: [ASTIAV] 已捕获 30 帧"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 检查结果
if ls debug/astiav_frame_*.jpg 1> /dev/null 2>&1; then
    echo "🎉 AstiAV摄像头捕获成功!"
    for file in debug/astiav_frame_*.jpg; do
        size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
        echo "  📸 $file (${size} bytes)"
    done
else
    echo "⚠️  AstiAV摄像头捕获可能失败，检查是否使用了备用方案"
fi

echo
echo "修复说明:"
echo "- 帧率控制避免了过快读取摄像头资源"
echo "- 智能错误处理减少了日志噪音"
echo "- 不同错误类型使用不同的重试策略"
echo "- 错误日志汇总显示，便于问题诊断"
