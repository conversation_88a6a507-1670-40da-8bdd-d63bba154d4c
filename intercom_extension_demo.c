/* 门禁分机 WebRTC 应用代码
   基于doorbell_demo修改，用于接收视频流
*/

#include "esp_webrtc.h"
#include "media_lib_os.h"
#include "driver/gpio.h"
#include "common.h"
#include "esp_log.h"
#include "esp_webrtc_defaults.h"
#include "esp_peer_default.h"

#define TAG "INTERCOM_EXT"

// 自定义命令 (与门禁主机通信)
#define INTERCOM_CALL_REQUEST_CMD    "CALL_REQUEST"      // 门禁主机请求呼叫
#define INTERCOM_CALL_ACCEPT_CMD     "CALL_ACCEPT"       // 分机接听
#define INTERCOM_CALL_REJECT_CMD     "CALL_REJECT"       // 分机拒绝
#define INTERCOM_CALL_HANGUP_CMD     "CALL_HANGUP"       // 挂断通话
#define INTERCOM_DOOR_OPEN_CMD       "DOOR_OPEN"         // 开门命令

#define SAME_STR(a, b) (strncmp(a, b, sizeof(b) - 1) == 0)
#define SEND_CMD(webrtc, cmd) \
    esp_webrtc_send_custom_data(webrtc, ESP_WEBRTC_CUSTOM_DATA_VIA_SIGNALING, (uint8_t *)cmd, strlen(cmd))

typedef enum {
    INTERCOM_STATE_IDLE,           // 空闲状态
    INTERCOM_STATE_INCOMING_CALL,  // 来电状态  
    INTERCOM_STATE_CONNECTED,      // 通话中
} intercom_state_t;

static esp_webrtc_handle_t webrtc;
static intercom_state_t intercom_state = INTERCOM_STATE_IDLE;
static bool monitor_key;

// 分机命令处理函数
static int intercom_on_cmd(esp_webrtc_custom_data_via_t via, uint8_t *data, int size, void *ctx)
{
    if (size == 0 || webrtc == NULL) {
        return 0;
    }
    ESP_LOGI(TAG, "Receive command %.*s", size, (char *)data);
    const char *cmd = (const char *)data;
    
    if (SAME_STR(cmd, INTERCOM_CALL_REQUEST_CMD)) {
        // 门禁主机发起呼叫请求
        if (intercom_state == INTERCOM_STATE_IDLE) {
            intercom_state = INTERCOM_STATE_INCOMING_CALL;
            ESP_LOGI(TAG, "Incoming call from door station");
            // 播放来电铃声
            play_tone(INTERCOM_TONE_RING);
            // 自动接听或等待用户操作
            // 这里可以选择自动接听或等待按键
        }
    } else if (SAME_STR(cmd, INTERCOM_CALL_HANGUP_CMD)) {
        // 门禁主机挂断
        if (intercom_state == INTERCOM_STATE_CONNECTED) {
            intercom_state = INTERCOM_STATE_IDLE;
            esp_webrtc_enable_peer_connection(webrtc, false);
            stop_music();
        }
    }
    return 0;
}

static int webrtc_event_handler(esp_webrtc_event_t *event, void *ctx)
{
    if (event->type == ESP_WEBRTC_EVENT_CONNECTED) {
        intercom_state = INTERCOM_STATE_CONNECTED;
        stop_music(); // 停止铃声
        ESP_LOGI(TAG, "Call connected");
    } else if (event->type == ESP_WEBRTC_EVENT_CONNECT_FAILED || 
               event->type == ESP_WEBRTC_EVENT_DISCONNECTED) {
        intercom_state = INTERCOM_STATE_IDLE;
        ESP_LOGI(TAG, "Call disconnected");
    }
    return 0;
}

// 接听电话
void accept_call(void)
{
    if (intercom_state == INTERCOM_STATE_INCOMING_CALL) {
        SEND_CMD(webrtc, INTERCOM_CALL_ACCEPT_CMD);
        esp_webrtc_enable_peer_connection(webrtc, true);
        ESP_LOGI(TAG, "Call accepted");
    }
}

// 拒绝电话  
void reject_call(void)
{
    if (intercom_state == INTERCOM_STATE_INCOMING_CALL) {
        SEND_CMD(webrtc, INTERCOM_CALL_REJECT_CMD);
        intercom_state = INTERCOM_STATE_IDLE;
        stop_music();
        ESP_LOGI(TAG, "Call rejected");
    }
}

// 挂断电话
void hangup_call(void)
{
    if (intercom_state == INTERCOM_STATE_CONNECTED) {
        SEND_CMD(webrtc, INTERCOM_CALL_HANGUP_CMD);
        esp_webrtc_enable_peer_connection(webrtc, false);
        intercom_state = INTERCOM_STATE_IDLE;
        ESP_LOGI(TAG, "Call hung up");
    }
}

// 开门命令
void open_door(void)
{
    if (intercom_state == INTERCOM_STATE_CONNECTED) {
        SEND_CMD(webrtc, INTERCOM_DOOR_OPEN_CMD);
        ESP_LOGI(TAG, "Door open command sent");
    }
}

int start_intercom_webrtc(char *url)
{
    if (network_is_connected() == false) {
        ESP_LOGE(TAG, "Wifi not connected yet");
        return -1;
    }
    if (url[0] == 0) {
        ESP_LOGE(TAG, "Room Url not set yet");
        return -1;
    }
    if (webrtc) {
        esp_webrtc_close(webrtc);
        webrtc = NULL;
    }

    esp_peer_default_cfg_t peer_cfg = {
        .agent_recv_timeout = 500,
    };
    
    esp_webrtc_cfg_t cfg = {
        .peer_cfg = {
            .audio_info = {
#ifdef WEBRTC_SUPPORT_OPUS
                .codec = ESP_PEER_AUDIO_CODEC_OPUS,
                .sample_rate = 16000,
                .channel = 2,
#else
                .codec = ESP_PEER_AUDIO_CODEC_G711A,
#endif
            },
            .video_info = {
                .codec = ESP_PEER_VIDEO_CODEC_H264,
                .width = VIDEO_WIDTH,
                .height = VIDEO_HEIGHT,
                .fps = VIDEO_FPS,
            },
            .audio_dir = ESP_PEER_MEDIA_DIR_SEND_RECV,  // 双向音频
            .video_dir = ESP_PEER_MEDIA_DIR_RECV_ONLY,  // 只接收视频 ⭐ 关键修改
            .on_custom_data = intercom_on_cmd,
            .enable_data_channel = DATA_CHANNEL_ENABLED,
            .no_auto_reconnect = true, // 禁用自动连接
            .extra_cfg = &peer_cfg,
            .extra_size = sizeof(peer_cfg),
        },
        .signaling_cfg = {
            .signal_url = url,
        },
        .peer_impl = esp_peer_get_default_impl(),
        .signaling_impl = esp_signaling_get_apprtc_impl(),
    };
    
    int ret = esp_webrtc_open(&cfg, &webrtc);
    if (ret != 0) {
        ESP_LOGE(TAG, "Fail to open webrtc");
        return ret;
    }
    
    // 设置媒体提供者 (只需要音频捕获和视频播放)
    esp_webrtc_media_provider_t media_provider = {};
    media_sys_get_provider(&media_provider);
    esp_webrtc_set_media_provider(webrtc, &media_provider);

    // 设置事件处理器
    esp_webrtc_set_event_handler(webrtc, webrtc_event_handler, NULL);

    // 默认禁用自动连接
    esp_webrtc_enable_peer_connection(webrtc, false);

    // 启动webrtc
    ret = esp_webrtc_start(webrtc);
    if (ret != 0) {
        ESP_LOGE(TAG, "Fail to start webrtc");
    } else {
        ESP_LOGI(TAG, "Intercom extension ready");
    }
    return ret;
} 