#!/bin/bash

echo "🎥 Video Debugging Test Script"
echo "============================="

# 编译项目
echo "📦 Building project..."
go build -o door-station-esp32 main.go
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"

echo ""
echo "🔧 New Video Debugging Features:"
echo "✅ Real-time video statistics monitoring"
echo "✅ Video frame capture and saving as JPEG"
echo "✅ Interactive debug commands"
echo "✅ Automatic video track validation"
echo ""

echo "📋 Testing Process:"
echo "1. Connect to a room to initialize media stream"
echo "2. Run video debugging commands"
echo "3. Check for saved frames in video_debug/ directory"
echo "4. Analyze video statistics"
echo ""

echo "🚀 Test Steps:"
echo ""
echo "Step 1: Start the door station"
echo "   ./door-station-esp32 https://webrtc.espressif.com video_debug_test"
echo ""
echo "Step 2: Connect to room (this will auto-validate video)"
echo "   door-station> connect video_debug_test"
echo ""
echo "Step 3: Run video debugging commands:"
echo "   door-station> debug test 15     # Test capture for 15 seconds"
echo "   door-station> debug stats       # Show current statistics"
echo "   door-station> debug video       # Enable frame saving"
echo "   door-station> debug validate    # Validate video tracks"
echo ""
echo "Step 4: Check results:"
echo "   ls -la video_debug/             # List saved frames"
echo "   open video_debug/frame_000010.jpg  # View a sample frame"
echo ""

echo "🎯 What to Look For:"
echo ""
echo "✅ SUCCESS INDICATORS:"
echo "- Frame count increases over time"
echo "- FPS shows reasonable values (>10)"
echo "- JPEG files are created in video_debug/"
echo "- Saved frames show colorful test patterns"
echo "- Video track validation passes"
echo ""
echo "❌ PROBLEM INDICATORS:"
echo "- Frame count stays at 0 or doesn't increase"
echo "- No JPEG files created"
echo "- Error messages during validation"
echo "- FPS shows 0 or very low values"
echo ""

echo "🔍 Expected Log Output:"
echo ""
echo "GOOD - Camera Working:"
echo "[DEBUG] Video constraints: 640x480@15fps"
echo "[INFO] Media stream created with 1 video tracks and 1 audio tracks"
echo "[DEBUG] Starting REAL video capture test for 15s"
echo "[DEBUG] Simulated frame 10 capture"
echo "[DEBUG] Saved frame 10 to video_debug/frame_000010.jpg"
echo "[VIDEO_STATS] Frames: 150, FPS: 10.00, Resolution: 640x480, Total Bytes: 153600"
echo ""

echo "BAD - Camera Not Working:"
echo "[ERROR] failed to get user media: no video devices found"
echo "[ERROR] Video track validation failed: no video tracks available"
echo "[VIDEO_STATS] Frames: 0, FPS: 0.00, Resolution: 0x0, Total Bytes: 0"
echo ""

echo "🛠️ Troubleshooting:"
echo ""
echo "If no frames are captured:"
echo "1. Check camera permissions (macOS: System Preferences > Security & Privacy > Camera)"
echo "2. Ensure no other apps are using the camera"
echo "3. Try different camera device IDs"
echo "4. Lower resolution/fps settings"
echo ""

echo "If frames are captured but browser shows black screen:"
echo "1. Check WebRTC connection logs"
echo "2. Verify H.264 encoding compatibility"
echo "3. Test with different browsers"
echo "4. Check network connectivity"
echo ""

echo "📊 Analysis Guide:"
echo ""
echo "Frame Count Analysis:"
echo "- 0 frames: Camera access failed"
echo "- Increasing frames: Camera is working"
echo "- Static frame count: Capture stopped"
echo ""
echo "FPS Analysis:"
echo "- 0 FPS: No video data"
echo "- 1-10 FPS: Low performance or issues"
echo "- 15-30 FPS: Normal operation"
echo "- >30 FPS: High performance"
echo ""

echo "🎉 Ready to Test!"
echo ""
echo "Run the commands above and report back:"
echo "1. What frame count do you see?"
echo "2. Are JPEG files created?"
echo "3. Do the saved frames show test patterns?"
echo "4. What are the video statistics?"
echo ""
echo "This will help us determine if the issue is:"
echo "- Camera capture (mediadevices)"
echo "- Video encoding (H.264)"
echo "- WebRTC transmission"
echo "- Browser rendering"
