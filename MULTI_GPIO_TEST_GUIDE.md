# 多GPIO背光测试指南

## 🎯 **问题分析**

从你的测试结果可以看出：
- ✅ **EK79007面板初始化成功**
- ✅ **硬件测试图案成功**（垂直/水平条纹）
- ✅ **软件绘制成功**（8个彩色方块）
- ❌ **但是屏幕虽然亮了，却什么都不显示**

这说明**GPIO 26可能不是正确的背光控制引脚**，或者需要尝试其他GPIO。

## 🔍 **官方Demo的线索**

从官方EK79007 demo可以看出：
```c
#define TEST_PIN_NUM_BK_LIGHT (22)  // 官方demo使用GPIO 22
```

但是你发现GPIO 26能让屏幕亮起，这说明不同的ESP32P4开发板可能使用不同的背光引脚。

## 🛠️ **解决方案：多GPIO自动测试**

我已经创建了一个自动测试程序，会依次测试多个可能的背光GPIO引脚。

### 📋 **测试的GPIO引脚**

1. **GPIO 22** - 官方demo使用
2. **GPIO 26** - 你发现的引脚
3. **GPIO 45-52, 54** - 其他可能的背光引脚

### 🚀 **使用方法**

```bash
esp> multi_gpio_test
```

### 📊 **测试流程**

程序会自动：

1. **依次测试每个GPIO**：
   ```
   🔍 测试 GPIO 22 (官方demo)
   请观察屏幕是否显示红色...
   ```

2. **每个GPIO测试时**：
   - 配置GPIO为输出模式
   - 设置GPIO为高电平（开启背光）
   - 初始化EK79007面板
   - 绘制红色全屏
   - 等待10秒观察效果

3. **清理并测试下一个**：
   - 清理当前GPIO的资源
   - 等待3秒
   - 测试下一个GPIO

### 🎯 **如何判断成功**

**✅ 成功的标志**：
- 某个GPIO测试时，**屏幕显示红色**
- 日志显示：`✅ 红色全屏绘制成功`

**❌ 失败的标志**：
- 屏幕仍然黑屏或只是亮着但没有颜色
- 日志显示：`❌ 红色全屏绘制失败`

### 📝 **预期日志**

**成功时**：
```
I (xxx) MULTI_GPIO_TEST: 🔍 测试 GPIO 22 (官方demo)
I (xxx) MULTI_GPIO_TEST: ✅ LCD backlight enabled on GPIO 22
I (xxx) MULTI_GPIO_TEST: ✅ EK79007面板初始化完成
I (xxx) MULTI_GPIO_TEST: ✅ 红色全屏绘制成功
I (xxx) MULTI_GPIO_TEST: 如果看到红色屏幕，说明GPIO 22是正确的背光引脚！
```

**失败时**：
```
I (xxx) MULTI_GPIO_TEST: 🔍 测试 GPIO 45
I (xxx) MULTI_GPIO_TEST: ✅ LCD backlight enabled on GPIO 45
I (xxx) MULTI_GPIO_TEST: ✅ EK79007面板初始化完成
I (xxx) MULTI_GPIO_TEST: ✅ 红色全屏绘制成功
```
**但是屏幕没有显示红色**

## 🎯 **找到正确GPIO后**

一旦找到正确的背光GPIO（比如GPIO 22），需要：

### 1. 更新配置文件
修改`components/codec_board/board_cfg.txt`：
```
rst: -1, ctrl_pin: 22,  # 使用正确的GPIO
```

### 2. 重新编译测试
```bash
cd solutions/videocall_demo
idf.py build
idf.py flash
```

### 3. 验证其他测试
```bash
esp> lcd_simple      # 应该能看到彩色方块
esp> ek79007_test    # 应该能看到测试图案
```

### 4. 测试WebRTC视频
```bash
esp> join aa0003     # WebRTC视频应该正常显示
```

## 🔧 **编译和运行**

```bash
cd solutions/videocall_demo
idf.py build
idf.py flash monitor
```

然后运行：
```bash
esp> multi_gpio_test
```

## 📊 **可能的结果**

### 场景1：找到正确GPIO
- 某个GPIO测试时屏幕显示红色
- 更新配置文件使用该GPIO
- 所有LCD功能正常工作

### 场景2：所有GPIO都不工作
- 可能是硬件问题
- 可能需要查看开发板原理图
- 可能需要检查硬件跳线设置

### 场景3：多个GPIO都能工作
- 选择官方推荐的GPIO（通常是GPIO 22）
- 或者选择你之前验证过的GPIO 26

## 🎯 **下一步行动**

请立即运行：
```bash
esp> multi_gpio_test
```

然后仔细观察：
1. **哪个GPIO测试时屏幕显示了红色**
2. **记录该GPIO编号**
3. **告诉我结果，我会帮你更新配置**

这个自动测试可以快速确定正确的背光GPIO，解决显示问题！
