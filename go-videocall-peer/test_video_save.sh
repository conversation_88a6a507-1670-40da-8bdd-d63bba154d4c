#!/bin/bash

# 测试视频保存和播放功能

echo "=== Go VideoCall Peer - 视频保存测试 ==="
echo

# 检查ffplay是否可用
if command -v ffplay >/dev/null 2>&1; then
    echo "✓ ffplay 可用"
else
    echo "⚠ ffplay 不可用，将只保存文件"
fi

# 创建调试目录
mkdir -p debug

echo "启动程序..."
echo "请按以下步骤测试："
echo "1. 输入 'join abc0002' 加入房间"
echo "2. 在ESP32上也加入相同房间"
echo "3. 发起或接受通话"
echo "4. 观察视频文件保存和ffplay播放"
echo "5. 通话结束后检查保存的文件"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 检查保存的文件
if [ -f "debug/received_video.h264" ]; then
    size=$(stat -f%z "debug/received_video.h264" 2>/dev/null || stat -c%s "debug/received_video.h264" 2>/dev/null)
    echo "✓ 视频文件已保存: debug/received_video.h264 (${size} bytes)"
    
    # 提供播放建议
    echo
    echo "可以使用以下命令播放保存的视频："
    echo "  ffplay -f h264 -framerate 15 debug/received_video.h264"
    echo "  vlc debug/received_video.h264"
else
    echo "✗ 未找到保存的视频文件"
fi

if [ -f "debug/received_audio.pcma" ]; then
    size=$(stat -f%z "debug/received_audio.pcma" 2>/dev/null || stat -c%s "debug/received_audio.pcma" 2>/dev/null)
    echo "✓ 音频文件已保存: debug/received_audio.pcma (${size} bytes)"
    
    # 提供播放建议
    echo
    echo "可以使用以下命令播放保存的音频："
    echo "  ffplay -f alaw -ar 8000 -ac 1 debug/received_audio.pcma"
else
    echo "✗ 未找到保存的音频文件"
fi
