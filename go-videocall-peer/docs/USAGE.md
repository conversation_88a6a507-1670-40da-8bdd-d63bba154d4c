# Go VideoCall Peer 使用指南

## 概述

Go VideoCall Peer 是一个专门设计用于与ESP32 videocall_demo设备进行双向视频通话的应用程序。它实现了完整的WebRTC点对点通信协议，支持H.264视频编码和PCMA音频编码。

## 快速开始

### 1. 环境准备

确保您的系统满足以下要求：
- Go 1.19 或更高版本
- 网络连接（用于信令服务器通信）
- 摄像头和麦克风（可选，当前版本使用测试数据）

### 2. 编译和运行

```bash
# 进入项目目录
cd go-videocall-peer

# 下载依赖
go mod tidy

# 编译项目
go build -o videocall-peer .

# 运行程序
./videocall-peer
```

### 3. 基本使用流程

1. **启动程序**
   ```bash
   ./videocall-peer
   ```

2. **加入房间**
   ```
   videocall> join myroom123
   ```
   使用与ESP32设备相同的房间ID

3. **发起呼叫**
   ```
   videocall> call
   ```

4. **接听来电**
   ```
   videocall> accept
   ```

5. **挂断通话**
   ```
   videocall> hangup
   ```

6. **退出程序**
   ```
   videocall> quit
   ```

## 命令参考

### 基本命令

| 命令 | 快捷键 | 描述 | 示例 |
|------|--------|------|------|
| `join <房间ID>` | `j` | 加入指定房间 | `join room123` |
| `leave` | `l` | 离开当前房间 | `leave` |
| `call` | `c` | 发起呼叫 | `call` |
| `accept` | `a` | 接听来电 | `accept` |
| `reject` | `r` | 拒绝来电 | `reject` |
| `hangup` | `hang` | 挂断通话 | `hangup` |
| `status` | `s` | 显示当前状态 | `status` |
| `stats` | - | 显示统计信息 | `stats` |
| `help` | `h` | 显示帮助信息 | `help` |
| `quit` | `q` | 退出程序 | `quit` |

### 测试命令

| 命令 | 描述 | 示例 |
|------|------|------|
| `test [秒数]` | 运行媒体测试 | `test 10` |

## 配置选项

### 命令行参数

```bash
./videocall-peer [选项]

选项:
  -config string    配置文件路径 (默认: "config/default.yaml")
  -debug           启用调试模式
  -version         显示版本信息
```

### 配置文件

配置文件位于 `config/default.yaml`，包含以下主要配置：

#### 信令服务器配置
```yaml
signaling:
  server_url: "https://webrtc.espressif.cn"
  connect_timeout: 30s
  message_timeout: 10s
```

#### 视频配置
```yaml
video:
  width: 640
  height: 480
  fps: 15
  bitrate: 500000
  codec: "h264"
```

#### 音频配置
```yaml
audio:
  sample_rate: 8000
  channels: 1
  bitrate: 64000
  codec: "pcma"
```

## 与ESP32设备通话

### 准备工作

1. **ESP32设备准备**
   - 确保ESP32设备运行videocall_demo固件
   - 设备已连接到网络
   - 记录设备的房间ID

2. **网络配置**
   - 确保两个设备可以访问相同的信令服务器
   - 如果设备在不同网络，可能需要配置TURN服务器

### 通话流程

1. **Go程序加入房间**
   ```
   videocall> join esp32room
   ```

2. **ESP32设备加入相同房间**
   - 在ESP32设备上输入相同的房间ID

3. **发起呼叫**
   - 任一设备都可以发起呼叫
   - Go程序：`call`
   - ESP32设备：按下呼叫按钮

4. **接听呼叫**
   - 被叫方接听呼叫
   - Go程序：`accept`
   - ESP32设备：按下接听按钮

5. **通话中**
   - 双向音视频传输
   - 可以查看连接状态：`status`

6. **结束通话**
   - 任一方都可以挂断
   - Go程序：`hangup`
   - ESP32设备：按下挂断按钮

## 状态说明

### 连接状态

| 状态 | 描述 |
|------|------|
| `Idle` | 空闲状态，未连接 |
| `Connecting` | 正在连接信令服务器 |
| `Connected` | 已连接到信令服务器 |
| `OutgoingCall` | 发起呼叫中 |
| `IncomingCall` | 接收到来电 |
| `Ringing` | 响铃中 |
| `Negotiating` | WebRTC协商中 |
| `InCall` | 通话中 |
| `HangingUp` | 挂断中 |
| `Disconnected` | 已断开连接 |
| `Error` | 错误状态 |

### 统计信息

使用 `stats` 命令可以查看：
- 媒体捕获统计（帧数、字节数、FPS等）
- 媒体播放统计（接收帧数、播放状态等）
- WebRTC连接统计（连接时间、质量指标等）

## 故障排除

### 常见问题

1. **无法连接到信令服务器**
   - 检查网络连接
   - 确认信令服务器URL正确
   - 检查防火墙设置

2. **无法建立WebRTC连接**
   - 检查ICE服务器配置
   - 确认两个设备在同一网络或配置了TURN服务器
   - 查看WebRTC连接日志

3. **音视频质量问题**
   - 检查网络带宽
   - 调整编码参数
   - 查看丢包率和延迟统计

### 调试模式

启用调试模式获取详细日志：
```bash
./videocall-peer -debug
```

调试模式会：
- 输出详细的连接日志
- 保存调试数据到 `debug/` 目录
- 显示更多统计信息

### 日志分析

日志格式：`[组件] 消息内容`

主要组件标识：
- `[PEER]` - 主要对等端逻辑
- `[SIGNALING]` - 信令处理
- `[WEBRTC]` - WebRTC连接
- `[MEDIA]` - 媒体处理

## 高级功能

### 自定义配置

创建自定义配置文件：
```bash
cp config/default.yaml config/custom.yaml
# 编辑 custom.yaml
./videocall-peer -config config/custom.yaml
```

### 网络优化

对于跨网络通话，配置TURN服务器：
```yaml
webrtc:
  ice_servers:
    - urls: ["stun:stun.l.google.com:19302"]
    - urls: ["turn:your-turn-server:3478"]
      username: "your-username"
      credential: "your-password"
```

### 媒体参数调优

根据网络条件调整媒体参数：
```yaml
video:
  bitrate: 300000  # 降低码率适应低带宽
  fps: 10          # 降低帧率
  
audio:
  bitrate: 32000   # 降低音频码率
```

## 开发说明

### 项目结构
```
go-videocall-peer/
├── main.go              # 主程序入口
├── config/              # 配置管理
├── internal/
│   ├── videocall/       # 视频通话核心逻辑
│   ├── media/           # 媒体处理
│   ├── webrtc/          # WebRTC封装
│   └── cli/             # 命令行界面
└── docs/                # 文档
```

### 扩展功能

当前版本使用测试媒体数据。要集成真实的摄像头和麦克风：

1. 实现 `media.MediaCapture` 接口
2. 集成AstiAV或其他媒体库
3. 添加设备枚举和选择功能
4. 实现媒体格式转换

### 贡献指南

欢迎提交问题报告和功能请求到项目仓库。
