#!/bin/bash

# 测试数据通道视频发送功能

echo "=== 数据通道视频发送测试 ==="
echo

# 清理之前的调试文件
rm -f debug/received_video.h264 debug/received_video.mjpeg debug/received_audio.pcma debug/monitor.log

echo "✅ 已清理调试文件"
echo

echo "启动程序进行数据通道视频测试..."
echo "请按以下步骤操作："
echo
echo "1. 程序启动后，输入: join a0005"
echo "2. 启用监控: monitor"
echo "3. 在ESP32上发起呼叫 (输入 b)"
echo "4. 在程序中接受呼叫: accept"
echo "5. 观察以下关键日志:"
echo "   - [MEDIA] 数据通道发送器已设置"
echo "   - [MEDIA] 已通过数据通道发送JPEG帧: 30 (大小: 20xxx bytes)"
echo "   - [WEBRTC] 数据通道已打开"
echo "   - [PEER] 数据通道二进制消息: 20xxx bytes, 前4字节: ffd8ffe0"
echo "   - [JPEG] 接收JPEG帧 #10, 大小: 20500 bytes, FPS: 10.0"
echo
echo "6. 使用调试命令检查状态:"
echo "   - debug  (检查双向数据传输)"
echo "   - files  (检查MJPEG文件大小)"
echo
echo "预期结果:"
echo "✅ Go程序发送JPEG数据到ESP32（通过数据通道）"
echo "✅ ESP32发送JPEG数据到Go程序（通过数据通道）"
echo "✅ ESP32日志应该显示: Recv V:[非0值] （接收到视频数据）"
echo "✅ 双向视频传输正常工作"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 检查结果
echo "检查测试结果:"

if [ -f "debug/received_video.mjpeg" ]; then
    size=$(stat -f%z "debug/received_video.mjpeg" 2>/dev/null || stat -c%s "debug/received_video.mjpeg" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 接收MJPEG文件: ${size} bytes (ESP32 -> Go)"
        
        # 计算大概的帧数（假设每帧20KB）
        frames=$((size / 20000))
        echo "📹 估计接收帧数: ~${frames} 帧"
        
        echo "🎬 播放命令:"
        echo "   ffplay -f mjpeg debug/received_video.mjpeg"
    else
        echo "❌ 接收MJPEG文件: 0 bytes (ESP32 -> Go 失败)"
    fi
else
    echo "❌ 接收MJPEG文件: 不存在"
fi

if [ -f "debug/received_audio.pcma" ]; then
    size=$(stat -f%z "debug/received_audio.pcma" 2>/dev/null || stat -c%s "debug/received_audio.pcma" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 音频文件: ${size} bytes (双向音频正常)"
        echo "🎵 播放命令:"
        echo "   ffplay -f alaw -ar 8000 -ac 1 debug/received_audio.pcma"
    else
        echo "❌ 音频文件: 0 bytes (音频传输失败)"
    fi
else
    echo "❌ 音频文件: 不存在"
fi

echo
echo "ESP32端检查要点:"
echo "1. ESP32日志应该显示:"
echo "   - Send V:xxx [帧数:字节数] (发送视频正常)"
echo "   - Recv V:[非0:非0] (接收视频正常) ← 关键指标"
echo "   - Send A:xxx [帧数:字节数] (发送音频正常)"
echo "   - Recv A:xxx [帧数:字节数] (接收音频正常)"
echo
echo "2. 如果ESP32仍显示 Recv V:[0:0]，可能原因:"
echo "   - 数据通道未正确建立"
echo "   - JPEG数据格式不兼容"
echo "   - 网络传输问题"
echo "   - ESP32数据通道接收逻辑问题"
echo
echo "3. 调试建议:"
echo "   - 检查Go程序是否正确发送JPEG数据"
echo "   - 验证数据通道连接状态"
echo "   - 对比ESP32发送的JPEG格式"
echo "   - 检查数据通道标签和配置"
echo

echo "数据通道视频传输说明:"
echo "- Go程序现在通过数据通道发送JPEG图像（模拟ESP32行为）"
echo "- ESP32通过数据通道接收JPEG图像"
echo "- 这与ESP32 videocall_demo的配置一致"
echo "- 双向数据通道视频传输应该正常工作"
