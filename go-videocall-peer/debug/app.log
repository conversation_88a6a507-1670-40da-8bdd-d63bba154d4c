2025/06/06 22:06:18 main.go:182: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:06:18 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:06:18 配置文件: config/default.yaml
2025/06/06 22:06:18 调试模式: false
2025/06/06 22:06:18 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:06:18 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:06:18 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:06:19 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:06:19 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:06:19 [PEER] 初始化视频通话对等端
2025/06/06 22:06:19 [MEDIA] 初始化媒体捕获器
2025/06/06 22:06:19 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:06:19 [WEBRTC] 数据通道创建成功
2025/06/06 22:06:19 [WEBRTC] PeerConnection创建成功
2025/06/06 22:06:19 [PEER] 视频通话对等端初始化完成
2025/06/06 22:06:19 视频通话对等端初始化完成
2025/06/06 22:06:19 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:19 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:20 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:20 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:21 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:21 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:22 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:22 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:23 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:23 收到信号: interrupt
2025/06/06 22:06:23 正在关闭应用程序...
2025/06/06 22:06:23 正在关闭视频通话对等端...
2025/06/06 22:06:23 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:06:23 [PEER] 离开房间...
2025/06/06 22:06:23 [PEER] 离开房间
2025/06/06 22:06:23 [PEER] 已离开房间
2025/06/06 22:06:23 [PEER] 关闭媒体捕获器...
2025/06/06 22:06:23 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:06:23 [PEER] 关闭媒体播放器...
2025/06/06 22:06:23 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:06:23 [H264] ffplay已关闭
2025/06/06 22:06:23 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:06:23 [JPEG] ffplay已关闭
2025/06/06 22:06:23 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:06:23 [PLAYER] 媒体播放器已关闭
2025/06/06 22:06:23 [PEER] 关闭WebRTC管理器...
2025/06/06 22:06:23 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:06:23 [WEBRTC] 关闭数据通道...
2025/06/06 22:06:23 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:06:23 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:06:23 [WEBRTC] 连接状态变化: closed
2025/06/06 22:06:23 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:06:23 等待资源释放...
2025/06/06 22:06:23 [WEBRTC] 数据通道状态: closed
2025/06/06 22:06:23 [WEBRTC] 💔 数据通道已关闭，停止监听
2025/06/06 22:06:25 应用程序已安全关闭
2025/06/06 22:06:25 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:06:25 [PEER] 离开房间...
2025/06/06 22:06:25 [PEER] 离开房间
2025/06/06 22:06:25 [PEER] 已离开房间
2025/06/06 22:06:25 [PEER] 关闭媒体捕获器...
2025/06/06 22:06:25 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:06:25 [PEER] 关闭媒体播放器...
2025/06/06 22:06:25 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:06:25 [H264] ffplay已关闭
2025/06/06 22:06:25 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:06:25 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:06:25 [JPEG] ffplay已关闭
2025/06/06 22:06:25 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:06:25 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:06:25 [PLAYER] 媒体播放器已关闭
2025/06/06 22:06:25 [PEER] 关闭WebRTC管理器...
2025/06/06 22:06:25 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:06:25 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:06:25 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:06:40 main.go:182: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:06:40 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:06:40 配置文件: config/default.yaml
2025/06/06 22:06:40 调试模式: false
2025/06/06 22:06:40 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:06:40 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:06:40 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:06:41 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:06:41 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:06:41 [PEER] 初始化视频通话对等端
2025/06/06 22:06:41 [MEDIA] 初始化媒体捕获器
2025/06/06 22:06:41 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:06:41 [WEBRTC] 数据通道创建成功
2025/06/06 22:06:41 [WEBRTC] PeerConnection创建成功
2025/06/06 22:06:41 [PEER] 视频通话对等端初始化完成
2025/06/06 22:06:41 视频通话对等端初始化完成
2025/06/06 22:06:41 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:42 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:42 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:43 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:43 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:44 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:44 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:45 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:45 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:46 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:46 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:47 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:47 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:48 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:48 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:49 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:49 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:50 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:50 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:51 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:51 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:52 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:52 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:53 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:53 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:54 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:54 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:55 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:55 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:56 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:56 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:57 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:57 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:58 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:58 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:59 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:06:59 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:00 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:00 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:01 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:01 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:02 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:02 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:03 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:03 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:04 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:04 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:05 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:05 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:06 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:06 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:07 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:07 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:08 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:08 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:09 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:09 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:10 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:10 [PEER] 加入房间: aaa0000
2025/06/06 22:07:10 [SIGNALING] 连接到房间: aaa0000
2025/06/06 22:07:10 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:07:10 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:10 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"aaa0000","clientid":"13998818"}
2025/06/06 22:07:10 [SIGNALING] 成功连接到房间: aaa0000, ClientID: 13998818, IsInitiator: false
2025/06/06 22:07:10 [MEDIA] 开始媒体捕获
2025/06/06 22:07:10 [MEDIA] 启动视频捕获循环
2025/06/06 22:07:10 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:07:10 [MEDIA] 启动音频捕获循环
2025/06/06 22:07:10 [WEBRTC] 添加视频轨道: video
2025/06/06 22:07:10 [PEER] 信令服务器已连接
2025/06/06 22:07:10 [PLAYER] 开始媒体播放
2025/06/06 22:07:10 [PEER] 成功加入房间: aaa0000
2025/06/06 22:07:10 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:07:10 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:07:10 [ASTIAV] 调试模式已启用
2025/06/06 22:07:10 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:07:10 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:07:10 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:07:10 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:07:10 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:07:10 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:07:10 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:07:10 [ASTIAV] 打开输入: 0:none
2025/06/06 22:07:11 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:11 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:12 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:12 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:07:12 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:07:12 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:07:12 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:07:12 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:07:12 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:07:12 [ASTIAV] 捕获循环已启动
2025/06/06 22:07:12 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7441 bytes, 分辨率: 1024x600)
2025/06/06 22:07:12 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:07:12 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:13 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:13 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:13 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"RING\"}","error":""}
2025/06/06 22:07:13 [SIGNALING] 收到自定义命令: RING, 数据: 
2025/06/06 22:07:13 [PEER] 处理自定义命令: RING, 数据: 
2025/06/06 22:07:13 [PEER] 状态变化: Connected -> IncomingCall
2025/06/06 22:07:14 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:14 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:14 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10840 bytes
2025/06/06 22:07:14 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10778 bytes, 分辨率: 1024x600)
2025/06/06 22:07:14 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:07:15 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:15 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:16 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:16 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:16 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10590 bytes
2025/06/06 22:07:16 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10457 bytes, 分辨率: 1024x600)
2025/06/06 22:07:16 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:07:17 [PEER] 接受呼叫
2025/06/06 22:07:17 [WEBRTC] 连接状态变化: closed
2025/06/06 22:07:17 [PEER] WebRTC连接状态: closed
2025/06/06 22:07:17 [WEBRTC] 数据通道创建成功
2025/06/06 22:07:17 [WEBRTC] PeerConnection创建成功
2025/06/06 22:07:17 [MEDIA] 数据通道发送器已设置
2025/06/06 22:07:17 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:07:17 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:17 [WEBRTC] 添加视频轨道: video
2025/06/06 22:07:17 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}"}
2025/06/06 22:07:17 [PEER] 呼叫已接受，开始WebRTC协商
2025/06/06 22:07:17 [PEER] 状态变化: IncomingCall -> Negotiating
2025/06/06 22:07:17 [WEBRTC] 数据通道状态: closed
2025/06/06 22:07:17 [WEBRTC] 💔 数据通道已关闭，停止监听
2025/06/06 22:07:17 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:17 [SIGNALING] 收到消息: {"msg":"{\"type\":\"offer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:0\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 54:67:8C:4C:2D:56:57:4A:62:C5:78:DC:A6:75:4D:23:86:0E:2F:B4:3A:FA:F1:CF:23:7C:0E:E7:AA:B0:16:65\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1691836159 *************** 55134 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15105535 ************* 59005 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2128043775 ************** 55134 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:1\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 54:67:8C:4C:2D:56:57:4A:62:C5:78:DC:A6:75:4D:23:86:0E:2F:B4:3A:FA:F1:CF:23:7C:0E:E7:AA:B0:16:65\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1691836159 *************** 55134 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15105535 ************* 59005 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2128043775 ************** 55134 typ host\\r\\n\"}","error":""}
2025/06/06 22:07:17 [PEER] 处理RTC消息: offer
2025/06/06 22:07:17 [PEER] 处理接收到的Offer
2025/06/06 22:07:17 [WEBRTC] 连接状态变化: closed
2025/06/06 22:07:17 [PEER] WebRTC连接状态: closed
2025/06/06 22:07:17 [WEBRTC] 数据通道创建成功
2025/06/06 22:07:17 [WEBRTC] PeerConnection创建成功
2025/06/06 22:07:17 [MEDIA] 数据通道发送器已设置
2025/06/06 22:07:17 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:17 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:07:17 [WEBRTC] 信令状态变化: have-remote-offer
2025/06/06 22:07:17 [WEBRTC] 设置远程offer描述成功
2025/06/06 22:07:17 [WEBRTC] ICE连接状态变化: checking
2025/06/06 22:07:17 [WEBRTC] 连接状态变化: connecting
2025/06/06 22:07:17 [WEBRTC] 信令状态变化: stable
2025/06/06 22:07:17 [PEER] WebRTC连接状态: connecting
2025/06/06 22:07:17 [WEBRTC] 创建并设置Answer成功
2025/06/06 22:07:17 [WEBRTC] 生成ICE候选: udp4 host **************:51596 (resolved: **************:51596)
2025/06/06 22:07:17 [WEBRTC] 生成ICE候选: udp4 host *************:61613 (resolved: *************:61613)
2025/06/06 22:07:17 [SIGNALING] 发送SDP answer
2025/06/06 22:07:17 [PEER] Answer已发送
2025/06/06 22:07:17 [SIGNALING] 发送ICE候选
2025/06/06 22:07:17 [SIGNALING] 发送ICE候选
2025/06/06 22:07:17 [WEBRTC] ICE连接状态变化: connected
2025/06/06 22:07:18 [WEBRTC] 数据通道状态: closed
2025/06/06 22:07:18 [WEBRTC] 💔 数据通道已关闭，停止监听
2025/06/06 22:07:18 [WEBRTC] 生成ICE候选: udp4 srflx ***************:61768 related 0.0.0.0:61768 (resolved: ***************:61768)
2025/06/06 22:07:18 [WEBRTC] 生成ICE候选: udp4 srflx ***************:55041 related 0.0.0.0:55041 (resolved: ***************:55041)
2025/06/06 22:07:18 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:18 [SIGNALING] 发送ICE候选
2025/06/06 22:07:18 [SIGNALING] 发送ICE候选
2025/06/06 22:07:18 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:18 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10784 bytes
2025/06/06 22:07:19 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10642 bytes, 分辨率: 1024x600)
2025/06/06 22:07:19 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:07:19 [WEBRTC] 连接状态变化: connected
2025/06/06 22:07:19 [PEER] WebRTC连接状态: connected
2025/06/06 22:07:19 [PEER] 状态变化: Negotiating -> InCall
2025/06/06 22:07:19 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:07:19 [WEBRTC] 接收到远程轨道:  (类型: audio)
2025/06/06 22:07:19 [PEER] 接收到远程轨道:  (类型: audio)
2025/06/06 22:07:19 [PLAYER] 设置远程音频轨道:  (类型: audio, 编解码器: audio/PCMA, 播放状态: true)
2025/06/06 22:07:19 [PLAYER] 开始播放音频轨道:  (编解码器: audio/PCMA)
2025/06/06 22:07:19 [PLAYER] 收到音频包 #1: 序号=0, 时间戳=0, 大小=160, 负载类型=8
2025/06/06 22:07:19 [PLAYER] 收到音频包: 序号=0, 时间戳=0, 大小=160
2025/06/06 22:07:19 [WEBRTC] ✅ 数据通道已打开，可以开始发送视频数据
2025/06/06 22:07:19 [PEER] 数据通道二进制消息: 20383 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:19 [PEER] 数据通道二进制消息: 20134 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:19 [PEER] 数据通道二进制消息: 20257 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:19 [WEBRTC] 数据通道状态: open
2025/06/06 22:07:19 [WEBRTC] 🎉 数据通道已就绪，视频传输可以开始
2025/06/06 22:07:19 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:07:19 [PEER] 数据通道二进制消息: 20293 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:19 [PEER] 数据通道二进制消息: 20355 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:19 [PEER] 数据通道二进制消息: 20287 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 20351 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 20068 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 20132 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 20260 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [JPEG] 接收JPEG帧 #10, 大小: 20260 bytes, FPS: 0.3
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 20251 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 20290 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 19932 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 20154 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 20156 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:20 [PEER] 数据通道二进制消息: 20211 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20258 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20186 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20256 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10503 bytes
2025/06/06 22:07:21 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10503 bytes, 分辨率: 1024x600)
2025/06/06 22:07:21 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:07:21 [MEDIA] 已通过数据通道发送JPEG帧: 120 (大小: 10503 bytes)
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20280 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [JPEG] 接收JPEG帧 #20, 大小: 20280 bytes, FPS: 0.5
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20312 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20184 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20291 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20298 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20295 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:21 [PEER] 数据通道二进制消息: 20317 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:22 [PEER] 数据通道二进制消息: 20278 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:22 [PEER] 数据通道二进制消息: 20270 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:22 [PEER] 数据通道二进制消息: 20226 bytes, 前4字节: ffd8ffe0
2025/06/06 22:07:22 收到信号: interrupt
2025/06/06 22:07:22 正在关闭应用程序...
2025/06/06 22:07:22 正在关闭视频通话对等端...
2025/06/06 22:07:22 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:07:22 [PEER] 离开房间...
2025/06/06 22:07:22 [PEER] 离开房间
2025/06/06 22:07:22 [MEDIA] 停止媒体捕获
2025/06/06 22:07:22 [PLAYER] 停止媒体播放
2025/06/06 22:07:22 [SIGNALING] 断开信令连接
2025/06/06 22:07:22 [SIGNALING] WebSocket读取错误: read tcp *************:55021->52.81.131.193:8089: use of closed network connection
2025/06/06 22:07:22 [PEER] 信令错误: read tcp *************:55021->52.81.131.193:8089: use of closed network connection
2025/06/06 22:07:22 [PEER] 已离开房间
2025/06/06 22:07:22 [PEER] 关闭媒体捕获器...
2025/06/06 22:07:22 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:07:22 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:07:22 [PLAYER] 音频轨道播放协程退出: 
2025/06/06 22:09:06 main.go:182: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:09:06 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:09:06 配置文件: config/default.yaml
2025/06/06 22:09:06 调试模式: false
2025/06/06 22:09:06 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:09:06 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:09:06 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:09:07 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:09:07 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:09:07 [PEER] 初始化视频通话对等端
2025/06/06 22:09:07 [MEDIA] 初始化媒体捕获器
2025/06/06 22:09:07 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:09:07 [WEBRTC] 数据通道创建成功
2025/06/06 22:09:07 [WEBRTC] PeerConnection创建成功
2025/06/06 22:09:07 [PEER] 视频通话对等端初始化完成
2025/06/06 22:09:07 视频通话对等端初始化完成
2025/06/06 22:09:07 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:07 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:08 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:08 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:09 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:09 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:10 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:10 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:11 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:11 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:12 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:12 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:13 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:13 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:14 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:14 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:15 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:15 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:16 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:16 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:17 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:17 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:18 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:18 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:19 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:19 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:20 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:20 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:21 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:21 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:22 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:22 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:23 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:23 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:24 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:24 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:25 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:25 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:26 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:26 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:27 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:27 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:28 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:28 [PEER] 加入房间: ccc001
2025/06/06 22:09:28 [SIGNALING] 连接到房间: ccc001
2025/06/06 22:09:28 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:09:28 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"ccc001","clientid":"16358271"}
2025/06/06 22:09:28 [SIGNALING] 成功连接到房间: ccc001, ClientID: 16358271, IsInitiator: false
2025/06/06 22:09:28 [PEER] 信令服务器已连接
2025/06/06 22:09:28 [MEDIA] 开始媒体捕获
2025/06/06 22:09:28 [MEDIA] 启动视频捕获循环
2025/06/06 22:09:28 [MEDIA] 启动音频捕获循环
2025/06/06 22:09:28 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:09:28 [WEBRTC] 添加视频轨道: video
2025/06/06 22:09:28 [PLAYER] 开始媒体播放
2025/06/06 22:09:28 [PEER] 成功加入房间: ccc001
2025/06/06 22:09:28 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:09:28 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:09:28 [ASTIAV] 调试模式已启用
2025/06/06 22:09:28 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:09:28 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:09:28 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:09:28 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:09:28 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:09:28 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:09:28 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:09:28 [ASTIAV] 打开输入: 0:none
2025/06/06 22:09:28 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:29 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:29 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:30 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:30 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:31 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:31 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:32 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:09:32 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:09:32 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:09:32 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:09:32 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:09:32 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:09:32 [ASTIAV] 捕获循环已启动
2025/06/06 22:09:32 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:32 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7659 bytes, 分辨率: 1024x600)
2025/06/06 22:09:32 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:09:32 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:33 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:33 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:34 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"RING\"}","error":""}
2025/06/06 22:09:34 [SIGNALING] 收到自定义命令: RING, 数据: 
2025/06/06 22:09:34 [PEER] 处理自定义命令: RING, 数据: 
2025/06/06 22:09:34 [PEER] 状态变化: Connected -> IncomingCall
2025/06/06 22:09:34 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:34 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10770 bytes
2025/06/06 22:09:34 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10770 bytes, 分辨率: 1024x600)
2025/06/06 22:09:34 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:09:34 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:35 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:35 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:36 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:36 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:36 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10937 bytes
2025/06/06 22:09:36 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10937 bytes, 分辨率: 1024x600)
2025/06/06 22:09:36 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:09:37 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:37 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:38 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:38 [PEER] 接受呼叫
2025/06/06 22:09:38 [WEBRTC] 连接状态变化: closed
2025/06/06 22:09:38 [PEER] WebRTC连接状态: closed
2025/06/06 22:09:38 [WEBRTC] 数据通道创建成功
2025/06/06 22:09:38 [WEBRTC] PeerConnection创建成功
2025/06/06 22:09:38 [MEDIA] 数据通道发送器已设置
2025/06/06 22:09:38 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:38 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:09:38 [WEBRTC] 添加视频轨道: video
2025/06/06 22:09:38 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}"}
2025/06/06 22:09:38 [PEER] 呼叫已接受，开始WebRTC协商
2025/06/06 22:09:38 [PEER] 状态变化: IncomingCall -> Negotiating
2025/06/06 22:09:38 [WEBRTC] 数据通道状态: closed
2025/06/06 22:09:38 [WEBRTC] 💔 数据通道已关闭，停止监听
2025/06/06 22:09:38 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:38 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10657 bytes
2025/06/06 22:09:38 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10657 bytes, 分辨率: 1024x600)
2025/06/06 22:09:38 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:09:38 [SIGNALING] 收到消息: {"msg":"{\"type\":\"offer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:0\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 54:67:8C:4C:2D:56:57:4A:62:C5:78:DC:A6:75:4D:23:86:0E:2F:B4:3A:FA:F1:CF:23:7C:0E:E7:AA:B0:16:65\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1691836415 *************** 55135 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15112959 ************* 59034 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2128044031 ************** 55135 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:1\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 54:67:8C:4C:2D:56:57:4A:62:C5:78:DC:A6:75:4D:23:86:0E:2F:B4:3A:FA:F1:CF:23:7C:0E:E7:AA:B0:16:65\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1691836415 *************** 55135 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15112959 ************* 59034 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2128044031 ************** 55135 typ host\\r\\n\"}","error":""}
2025/06/06 22:09:38 [PEER] 处理RTC消息: offer
2025/06/06 22:09:38 [PEER] 处理接收到的Offer
2025/06/06 22:09:38 [WEBRTC] 连接状态变化: closed
2025/06/06 22:09:38 [PEER] WebRTC连接状态: closed
2025/06/06 22:09:38 [WEBRTC] 数据通道创建成功
2025/06/06 22:09:38 [WEBRTC] PeerConnection创建成功
2025/06/06 22:09:38 [MEDIA] 数据通道发送器已设置
2025/06/06 22:09:38 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:09:38 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:38 [WEBRTC] 信令状态变化: have-remote-offer
2025/06/06 22:09:38 [WEBRTC] 设置远程offer描述成功
2025/06/06 22:09:38 [WEBRTC] 信令状态变化: stable
2025/06/06 22:09:38 [WEBRTC] 创建并设置Answer成功
2025/06/06 22:09:38 [WEBRTC] ICE连接状态变化: checking
2025/06/06 22:09:38 [WEBRTC] 连接状态变化: connecting
2025/06/06 22:09:38 [PEER] WebRTC连接状态: connecting
2025/06/06 22:09:38 [WEBRTC] 生成ICE候选: udp4 host **************:49796 (resolved: **************:49796)
2025/06/06 22:09:38 [WEBRTC] 生成ICE候选: udp4 host *************:54703 (resolved: *************:54703)
2025/06/06 22:09:39 [SIGNALING] 发送SDP answer
2025/06/06 22:09:39 [PEER] Answer已发送
2025/06/06 22:09:39 [SIGNALING] 发送ICE候选
2025/06/06 22:09:39 [SIGNALING] 发送ICE候选
2025/06/06 22:09:39 [WEBRTC] 数据通道状态: closed
2025/06/06 22:09:39 [WEBRTC] 💔 数据通道已关闭，停止监听
2025/06/06 22:09:39 [WEBRTC] 生成ICE候选: udp4 srflx ***************:59126 related 0.0.0.0:59126 (resolved: ***************:59126)
2025/06/06 22:09:39 [WEBRTC] 生成ICE候选: udp4 srflx ***************:54152 related 0.0.0.0:54152 (resolved: ***************:54152)
2025/06/06 22:09:39 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:39 [SIGNALING] 发送ICE候选
2025/06/06 22:09:39 [SIGNALING] 发送ICE候选
2025/06/06 22:09:39 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:40 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:40 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:41 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10425 bytes, 分辨率: 1024x600)
2025/06/06 22:09:41 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:09:41 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10653 bytes
2025/06/06 22:09:41 [WEBRTC] ICE连接状态变化: connected
2025/06/06 22:09:41 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:41 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:42 [WEBRTC] 连接状态变化: connected
2025/06/06 22:09:42 [PEER] WebRTC连接状态: connected
2025/06/06 22:09:42 [PEER] 状态变化: Negotiating -> InCall
2025/06/06 22:09:42 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:09:42 [WEBRTC] 接收到远程轨道:  (类型: audio)
2025/06/06 22:09:42 [PEER] 接收到远程轨道:  (类型: audio)
2025/06/06 22:09:42 [PLAYER] 设置远程音频轨道:  (类型: audio, 编解码器: audio/PCMA, 播放状态: true)
2025/06/06 22:09:42 [PLAYER] 开始播放音频轨道:  (编解码器: audio/PCMA)
2025/06/06 22:09:42 [PLAYER] 收到音频包 #1: 序号=0, 时间戳=0, 大小=160, 负载类型=8
2025/06/06 22:09:42 [PLAYER] 收到音频包: 序号=0, 时间戳=0, 大小=160
2025/06/06 22:09:42 [WEBRTC] ✅ 数据通道已打开，可以开始发送视频数据
2025/06/06 22:09:42 [PEER] 数据通道二进制消息: 24363 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:42 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:09:42 [PEER] 数据通道二进制消息: 24691 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:42 [PEER] 数据通道二进制消息: 24804 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:42 [WEBRTC] 数据通道状态: open
2025/06/06 22:09:42 [WEBRTC] 🎉 数据通道已就绪，视频传输可以开始
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 24851 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 24800 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 21763 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 21763 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:43 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 11006 bytes, 分辨率: 1024x600)
2025/06/06 22:09:43 [MEDIA] 已通过数据通道发送JPEG帧: 150 (大小: 11006 bytes)
2025/06/06 22:09:43 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10862 bytes
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 21635 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 24402 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 24067 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:43 [JPEG] 接收JPEG帧 #10, 大小: 24067 bytes, FPS: 0.3
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 20307 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 23902 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:43 [PEER] 数据通道二进制消息: 23891 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 23938 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 24328 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 24320 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 24540 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 23635 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 23946 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 24124 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [JPEG] 接收JPEG帧 #20, 大小: 24124 bytes, FPS: 0.5
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 24538 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 24618 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:44 [PEER] 数据通道二进制消息: 24255 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24645 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24542 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24546 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24623 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24682 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 11046 bytes, 分辨率: 1024x600)
2025/06/06 22:09:45 [MEDIA] 已通过数据通道发送JPEG帧: 180 (大小: 11046 bytes)
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24612 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24693 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [JPEG] 接收JPEG帧 #30, 大小: 24693 bytes, FPS: 0.8
2025/06/06 22:09:45 [ASTIAV] 已捕获 180 帧, 当前帧大小: 11014 bytes
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24806 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24742 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:45 [PEER] 数据通道二进制消息: 24780 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:46 [PEER] 数据通道二进制消息: 24842 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:46 [PEER] 数据通道二进制消息: 24791 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:46 [PEER] 数据通道二进制消息: 24843 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:46 [PEER] 数据通道二进制消息: 24845 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:46 [PEER] 数据通道二进制消息: 24849 bytes, 前4字节: ffd8ffe0
2025/06/06 22:09:46 收到信号: interrupt
2025/06/06 22:09:46 正在关闭应用程序...
2025/06/06 22:09:46 正在关闭视频通话对等端...
2025/06/06 22:09:46 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:09:46 [PEER] 离开房间...
2025/06/06 22:09:46 [PEER] 离开房间
2025/06/06 22:09:46 [MEDIA] 停止媒体捕获
2025/06/06 22:09:46 [PLAYER] 停止媒体播放
2025/06/06 22:09:46 [SIGNALING] 断开信令连接
2025/06/06 22:09:46 [SIGNALING] WebSocket读取错误: read tcp *************:55569->52.81.131.193:8089: use of closed network connection
2025/06/06 22:09:46 [PEER] 已离开房间
2025/06/06 22:09:46 [PEER] 关闭媒体捕获器...
2025/06/06 22:09:46 [PEER] 信令错误: read tcp *************:55569->52.81.131.193:8089: use of closed network connection
2025/06/06 22:09:46 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:09:46 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:09:46 [PLAYER] 音频轨道播放协程退出: 
2025/06/06 22:14:35 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:14:35 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:14:35 配置文件: config/default.yaml
2025/06/06 22:14:35 调试模式: false
2025/06/06 22:14:35 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:14:35 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:14:35 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:14:36 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:14:36 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:14:36 [PEER] 初始化视频通话对等端
2025/06/06 22:14:36 [MEDIA] 初始化媒体捕获器
2025/06/06 22:14:36 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:14:36 [WEBRTC] 数据通道创建成功
2025/06/06 22:14:36 [WEBRTC] PeerConnection创建成功
2025/06/06 22:14:36 [PEER] 视频通话对等端初始化完成
2025/06/06 22:14:36 视频通话对等端初始化完成
2025/06/06 22:14:36 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:36 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:37 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:37 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:38 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:38 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:39 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:39 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:40 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:40 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:41 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:41 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:42 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:42 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:43 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:43 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:44 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:44 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:45 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:45 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:46 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:46 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:47 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:47 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:48 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:48 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:49 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:49 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:50 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:50 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:51 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:51 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:52 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:52 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:53 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:53 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:54 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:54 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:55 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:55 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:56 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:56 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:57 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:57 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:58 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:58 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:59 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:14:59 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:00 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:00 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:01 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:01 [PEER] 加入房间: cccc111
2025/06/06 22:15:01 [SIGNALING] 连接到房间: cccc111
2025/06/06 22:15:01 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:15:01 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:01 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"cccc111","clientid":"12211702"}
2025/06/06 22:15:01 [SIGNALING] 成功连接到房间: cccc111, ClientID: 12211702, IsInitiator: false
2025/06/06 22:15:01 [MEDIA] 开始媒体捕获
2025/06/06 22:15:01 [PEER] 信令服务器已连接
2025/06/06 22:15:01 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:15:01 [MEDIA] 启动音频捕获循环
2025/06/06 22:15:01 [WEBRTC] 添加视频轨道: video
2025/06/06 22:15:01 [MEDIA] 启动视频捕获循环
2025/06/06 22:15:01 [PLAYER] 开始媒体播放
2025/06/06 22:15:01 [PEER] 成功加入房间: cccc111
2025/06/06 22:15:01 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:15:01 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:15:01 [ASTIAV] 调试模式已启用
2025/06/06 22:15:01 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:15:01 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:15:01 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:15:01 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:15:01 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:15:01 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:15:01 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:15:01 [ASTIAV] 打开输入: 0:none
2025/06/06 22:15:02 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:02 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:03 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:03 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:15:03 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:15:03 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:15:03 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:15:03 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:15:03 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:15:03 [ASTIAV] 捕获循环已启动
2025/06/06 22:15:03 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7547 bytes, 分辨率: 1024x600)
2025/06/06 22:15:03 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:15:03 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:04 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:04 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:05 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:05 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10535 bytes
2025/06/06 22:15:05 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10622 bytes, 分辨率: 1024x600)
2025/06/06 22:15:05 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:15:05 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:06 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:06 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:07 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:07 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10607 bytes
2025/06/06 22:15:07 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:07 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10777 bytes, 分辨率: 1024x600)
2025/06/06 22:15:07 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:15:07 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"RING\"}","error":""}
2025/06/06 22:15:07 [SIGNALING] 收到自定义命令: RING, 数据: 
2025/06/06 22:15:07 [PEER] 处理自定义命令: RING, 数据: 
2025/06/06 22:15:07 [PEER] 状态变化: Connected -> IncomingCall
2025/06/06 22:15:08 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:08 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:09 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:09 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:09 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10619 bytes
2025/06/06 22:15:09 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10518 bytes, 分辨率: 1024x600)
2025/06/06 22:15:09 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:15:10 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:10 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:10 [PEER] 接受呼叫
2025/06/06 22:15:10 [WEBRTC] 连接状态变化: closed
2025/06/06 22:15:10 [PEER] WebRTC连接状态: closed
2025/06/06 22:15:10 [WEBRTC] 数据通道创建成功
2025/06/06 22:15:10 [WEBRTC] PeerConnection创建成功
2025/06/06 22:15:10 [MEDIA] 数据通道发送器已设置
2025/06/06 22:15:10 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:10 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:15:10 [WEBRTC] 添加视频轨道: video
2025/06/06 22:15:10 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}"}
2025/06/06 22:15:10 [PEER] 呼叫已接受，开始WebRTC协商
2025/06/06 22:15:10 [PEER] 状态变化: IncomingCall -> Negotiating
2025/06/06 22:15:11 [WEBRTC] 数据通道状态: closed
2025/06/06 22:15:11 [WEBRTC] 💔 数据通道已关闭，停止监听
2025/06/06 22:15:11 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:11 [SIGNALING] 收到消息: {"msg":"{\"type\":\"offer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:0\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 54:67:8C:4C:2D:56:57:4A:62:C5:78:DC:A6:75:4D:23:86:0E:2F:B4:3A:FA:F1:CF:23:7C:0E:E7:AA:B0:16:65\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1691836671 *************** 55136 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15128063 ************* 59093 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2128044287 ************** 55136 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:1\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 54:67:8C:4C:2D:56:57:4A:62:C5:78:DC:A6:75:4D:23:86:0E:2F:B4:3A:FA:F1:CF:23:7C:0E:E7:AA:B0:16:65\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1691836671 *************** 55136 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15128063 ************* 59093 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2128044287 ************** 55136 typ host\\r\\n\"}","error":""}
2025/06/06 22:15:11 [PEER] 处理RTC消息: offer
2025/06/06 22:15:11 [PEER] 处理接收到的Offer
2025/06/06 22:15:11 [WEBRTC] 连接状态变化: closed
2025/06/06 22:15:11 [PEER] WebRTC连接状态: closed
2025/06/06 22:15:11 [WEBRTC] 数据通道创建成功
2025/06/06 22:15:11 [WEBRTC] PeerConnection创建成功
2025/06/06 22:15:11 [MEDIA] 数据通道发送器已设置
2025/06/06 22:15:11 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:15:11 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:11 [WEBRTC] 信令状态变化: have-remote-offer
2025/06/06 22:15:11 [WEBRTC] 设置远程offer描述成功
2025/06/06 22:15:11 [WEBRTC] ICE连接状态变化: checking
2025/06/06 22:15:11 [WEBRTC] 连接状态变化: connecting
2025/06/06 22:15:11 [PEER] WebRTC连接状态: connecting
2025/06/06 22:15:11 [WEBRTC] 信令状态变化: stable
2025/06/06 22:15:11 [WEBRTC] 创建并设置Answer成功
2025/06/06 22:15:11 [WEBRTC] 生成ICE候选: udp4 host **************:52187 (resolved: **************:52187)
2025/06/06 22:15:11 [WEBRTC] 生成ICE候选: udp4 host *************:52010 (resolved: *************:52010)
2025/06/06 22:15:11 [SIGNALING] 发送ICE候选
2025/06/06 22:15:11 [SIGNALING] 发送ICE候选
2025/06/06 22:15:11 [SIGNALING] 发送SDP answer
2025/06/06 22:15:11 [PEER] Answer已发送
2025/06/06 22:15:11 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10589 bytes
2025/06/06 22:15:11 [WEBRTC] 数据通道状态: closed
2025/06/06 22:15:11 [WEBRTC] 💔 数据通道已关闭，停止监听
2025/06/06 22:15:12 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10646 bytes, 分辨率: 1024x600)
2025/06/06 22:15:12 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:15:12 [WEBRTC] 生成ICE候选: udp4 srflx ***************:61744 related 0.0.0.0:61744 (resolved: ***************:61744)
2025/06/06 22:15:12 [WEBRTC] 生成ICE候选: udp4 srflx ***************:51209 related 0.0.0.0:51209 (resolved: ***************:51209)
2025/06/06 22:15:12 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:12 [SIGNALING] 发送ICE候选
2025/06/06 22:15:12 [SIGNALING] 发送ICE候选
2025/06/06 22:15:12 [WEBRTC] ICE连接状态变化: connected
2025/06/06 22:15:12 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:13 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:13 [WEBRTC] 连接状态变化: connected
2025/06/06 22:15:13 [PEER] WebRTC连接状态: connected
2025/06/06 22:15:13 [PEER] 状态变化: Negotiating -> InCall
2025/06/06 22:15:13 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:13 [WEBRTC] 接收到远程轨道:  (类型: audio)
2025/06/06 22:15:13 [PEER] 接收到远程轨道:  (类型: audio)
2025/06/06 22:15:13 [PLAYER] 设置远程音频轨道:  (类型: audio, 编解码器: audio/PCMA, 播放状态: true)
2025/06/06 22:15:13 [PLAYER] 开始播放音频轨道:  (编解码器: audio/PCMA)
2025/06/06 22:15:13 [PLAYER] 收到音频包 #1: 序号=0, 时间戳=0, 大小=160, 负载类型=8
2025/06/06 22:15:13 [PLAYER] 收到音频包: 序号=0, 时间戳=0, 大小=160
2025/06/06 22:15:13 [WEBRTC] ✅ 数据通道已打开，可以开始发送视频数据
2025/06/06 22:15:13 [PEER] 数据通道二进制消息: 21786 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:13 [PEER] 数据通道二进制消息: 21772 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 21816 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 21802 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [WEBRTC] 数据通道状态: open
2025/06/06 22:15:14 [WEBRTC] 🎉 数据通道已就绪，视频传输可以开始
2025/06/06 22:15:14 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10556 bytes
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 21892 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10548 bytes, 分辨率: 1024x600)
2025/06/06 22:15:14 [MEDIA] 已通过数据通道发送JPEG帧: 150 (大小: 10548 bytes)
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 21865 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 21933 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 21853 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 21952 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 21886 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [JPEG] 接收JPEG帧 #10, 大小: 21886 bytes, FPS: 0.3
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 20485 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:14 [PEER] 数据通道二进制消息: 21949 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 20907 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 19941 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 20780 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 20809 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 20966 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 21314 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 21615 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 21713 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [JPEG] 接收JPEG帧 #20, 大小: 21713 bytes, FPS: 0.5
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 21502 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:15 [PEER] 数据通道二进制消息: 21775 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21792 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21732 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21874 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10675 bytes
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21901 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10592 bytes, 分辨率: 1024x600)
2025/06/06 22:15:16 [MEDIA] 已通过数据通道发送JPEG帧: 180 (大小: 10592 bytes)
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21872 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21868 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21845 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21921 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [JPEG] 接收JPEG帧 #30, 大小: 21921 bytes, FPS: 0.7
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21828 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:16 [PEER] 数据通道二进制消息: 21884 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21872 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21855 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21956 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21897 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21852 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21858 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21822 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21900 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [JPEG] 接收JPEG帧 #40, 大小: 21900 bytes, FPS: 0.9
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21889 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:17 [PEER] 数据通道二进制消息: 21869 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:18 [PEER] 数据通道二进制消息: 21845 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:18 [PEER] 数据通道二进制消息: 21799 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:18 [PEER] 数据通道二进制消息: 21862 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:18 [PEER] 数据通道二进制消息: 21775 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:18 [PEER] 数据通道二进制消息: 21817 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:18 [PEER] 数据通道二进制消息: 21748 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:18 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10954 bytes
2025/06/06 22:15:18 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10943 bytes, 分辨率: 1024x600)
2025/06/06 22:15:18 [MEDIA] 已通过数据通道发送JPEG帧: 210 (大小: 10943 bytes)
2025/06/06 22:15:18 [PEER] 数据通道二进制消息: 21792 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:18 收到信号: interrupt
2025/06/06 22:15:18 正在关闭应用程序...
2025/06/06 22:15:18 正在关闭视频通话对等端...
2025/06/06 22:15:18 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:15:18 [PEER] 离开房间...
2025/06/06 22:15:18 [PEER] 离开房间
2025/06/06 22:15:18 [MEDIA] 停止媒体捕获
2025/06/06 22:15:18 [PLAYER] 停止媒体播放
2025/06/06 22:15:18 [SIGNALING] 断开信令连接
2025/06/06 22:15:18 [SIGNALING] WebSocket读取错误: read tcp *************:56811->52.81.131.193:8089: use of closed network connection
2025/06/06 22:15:18 [PEER] 已离开房间
2025/06/06 22:15:18 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:15:18 [PEER] 信令错误: read tcp *************:56811->52.81.131.193:8089: use of closed network connection
2025/06/06 22:15:18 [PEER] 关闭媒体捕获器...
2025/06/06 22:15:18 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:15:18 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:15:18 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:15:18 [ASTIAV] 开始清理资源...
2025/06/06 22:15:18 [ASTIAV] 释放输入包...
2025/06/06 22:15:18 [ASTIAV] 释放解码帧...
2025/06/06 22:15:18 [ASTIAV] 释放缩放帧...
2025/06/06 22:15:18 [ASTIAV] 释放编码包...
2025/06/06 22:15:18 [ASTIAV] 释放缩放上下文...
2025/06/06 22:15:18 [ASTIAV] 释放编码器上下文...
2025/06/06 22:15:18 [ASTIAV] 释放解码器上下文...
2025/06/06 22:15:18 [ASTIAV] 关闭输入上下文...
2025/06/06 22:15:18 [PEER] 数据通道二进制消息: 21814 bytes, 前4字节: ffd8ffe0
2025/06/06 22:15:18 [JPEG] 接收JPEG帧 #50, 大小: 21814 bytes, FPS: 1.2
2025/06/06 22:15:18 [JPEG] 写入ffplay失败: write /tmp/videocall_mjpeg_pipe: broken pipe
2025/06/06 22:15:18 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:15:18 [PLAYER] 音频轨道播放协程退出: 
2025/06/06 22:15:18 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:15:18 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:15:18 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:15:18 [PEER] 关闭媒体播放器...
2025/06/06 22:15:18 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:15:18 [H264] ffplay已关闭
2025/06/06 22:15:18 [JPEG] JPEG文件已保存: 50 帧, 1084052 bytes
2025/06/06 22:15:18 [JPEG] ffplay已关闭
2025/06/06 22:15:18 [AUDIO] 音频文件已保存: 40320 bytes
2025/06/06 22:15:18 [PLAYER] 媒体播放器已关闭
2025/06/06 22:15:18 [PEER] 关闭WebRTC管理器...
2025/06/06 22:15:18 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:15:18 [WEBRTC] 关闭数据通道...
2025/06/06 22:15:18 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:15:18 [WEBRTC] ❌ 数据通道已关闭
2025/06/06 22:15:18 [WEBRTC] ICE连接状态变化: closed
2025/06/06 22:15:18 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:15:18 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:15:18 [WEBRTC] 连接状态变化: closed
2025/06/06 22:15:18 等待资源释放...
2025/06/06 22:15:18 [MEDIA] 视频捕获循环结束
2025/06/06 22:15:20 应用程序已安全关闭
2025/06/06 22:15:20 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:15:20 [PEER] 离开房间...
2025/06/06 22:15:20 [PEER] 离开房间
2025/06/06 22:15:20 [PEER] 已离开房间
2025/06/06 22:15:20 [PEER] 关闭媒体捕获器...
2025/06/06 22:15:20 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:15:20 [PEER] 关闭媒体播放器...
2025/06/06 22:15:20 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:15:20 [H264] ffplay已关闭
2025/06/06 22:15:20 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:15:20 [JPEG] JPEG文件已保存: 50 帧, 1084052 bytes
2025/06/06 22:15:20 [JPEG] ffplay已关闭
2025/06/06 22:15:20 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:15:20 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:15:20 [PLAYER] 媒体播放器已关闭
2025/06/06 22:15:20 [PEER] 关闭WebRTC管理器...
2025/06/06 22:15:20 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:15:20 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:15:20 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:15:26 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:15:26 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:15:26 配置文件: config/default.yaml
2025/06/06 22:15:26 调试模式: false
2025/06/06 22:15:26 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:15:26 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:15:26 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:15:27 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:15:27 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:15:27 [PEER] 初始化视频通话对等端
2025/06/06 22:15:27 [MEDIA] 初始化媒体捕获器
2025/06/06 22:15:27 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:15:27 [WEBRTC] 数据通道创建成功
2025/06/06 22:15:27 [WEBRTC] PeerConnection创建成功
2025/06/06 22:15:27 [PEER] 视频通话对等端初始化完成
2025/06/06 22:15:27 视频通话对等端初始化完成
2025/06/06 22:15:27 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:27 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:28 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:28 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:29 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:29 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:30 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:30 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:31 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:31 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:32 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:32 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:33 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:33 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:34 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:34 [WEBRTC] 数据通道状态: connecting
2025/06/06 22:15:35 收到信号: interrupt
2025/06/06 22:15:35 正在关闭应用程序...
2025/06/06 22:15:35 正在关闭视频通话对等端...
2025/06/06 22:15:35 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:15:35 [PEER] 离开房间...
2025/06/06 22:15:35 [PEER] 离开房间
2025/06/06 22:15:35 [PEER] 已离开房间
2025/06/06 22:15:35 [PEER] 关闭媒体捕获器...
2025/06/06 22:15:35 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:15:35 [PEER] 关闭媒体播放器...
2025/06/06 22:15:35 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:15:35 [H264] ffplay已关闭
2025/06/06 22:15:35 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:15:35 [JPEG] ffplay已关闭
2025/06/06 22:15:35 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:15:35 [PLAYER] 媒体播放器已关闭
2025/06/06 22:15:35 [PEER] 关闭WebRTC管理器...
2025/06/06 22:15:35 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:15:35 [WEBRTC] 关闭数据通道...
2025/06/06 22:15:35 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:15:35 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:15:35 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:15:35 等待资源释放...
2025/06/06 22:15:35 [WEBRTC] 连接状态变化: closed
2025/06/06 22:15:35 [WEBRTC] 数据通道状态: closed
2025/06/06 22:15:35 [WEBRTC] 💔 数据通道已关闭，停止监听
2025/06/06 22:15:37 应用程序已安全关闭
2025/06/06 22:15:37 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:15:37 [PEER] 离开房间...
2025/06/06 22:15:37 [PEER] 离开房间
2025/06/06 22:15:37 [PEER] 已离开房间
2025/06/06 22:15:37 [PEER] 关闭媒体捕获器...
2025/06/06 22:15:37 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:15:37 [PEER] 关闭媒体播放器...
2025/06/06 22:15:37 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:15:37 [H264] ffplay已关闭
2025/06/06 22:15:37 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:15:37 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:15:37 [JPEG] ffplay已关闭
2025/06/06 22:15:37 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:15:37 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:15:37 [PLAYER] 媒体播放器已关闭
2025/06/06 22:15:37 [PEER] 关闭WebRTC管理器...
2025/06/06 22:15:37 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:15:37 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:15:37 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:17:18 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:17:18 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:17:18 配置文件: config/default.yaml
2025/06/06 22:17:18 调试模式: false
2025/06/06 22:17:18 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:17:18 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:17:18 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:17:19 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:17:19 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:17:19 [PEER] 初始化视频通话对等端
2025/06/06 22:17:19 [MEDIA] 初始化媒体捕获器
2025/06/06 22:17:19 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:17:19 [WEBRTC] 数据通道创建成功
2025/06/06 22:17:19 [WEBRTC] PeerConnection创建成功
2025/06/06 22:17:19 [PEER] 视频通话对等端初始化完成
2025/06/06 22:17:19 视频通话对等端初始化完成
2025/06/06 22:17:33 [PEER] 加入房间: abc001
2025/06/06 22:17:33 [SIGNALING] 连接到房间: abc001
2025/06/06 22:17:33 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:17:33 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"abc001","clientid":"80924229"}
2025/06/06 22:17:33 [SIGNALING] 成功连接到房间: abc001, ClientID: 80924229, IsInitiator: false
2025/06/06 22:17:33 [MEDIA] 开始媒体捕获
2025/06/06 22:17:33 [PEER] 信令服务器已连接
2025/06/06 22:17:33 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:17:33 [MEDIA] 启动视频捕获循环
2025/06/06 22:17:33 [MEDIA] 启动音频捕获循环
2025/06/06 22:17:33 [WEBRTC] 添加视频轨道: video
2025/06/06 22:17:33 [PLAYER] 开始媒体播放
2025/06/06 22:17:33 [PEER] 成功加入房间: abc001
2025/06/06 22:17:33 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:17:33 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:17:33 [ASTIAV] 调试模式已启用
2025/06/06 22:17:33 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:17:33 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:17:33 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:17:33 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:17:33 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:17:33 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:17:33 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:17:33 [ASTIAV] 打开输入: 0:none
2025/06/06 22:17:35 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:17:35 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:17:35 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:17:35 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:17:35 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:17:35 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:17:35 [ASTIAV] 捕获循环已启动
2025/06/06 22:17:35 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7503 bytes, 分辨率: 1024x600)
2025/06/06 22:17:35 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:17:37 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10585 bytes
2025/06/06 22:17:37 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10585 bytes, 分辨率: 1024x600)
2025/06/06 22:17:37 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:17:38 [PEER] 发起呼叫
2025/06/06 22:17:38 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:17:38 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:17:38 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:17:38 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:17:39 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10656 bytes
2025/06/06 22:17:39 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10656 bytes, 分辨率: 1024x600)
2025/06/06 22:17:39 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:17:41 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}","error":""}
2025/06/06 22:17:41 [SIGNALING] 收到自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:17:41 [PEER] 处理自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:17:41 [PEER] 创建并发送Offer
2025/06/06 22:17:41 [PEER] 状态变化: Ringing -> Negotiating
2025/06/06 22:17:41 [WEBRTC] 连接状态变化: closed
2025/06/06 22:17:41 [PEER] WebRTC连接状态: closed
2025/06/06 22:17:41 [WEBRTC] 数据通道创建成功
2025/06/06 22:17:41 [WEBRTC] PeerConnection创建成功
2025/06/06 22:17:41 [MEDIA] 数据通道发送器已设置
2025/06/06 22:17:41 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:17:41 [WEBRTC] 创建并设置Offer成功
2025/06/06 22:17:41 [WEBRTC] 信令状态变化: have-local-offer
2025/06/06 22:17:41 [WEBRTC] 生成ICE候选: udp4 host **************:49681 (resolved: **************:49681)
2025/06/06 22:17:41 [WEBRTC] 生成ICE候选: udp4 host *************:49477 (resolved: *************:49477)
2025/06/06 22:17:41 [SIGNALING] 发送SDP offer
2025/06/06 22:17:41 [PEER] Offer已发送
2025/06/06 22:17:41 [SIGNALING] 发送ICE候选
2025/06/06 22:17:41 [SIGNALING] 发送ICE候选
2025/06/06 22:17:41 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10409 bytes, 分辨率: 1024x600)
2025/06/06 22:17:41 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:17:42 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10405 bytes
2025/06/06 22:17:42 [WEBRTC] 生成ICE候选: udp4 srflx ***************:55574 related 0.0.0.0:55574 (resolved: ***************:55574)
2025/06/06 22:17:42 [WEBRTC] 生成ICE候选: udp4 srflx ***************:63348 related 0.0.0.0:63348 (resolved: ***************:63348)
2025/06/06 22:17:42 [SIGNALING] 发送ICE候选
2025/06/06 22:17:42 [SIGNALING] 收到消息: {"msg":"{\"type\":\"offer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:0\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 54:67:8C:4C:2D:56:57:4A:62:C5:78:DC:A6:75:4D:23:86:0E:2F:B4:3A:FA:F1:CF:23:7C:0E:E7:AA:B0:16:65\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1691836927 *************** 55137 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15113471 ************* 59036 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2128044543 ************** 55137 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:1\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 54:67:8C:4C:2D:56:57:4A:62:C5:78:DC:A6:75:4D:23:86:0E:2F:B4:3A:FA:F1:CF:23:7C:0E:E7:AA:B0:16:65\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1691836927 *************** 55137 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15113471 ************* 59036 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2128044543 ************** 55137 typ host\\r\\n\"}","error":""}
2025/06/06 22:17:42 [PEER] 处理RTC消息: offer
2025/06/06 22:17:42 [PEER] 处理接收到的Offer
2025/06/06 22:17:42 [WEBRTC] 连接状态变化: closed
2025/06/06 22:17:42 [PEER] WebRTC连接状态: closed
2025/06/06 22:17:42 [WEBRTC] 数据通道创建成功
2025/06/06 22:17:42 [WEBRTC] PeerConnection创建成功
2025/06/06 22:17:42 [MEDIA] 数据通道发送器已设置
2025/06/06 22:17:42 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:17:42 [WEBRTC] 信令状态变化: have-remote-offer
2025/06/06 22:17:42 [WEBRTC] 设置远程offer描述成功
2025/06/06 22:17:42 [WEBRTC] ICE连接状态变化: checking
2025/06/06 22:17:42 [WEBRTC] 连接状态变化: connecting
2025/06/06 22:17:42 [PEER] WebRTC连接状态: connecting
2025/06/06 22:17:42 [WEBRTC] 创建并设置Answer成功
2025/06/06 22:17:42 [WEBRTC] 信令状态变化: stable
2025/06/06 22:17:42 [WEBRTC] 生成ICE候选: udp4 host **************:63481 (resolved: **************:63481)
2025/06/06 22:17:42 [WEBRTC] 生成ICE候选: udp4 host *************:56925 (resolved: *************:56925)
2025/06/06 22:17:42 [SIGNALING] 发送ICE候选
2025/06/06 22:17:42 [SIGNALING] 发送SDP answer
2025/06/06 22:17:42 [PEER] Answer已发送
2025/06/06 22:17:42 [SIGNALING] 发送ICE候选
2025/06/06 22:17:42 [SIGNALING] 发送ICE候选
2025/06/06 22:17:42 [WEBRTC] 生成ICE候选: udp4 srflx ***************:59696 related 0.0.0.0:59696 (resolved: ***************:59696)
2025/06/06 22:17:42 [WEBRTC] 生成ICE候选: udp4 srflx ***************:49997 related 0.0.0.0:49997 (resolved: ***************:49997)
2025/06/06 22:17:42 [WEBRTC] ICE连接状态变化: connected
2025/06/06 22:17:42 [SIGNALING] 发送ICE候选
2025/06/06 22:17:42 [SIGNALING] 发送ICE候选
2025/06/06 22:17:43 [WEBRTC] 连接状态变化: connected
2025/06/06 22:17:43 [PEER] WebRTC连接状态: connected
2025/06/06 22:17:43 [PEER] 状态变化: Negotiating -> InCall
2025/06/06 22:17:44 [WEBRTC] 接收到远程轨道:  (类型: audio)
2025/06/06 22:17:44 [PEER] 接收到远程轨道:  (类型: audio)
2025/06/06 22:17:44 [PLAYER] 设置远程音频轨道:  (类型: audio, 编解码器: audio/PCMA, 播放状态: true)
2025/06/06 22:17:44 [PLAYER] 开始播放音频轨道:  (编解码器: audio/PCMA)
2025/06/06 22:17:44 [PLAYER] 收到音频包 #1: 序号=0, 时间戳=0, 大小=160, 负载类型=8
2025/06/06 22:17:44 [PLAYER] 收到音频包: 序号=0, 时间戳=0, 大小=160
2025/06/06 22:17:44 [WEBRTC] ✅ 数据通道已打开，可以开始发送视频数据
2025/06/06 22:17:44 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10736 bytes, 分辨率: 1024x600)
2025/06/06 22:17:44 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:17:44 [MEDIA] 已通过数据通道发送JPEG帧: 120 (大小: 10736 bytes)
2025/06/06 22:17:44 [PEER] 数据通道二进制消息: 21902 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:44 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10655 bytes
2025/06/06 22:17:44 [PEER] 数据通道二进制消息: 21856 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:44 [PEER] 数据通道二进制消息: 21851 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:44 [PEER] 数据通道二进制消息: 21903 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:44 [PEER] 数据通道二进制消息: 21914 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:44 [PEER] 数据通道二进制消息: 21832 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:44 [PEER] 数据通道二进制消息: 21941 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:44 [PEER] 数据通道二进制消息: 21836 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21922 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21943 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [JPEG] 接收JPEG帧 #10, 大小: 21943 bytes, FPS: 0.4
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21864 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21937 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21783 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21865 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21879 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21952 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21791 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:45 [PEER] 数据通道二进制消息: 21830 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 21897 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 21947 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [JPEG] 接收JPEG帧 #20, 大小: 21947 bytes, FPS: 0.7
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 21846 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 21893 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10535 bytes, 分辨率: 1024x600)
2025/06/06 22:17:46 [MEDIA] 已通过数据通道发送JPEG帧: 150 (大小: 10535 bytes)
2025/06/06 22:17:46 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10478 bytes
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 21978 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 21948 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 21952 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 21914 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 21945 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:46 [PEER] 数据通道二进制消息: 22003 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 21941 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 21933 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [JPEG] 接收JPEG帧 #30, 大小: 21933 bytes, FPS: 1.1
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 22001 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 21933 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 21944 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 21969 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 21999 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 21991 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 21873 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:47 [PEER] 数据通道二进制消息: 21943 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21813 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21878 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [JPEG] 接收JPEG帧 #40, 大小: 21878 bytes, FPS: 1.4
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21802 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21818 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21960 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21867 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10333 bytes, 分辨率: 1024x600)
2025/06/06 22:17:48 [MEDIA] 已通过数据通道发送JPEG帧: 180 (大小: 10333 bytes)
2025/06/06 22:17:48 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10308 bytes
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21849 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21872 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21954 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:48 [PEER] 数据通道二进制消息: 21965 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 21856 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 21954 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [JPEG] 接收JPEG帧 #50, 大小: 21954 bytes, FPS: 1.7
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 22065 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 21743 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 21790 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 21493 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 20135 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 20735 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 21107 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 20580 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:49 [PEER] 数据通道二进制消息: 21427 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:50 [PEER] 数据通道二进制消息: 21069 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:50 [PEER] 数据通道二进制消息: 20776 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:50 [PEER] 数据通道二进制消息: 20308 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:50 [PEER] 数据通道二进制消息: 21123 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:50 [PEER] 数据通道二进制消息: 19518 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:50 [PEER] 数据通道二进制消息: 20064 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:50 [PEER] 数据通道二进制消息: 20590 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:50 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10467 bytes, 分辨率: 1024x600)
2025/06/06 22:17:50 [MEDIA] 已通过数据通道发送JPEG帧: 210 (大小: 10467 bytes)
2025/06/06 22:17:50 [PEER] 数据通道二进制消息: 20398 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:50 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10453 bytes
2025/06/06 22:17:50 [PEER] 数据通道二进制消息: 20734 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 20879 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 20897 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 21052 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 20932 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 20947 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 20922 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 20714 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 20909 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 20805 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:51 [PEER] 数据通道二进制消息: 20738 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 20628 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 20984 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [JPEG] 接收JPEG帧 #60, 大小: 21069 bytes, FPS: 1.8
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 21071 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 20939 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 20972 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 21003 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 21021 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 20993 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 21073 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [PEER] 数据通道二进制消息: 20952 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:52 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10634 bytes, 分辨率: 1024x600)
2025/06/06 22:17:52 [MEDIA] 已通过数据通道发送JPEG帧: 240 (大小: 10634 bytes)
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 20921 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:53 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10587 bytes
2025/06/06 22:17:53 [JPEG] 接收JPEG帧 #70, 大小: 20897 bytes, FPS: 2.1
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 20989 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 21015 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 20912 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 21046 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 20958 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 21056 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 21013 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 21081 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:53 [PEER] 数据通道二进制消息: 21097 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21064 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PLAYER] 收到音频包 #500: 序号=499, 时间戳=79840, 大小=160, 负载类型=8
2025/06/06 22:17:54 [PLAYER] 收到音频包: 序号=500, 时间戳=80000, 大小=160
2025/06/06 22:17:54 [JPEG] 接收JPEG帧 #80, 大小: 20984 bytes, FPS: 2.3
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21043 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21042 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21039 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21091 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21091 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21073 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21116 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21042 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:54 [PEER] 数据通道二进制消息: 21039 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21104 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [JPEG] 接收JPEG帧 #90, 大小: 20989 bytes, FPS: 2.5
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21156 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10580 bytes, 分辨率: 1024x600)
2025/06/06 22:17:55 [MEDIA] 已通过数据通道发送JPEG帧: 270 (大小: 10580 bytes)
2025/06/06 22:17:55 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10563 bytes
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21157 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21108 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21062 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21096 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21014 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21138 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21184 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:55 [PEER] 数据通道二进制消息: 21225 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21106 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21197 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [JPEG] 接收JPEG帧 #100, 大小: 21043 bytes, FPS: 2.7
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21140 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21040 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21050 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21166 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21115 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21097 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21109 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:56 [PEER] 数据通道二进制消息: 21108 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21079 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [JPEG] 接收JPEG帧 #110, 大小: 21156 bytes, FPS: 2.9
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21093 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21083 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21088 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10590 bytes, 分辨率: 1024x600)
2025/06/06 22:17:57 [MEDIA] 已通过数据通道发送JPEG帧: 300 (大小: 10590 bytes)
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21165 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10558 bytes
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21177 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21044 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21082 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21119 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21039 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:57 收到信号: interrupt
2025/06/06 22:17:57 正在关闭应用程序...
2025/06/06 22:17:57 正在关闭视频通话对等端...
2025/06/06 22:17:57 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:17:57 [PEER] 离开房间...
2025/06/06 22:17:57 [PEER] 离开房间
2025/06/06 22:17:57 [MEDIA] 停止媒体捕获
2025/06/06 22:17:57 [PLAYER] 停止媒体播放
2025/06/06 22:17:57 [SIGNALING] 断开信令连接
2025/06/06 22:17:57 [SIGNALING] WebSocket读取错误: read tcp *************:57401->52.81.131.193:8089: use of closed network connection
2025/06/06 22:17:57 [PEER] 信令错误: read tcp *************:57401->52.81.131.193:8089: use of closed network connection
2025/06/06 22:17:57 [PEER] 已离开房间
2025/06/06 22:17:57 [PEER] 关闭媒体捕获器...
2025/06/06 22:17:57 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:17:57 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:17:57 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:17:57 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:17:57 [ASTIAV] 开始清理资源...
2025/06/06 22:17:57 [ASTIAV] 释放输入包...
2025/06/06 22:17:57 [ASTIAV] 释放解码帧...
2025/06/06 22:17:57 [ASTIAV] 释放缩放帧...
2025/06/06 22:17:57 [ASTIAV] 释放编码包...
2025/06/06 22:17:57 [ASTIAV] 释放缩放上下文...
2025/06/06 22:17:57 [ASTIAV] 释放编码器上下文...
2025/06/06 22:17:57 [ASTIAV] 释放解码器上下文...
2025/06/06 22:17:57 [ASTIAV] 关闭输入上下文...
2025/06/06 22:17:57 [JPEG] 写入ffplay失败: write /tmp/videocall_mjpeg_pipe: broken pipe
2025/06/06 22:17:57 [JPEG] 接收JPEG帧 #120, 大小: 21197 bytes, FPS: 3.1
2025/06/06 22:17:57 [JPEG] 接收JPEG帧 #130, 大小: 21093 bytes, FPS: 3.3
2025/06/06 22:17:57 [PEER] 数据通道二进制消息: 21175 bytes, 前4字节: ffd8ffe0
2025/06/06 22:17:58 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:17:58 [PLAYER] 音频轨道播放协程退出: 
2025/06/06 22:17:58 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:17:58 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:17:58 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:17:58 [PEER] 关闭媒体播放器...
2025/06/06 22:17:58 [MEDIA] 视频捕获循环结束
2025/06/06 22:17:58 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:17:58 [H264] ffplay已关闭
2025/06/06 22:17:58 [JPEG] JPEG文件已保存: 139 帧, 2964569 bytes
2025/06/06 22:17:58 [JPEG] ffplay已关闭
2025/06/06 22:17:58 [AUDIO] 音频文件已保存: 111520 bytes
2025/06/06 22:17:58 [PLAYER] 媒体播放器已关闭
2025/06/06 22:17:58 [PEER] 关闭WebRTC管理器...
2025/06/06 22:17:58 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:17:58 [WEBRTC] 关闭数据通道...
2025/06/06 22:17:58 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:17:58 [WEBRTC] ❌ 数据通道已关闭
2025/06/06 22:17:58 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:17:58 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:17:58 等待资源释放...
2025/06/06 22:17:58 [WEBRTC] ICE连接状态变化: closed
2025/06/06 22:17:58 [WEBRTC] 连接状态变化: closed
2025/06/06 22:18:00 应用程序已安全关闭
2025/06/06 22:18:00 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:18:00 [PEER] 离开房间...
2025/06/06 22:18:00 [PEER] 离开房间
2025/06/06 22:18:00 [PEER] 已离开房间
2025/06/06 22:18:00 [PEER] 关闭媒体捕获器...
2025/06/06 22:18:00 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:18:00 [PEER] 关闭媒体播放器...
2025/06/06 22:18:00 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:18:00 [H264] ffplay已关闭
2025/06/06 22:18:00 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:18:00 [JPEG] JPEG文件已保存: 139 帧, 2964569 bytes
2025/06/06 22:18:00 [JPEG] ffplay已关闭
2025/06/06 22:18:00 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:18:00 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:18:00 [PLAYER] 媒体播放器已关闭
2025/06/06 22:18:00 [PEER] 关闭WebRTC管理器...
2025/06/06 22:18:00 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:18:00 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:18:00 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:18:07 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:18:07 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:18:07 配置文件: config/default.yaml
2025/06/06 22:18:07 调试模式: false
2025/06/06 22:18:07 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:18:07 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:18:07 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:18:07 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:18:07 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:18:07 [PEER] 初始化视频通话对等端
2025/06/06 22:18:07 [MEDIA] 初始化媒体捕获器
2025/06/06 22:18:07 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:18:07 [WEBRTC] 数据通道创建成功
2025/06/06 22:18:07 [WEBRTC] PeerConnection创建成功
2025/06/06 22:18:07 [PEER] 视频通话对等端初始化完成
2025/06/06 22:18:07 视频通话对等端初始化完成
2025/06/06 22:18:11 [PEER] 加入房间: abc0002
2025/06/06 22:18:11 [SIGNALING] 连接到房间: abc0002
2025/06/06 22:18:11 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:18:12 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"abc0002","clientid":"93156865"}
2025/06/06 22:18:12 [SIGNALING] 成功连接到房间: abc0002, ClientID: 93156865, IsInitiator: true
2025/06/06 22:18:12 [MEDIA] 开始媒体捕获
2025/06/06 22:18:12 [PEER] 信令服务器已连接
2025/06/06 22:18:12 [MEDIA] 启动视频捕获循环
2025/06/06 22:18:12 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:18:12 [MEDIA] 启动音频捕获循环
2025/06/06 22:18:12 [WEBRTC] 添加视频轨道: video
2025/06/06 22:18:12 [PLAYER] 开始媒体播放
2025/06/06 22:18:12 [PEER] 成功加入房间: abc0002
2025/06/06 22:18:12 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:18:12 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:18:12 [ASTIAV] 调试模式已启用
2025/06/06 22:18:12 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:18:12 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:18:12 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:18:12 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:18:12 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:18:12 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:18:12 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:18:12 [ASTIAV] 打开输入: 0:none
2025/06/06 22:18:13 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:18:13 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:18:13 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:18:13 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:18:13 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:18:13 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:18:13 [ASTIAV] 捕获循环已启动
2025/06/06 22:18:13 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7624 bytes, 分辨率: 1024x600)
2025/06/06 22:18:13 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:18:15 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10114 bytes
2025/06/06 22:18:15 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10243 bytes, 分辨率: 1024x600)
2025/06/06 22:18:15 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:18:18 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10707 bytes
2025/06/06 22:18:18 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10707 bytes, 分辨率: 1024x600)
2025/06/06 22:18:18 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:18:20 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10509 bytes
2025/06/06 22:18:20 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10509 bytes, 分辨率: 1024x600)
2025/06/06 22:18:20 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:18:22 [PEER] 发起呼叫
2025/06/06 22:18:22 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:18:22 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:18:22 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:18:22 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:18:22 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10522 bytes
2025/06/06 22:18:22 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10522 bytes, 分辨率: 1024x600)
2025/06/06 22:18:22 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:18:24 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10628 bytes, 分辨率: 1024x600)
2025/06/06 22:18:24 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10564 bytes
2025/06/06 22:18:26 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10377 bytes, 分辨率: 1024x600)
2025/06/06 22:18:26 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10419 bytes
2025/06/06 22:18:29 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10607 bytes, 分辨率: 1024x600)
2025/06/06 22:18:29 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10558 bytes
2025/06/06 22:18:31 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10735 bytes, 分辨率: 1024x600)
2025/06/06 22:18:31 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10703 bytes
2025/06/06 22:18:33 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10711 bytes, 分辨率: 1024x600)
2025/06/06 22:18:33 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10717 bytes
2025/06/06 22:18:35 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10519 bytes, 分辨率: 1024x600)
2025/06/06 22:18:35 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10491 bytes
2025/06/06 22:18:36 收到信号: interrupt
2025/06/06 22:18:36 正在关闭应用程序...
2025/06/06 22:18:36 正在关闭视频通话对等端...
2025/06/06 22:18:36 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:18:36 [PEER] 离开房间...
2025/06/06 22:18:36 [PEER] 离开房间
2025/06/06 22:18:36 [MEDIA] 停止媒体捕获
2025/06/06 22:18:36 [PLAYER] 停止媒体播放
2025/06/06 22:18:36 [SIGNALING] 断开信令连接
2025/06/06 22:18:36 [SIGNALING] WebSocket读取错误: read tcp *************:57548->52.81.131.193:8089: use of closed network connection
2025/06/06 22:18:36 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:18:36 [PEER] 已离开房间
2025/06/06 22:18:36 [PEER] 关闭媒体捕获器...
2025/06/06 22:18:36 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:18:36 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:18:36 [PEER] 信令错误: read tcp *************:57548->52.81.131.193:8089: use of closed network connection
2025/06/06 22:18:36 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:18:36 [ASTIAV] 开始清理资源...
2025/06/06 22:18:36 [ASTIAV] 释放输入包...
2025/06/06 22:18:36 [ASTIAV] 释放解码帧...
2025/06/06 22:18:36 [ASTIAV] 释放缩放帧...
2025/06/06 22:18:36 [ASTIAV] 释放编码包...
2025/06/06 22:18:36 [ASTIAV] 释放缩放上下文...
2025/06/06 22:18:36 [ASTIAV] 释放编码器上下文...
2025/06/06 22:18:36 [ASTIAV] 释放解码器上下文...
2025/06/06 22:18:36 [ASTIAV] 关闭输入上下文...
2025/06/06 22:18:36 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:18:36 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:18:36 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:18:36 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:18:36 [PEER] 关闭媒体播放器...
2025/06/06 22:18:36 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:18:36 [H264] ffplay已关闭
2025/06/06 22:18:36 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:18:36 [JPEG] ffplay已关闭
2025/06/06 22:18:36 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:18:36 [PLAYER] 媒体播放器已关闭
2025/06/06 22:18:36 [PEER] 关闭WebRTC管理器...
2025/06/06 22:18:36 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:18:36 [WEBRTC] 关闭数据通道...
2025/06/06 22:18:36 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:18:36 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:18:36 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:18:36 [WEBRTC] 连接状态变化: closed
2025/06/06 22:18:36 等待资源释放...
2025/06/06 22:18:37 [MEDIA] 视频捕获循环结束
2025/06/06 22:18:38 应用程序已安全关闭
2025/06/06 22:18:38 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:18:38 [PEER] 离开房间...
2025/06/06 22:18:38 [PEER] 离开房间
2025/06/06 22:18:38 [PEER] 已离开房间
2025/06/06 22:18:38 [PEER] 关闭媒体捕获器...
2025/06/06 22:18:38 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:18:38 [PEER] 关闭媒体播放器...
2025/06/06 22:18:38 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:18:38 [H264] ffplay已关闭
2025/06/06 22:18:38 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:18:38 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:18:38 [JPEG] ffplay已关闭
2025/06/06 22:18:38 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:18:38 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:18:38 [PLAYER] 媒体播放器已关闭
2025/06/06 22:18:38 [PEER] 关闭WebRTC管理器...
2025/06/06 22:18:38 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:18:38 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:18:38 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:18:40 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:18:40 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:18:40 配置文件: config/default.yaml
2025/06/06 22:18:40 调试模式: false
2025/06/06 22:18:40 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:18:40 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:18:40 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:18:40 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:18:40 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:18:40 [PEER] 初始化视频通话对等端
2025/06/06 22:18:40 [MEDIA] 初始化媒体捕获器
2025/06/06 22:18:40 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:18:40 [WEBRTC] 数据通道创建成功
2025/06/06 22:18:40 [WEBRTC] PeerConnection创建成功
2025/06/06 22:18:40 [PEER] 视频通话对等端初始化完成
2025/06/06 22:18:40 视频通话对等端初始化完成
2025/06/06 22:18:47 [PEER] 加入房间: abc003
2025/06/06 22:18:47 [SIGNALING] 连接到房间: abc003
2025/06/06 22:18:47 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:18:47 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"abc003","clientid":"51121917"}
2025/06/06 22:18:47 [SIGNALING] 成功连接到房间: abc003, ClientID: 51121917, IsInitiator: true
2025/06/06 22:18:47 [MEDIA] 开始媒体捕获
2025/06/06 22:18:47 [MEDIA] 启动视频捕获循环
2025/06/06 22:18:47 [PEER] 信令服务器已连接
2025/06/06 22:18:47 [MEDIA] 启动音频捕获循环
2025/06/06 22:18:47 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:18:47 [WEBRTC] 添加视频轨道: video
2025/06/06 22:18:47 [PLAYER] 开始媒体播放
2025/06/06 22:18:47 [PEER] 成功加入房间: abc003
2025/06/06 22:18:47 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:18:47 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:18:47 [ASTIAV] 调试模式已启用
2025/06/06 22:18:47 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:18:47 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:18:47 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:18:47 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:18:47 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:18:47 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:18:47 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:18:47 [ASTIAV] 打开输入: 0:none
2025/06/06 22:18:49 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:18:49 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:18:49 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:18:49 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:18:49 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:18:49 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:18:49 [ASTIAV] 捕获循环已启动
2025/06/06 22:18:49 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7541 bytes, 分辨率: 1024x600)
2025/06/06 22:18:49 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:18:51 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10406 bytes
2025/06/06 22:18:51 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10395 bytes, 分辨率: 1024x600)
2025/06/06 22:18:51 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:18:53 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10609 bytes
2025/06/06 22:18:53 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10657 bytes, 分辨率: 1024x600)
2025/06/06 22:18:53 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:18:55 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10646 bytes
2025/06/06 22:18:55 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10638 bytes, 分辨率: 1024x600)
2025/06/06 22:18:55 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:18:57 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10774 bytes
2025/06/06 22:18:58 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10758 bytes, 分辨率: 1024x600)
2025/06/06 22:18:58 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:19:00 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10842 bytes
2025/06/06 22:19:00 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10812 bytes, 分辨率: 1024x600)
2025/06/06 22:19:01 [PEER] 发起呼叫
2025/06/06 22:19:01 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:19:01 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:19:01 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:19:01 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:19:02 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10469 bytes
2025/06/06 22:19:02 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10469 bytes, 分辨率: 1024x600)
2025/06/06 22:19:04 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10616 bytes
2025/06/06 22:19:04 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10616 bytes, 分辨率: 1024x600)
2025/06/06 22:19:06 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10655 bytes
2025/06/06 22:19:06 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10655 bytes, 分辨率: 1024x600)
2025/06/06 22:19:09 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10696 bytes
2025/06/06 22:19:09 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10696 bytes, 分辨率: 1024x600)
2025/06/06 22:19:11 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10627 bytes
2025/06/06 22:19:11 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10627 bytes, 分辨率: 1024x600)
2025/06/06 22:19:13 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10631 bytes
2025/06/06 22:19:13 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10631 bytes, 分辨率: 1024x600)
2025/06/06 22:19:15 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10678 bytes
2025/06/06 22:19:15 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10678 bytes, 分辨率: 1024x600)
2025/06/06 22:19:17 [ASTIAV] 已捕获 390 帧, 当前帧大小: 10542 bytes
2025/06/06 22:19:17 [MEDIA] AstiAV成功捕获摄像头帧: 390 (大小: 10542 bytes, 分辨率: 1024x600)
2025/06/06 22:19:20 [ASTIAV] 已捕获 420 帧, 当前帧大小: 10563 bytes
2025/06/06 22:19:20 [MEDIA] AstiAV成功捕获摄像头帧: 420 (大小: 10563 bytes, 分辨率: 1024x600)
2025/06/06 22:19:22 [ASTIAV] 已捕获 450 帧, 当前帧大小: 10705 bytes
2025/06/06 22:19:22 [MEDIA] AstiAV成功捕获摄像头帧: 450 (大小: 10638 bytes, 分辨率: 1024x600)
2025/06/06 22:19:24 收到信号: interrupt
2025/06/06 22:19:24 正在关闭应用程序...
2025/06/06 22:19:24 正在关闭视频通话对等端...
2025/06/06 22:19:24 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:19:24 [PEER] 离开房间...
2025/06/06 22:19:24 [PEER] 离开房间
2025/06/06 22:19:24 [MEDIA] 停止媒体捕获
2025/06/06 22:19:24 [PLAYER] 停止媒体播放
2025/06/06 22:19:24 [SIGNALING] 断开信令连接
2025/06/06 22:19:24 [SIGNALING] WebSocket读取错误: read tcp *************:57680->52.81.131.193:8089: use of closed network connection
2025/06/06 22:19:24 [PEER] 信令错误: read tcp *************:57680->52.81.131.193:8089: use of closed network connection
2025/06/06 22:19:24 [PEER] 已离开房间
2025/06/06 22:19:24 [PEER] 关闭媒体捕获器...
2025/06/06 22:19:24 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:19:24 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:19:24 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:19:24 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:19:24 [ASTIAV] 开始清理资源...
2025/06/06 22:19:24 [ASTIAV] 释放输入包...
2025/06/06 22:19:24 [ASTIAV] 释放解码帧...
2025/06/06 22:19:24 [ASTIAV] 释放缩放帧...
2025/06/06 22:19:24 [ASTIAV] 释放编码包...
2025/06/06 22:19:24 [ASTIAV] 释放缩放上下文...
2025/06/06 22:19:24 [ASTIAV] 释放编码器上下文...
2025/06/06 22:19:24 [ASTIAV] 释放解码器上下文...
2025/06/06 22:19:24 [ASTIAV] 关闭输入上下文...
2025/06/06 22:19:24 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:19:24 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:19:24 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:19:24 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:19:24 [PEER] 关闭媒体播放器...
2025/06/06 22:19:24 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:19:24 [H264] ffplay已关闭
2025/06/06 22:19:24 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:19:24 [JPEG] ffplay已关闭
2025/06/06 22:19:24 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:19:24 [PLAYER] 媒体播放器已关闭
2025/06/06 22:19:24 [PEER] 关闭WebRTC管理器...
2025/06/06 22:19:24 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:19:24 [WEBRTC] 关闭数据通道...
2025/06/06 22:19:24 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:19:24 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:19:24 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:19:24 等待资源释放...
2025/06/06 22:19:24 [WEBRTC] 连接状态变化: closed
2025/06/06 22:19:24 [MEDIA] 视频捕获循环结束
2025/06/06 22:19:26 应用程序已安全关闭
2025/06/06 22:19:26 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:19:26 [PEER] 离开房间...
2025/06/06 22:19:26 [PEER] 离开房间
2025/06/06 22:19:26 [PEER] 已离开房间
2025/06/06 22:19:26 [PEER] 关闭媒体捕获器...
2025/06/06 22:19:26 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:19:26 [PEER] 关闭媒体播放器...
2025/06/06 22:19:26 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:19:26 [H264] ffplay已关闭
2025/06/06 22:19:26 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:19:26 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:19:26 [JPEG] ffplay已关闭
2025/06/06 22:19:26 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:19:26 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:19:26 [PLAYER] 媒体播放器已关闭
2025/06/06 22:19:26 [PEER] 关闭WebRTC管理器...
2025/06/06 22:19:26 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:19:26 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:19:26 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:19:27 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:19:27 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:19:27 配置文件: config/default.yaml
2025/06/06 22:19:27 调试模式: false
2025/06/06 22:19:27 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:19:27 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:19:27 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:19:28 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:19:28 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:19:28 [PEER] 初始化视频通话对等端
2025/06/06 22:19:28 [MEDIA] 初始化媒体捕获器
2025/06/06 22:19:28 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:19:28 [WEBRTC] 数据通道创建成功
2025/06/06 22:19:28 [WEBRTC] PeerConnection创建成功
2025/06/06 22:19:28 [PEER] 视频通话对等端初始化完成
2025/06/06 22:19:28 视频通话对等端初始化完成
2025/06/06 22:19:32 [PEER] 加入房间: abc004
2025/06/06 22:19:32 [SIGNALING] 连接到房间: abc004
2025/06/06 22:19:32 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:19:32 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"abc004","clientid":"47617264"}
2025/06/06 22:19:32 [SIGNALING] 成功连接到房间: abc004, ClientID: 47617264, IsInitiator: true
2025/06/06 22:19:32 [MEDIA] 开始媒体捕获
2025/06/06 22:19:32 [PEER] 信令服务器已连接
2025/06/06 22:19:32 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:19:32 [MEDIA] 启动音频捕获循环
2025/06/06 22:19:32 [MEDIA] 启动视频捕获循环
2025/06/06 22:19:32 [WEBRTC] 添加视频轨道: video
2025/06/06 22:19:32 [PLAYER] 开始媒体播放
2025/06/06 22:19:32 [PEER] 成功加入房间: abc004
2025/06/06 22:19:32 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:19:32 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:19:32 [ASTIAV] 调试模式已启用
2025/06/06 22:19:32 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:19:32 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:19:32 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:19:32 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:19:32 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:19:32 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:19:32 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:19:32 [ASTIAV] 打开输入: 0:none
2025/06/06 22:19:34 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:19:34 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:19:34 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:19:34 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:19:34 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:19:34 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:19:34 [ASTIAV] 捕获循环已启动
2025/06/06 22:19:34 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7505 bytes, 分辨率: 1024x600)
2025/06/06 22:19:34 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:19:36 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10436 bytes
2025/06/06 22:19:36 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10406 bytes, 分辨率: 1024x600)
2025/06/06 22:19:36 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:19:38 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10416 bytes
2025/06/06 22:19:38 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10322 bytes, 分辨率: 1024x600)
2025/06/06 22:19:38 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:19:40 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10554 bytes
2025/06/06 22:19:41 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10289 bytes, 分辨率: 1024x600)
2025/06/06 22:19:41 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:19:43 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10496 bytes
2025/06/06 22:19:43 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10500 bytes, 分辨率: 1024x600)
2025/06/06 22:19:43 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:19:45 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10729 bytes
2025/06/06 22:19:45 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10729 bytes, 分辨率: 1024x600)
2025/06/06 22:19:47 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10607 bytes
2025/06/06 22:19:47 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10607 bytes, 分辨率: 1024x600)
2025/06/06 22:19:48 [PEER] 发起呼叫
2025/06/06 22:19:48 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:19:48 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:19:48 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:19:48 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:19:49 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10692 bytes
2025/06/06 22:19:49 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10692 bytes, 分辨率: 1024x600)
2025/06/06 22:19:52 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10537 bytes
2025/06/06 22:19:52 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10537 bytes, 分辨率: 1024x600)
2025/06/06 22:19:54 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10726 bytes
2025/06/06 22:19:54 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10726 bytes, 分辨率: 1024x600)
2025/06/06 22:19:56 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10715 bytes
2025/06/06 22:19:56 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10715 bytes, 分辨率: 1024x600)
2025/06/06 22:19:58 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10702 bytes
2025/06/06 22:19:58 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10702 bytes, 分辨率: 1024x600)
2025/06/06 22:20:00 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10694 bytes
2025/06/06 22:20:00 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10694 bytes, 分辨率: 1024x600)
2025/06/06 22:20:03 [ASTIAV] 已捕获 390 帧, 当前帧大小: 10669 bytes
2025/06/06 22:20:03 [MEDIA] AstiAV成功捕获摄像头帧: 390 (大小: 10669 bytes, 分辨率: 1024x600)
2025/06/06 22:20:05 [ASTIAV] 已捕获 420 帧, 当前帧大小: 10740 bytes
2025/06/06 22:20:05 [MEDIA] AstiAV成功捕获摄像头帧: 420 (大小: 10740 bytes, 分辨率: 1024x600)
2025/06/06 22:20:07 [ASTIAV] 已捕获 450 帧, 当前帧大小: 10758 bytes
2025/06/06 22:20:07 [MEDIA] AstiAV成功捕获摄像头帧: 450 (大小: 10758 bytes, 分辨率: 1024x600)
2025/06/06 22:20:09 [ASTIAV] 已捕获 480 帧, 当前帧大小: 10769 bytes
2025/06/06 22:20:09 [MEDIA] AstiAV成功捕获摄像头帧: 480 (大小: 10769 bytes, 分辨率: 1024x600)
2025/06/06 22:20:12 [ASTIAV] 已捕获 510 帧, 当前帧大小: 10606 bytes
2025/06/06 22:20:12 [MEDIA] AstiAV成功捕获摄像头帧: 510 (大小: 10606 bytes, 分辨率: 1024x600)
2025/06/06 22:20:14 [ASTIAV] 已捕获 540 帧, 当前帧大小: 10769 bytes
2025/06/06 22:20:14 [MEDIA] AstiAV成功捕获摄像头帧: 540 (大小: 10769 bytes, 分辨率: 1024x600)
2025/06/06 22:20:16 [ASTIAV] 已捕获 570 帧, 当前帧大小: 10354 bytes
2025/06/06 22:20:16 [MEDIA] AstiAV成功捕获摄像头帧: 570 (大小: 10354 bytes, 分辨率: 1024x600)
2025/06/06 22:20:18 [PEER] 状态变化: Ringing -> Disconnected
2025/06/06 22:20:18 [ASTIAV] 已捕获 600 帧, 当前帧大小: 10522 bytes
2025/06/06 22:20:18 [MEDIA] AstiAV成功捕获摄像头帧: 600 (大小: 10522 bytes, 分辨率: 1024x600)
2025/06/06 22:20:20 [ASTIAV] 已捕获 630 帧, 当前帧大小: 10542 bytes
2025/06/06 22:20:20 [MEDIA] AstiAV成功捕获摄像头帧: 630 (大小: 10542 bytes, 分辨率: 1024x600)
2025/06/06 22:20:23 [ASTIAV] 已捕获 660 帧, 当前帧大小: 10597 bytes
2025/06/06 22:20:23 [MEDIA] AstiAV成功捕获摄像头帧: 660 (大小: 10597 bytes, 分辨率: 1024x600)
2025/06/06 22:20:23 收到信号: interrupt
2025/06/06 22:20:23 正在关闭应用程序...
2025/06/06 22:20:23 正在关闭视频通话对等端...
2025/06/06 22:20:23 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:20:23 [PEER] 离开房间...
2025/06/06 22:20:23 [PEER] 离开房间
2025/06/06 22:20:23 [MEDIA] 停止媒体捕获
2025/06/06 22:20:23 [PLAYER] 停止媒体播放
2025/06/06 22:20:23 [SIGNALING] 断开信令连接
2025/06/06 22:20:23 [SIGNALING] WebSocket读取错误: read tcp *************:57841->52.81.131.193:8089: use of closed network connection
2025/06/06 22:20:23 [PEER] 已离开房间
2025/06/06 22:20:23 [PEER] 信令错误: read tcp *************:57841->52.81.131.193:8089: use of closed network connection
2025/06/06 22:20:23 [PEER] 关闭媒体捕获器...
2025/06/06 22:20:23 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:20:23 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:20:23 [PEER] 状态变化: Disconnected -> Idle
2025/06/06 22:20:23 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:20:23 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:20:23 [ASTIAV] 开始清理资源...
2025/06/06 22:20:23 [ASTIAV] 释放输入包...
2025/06/06 22:20:23 [ASTIAV] 释放解码帧...
2025/06/06 22:20:23 [ASTIAV] 释放缩放帧...
2025/06/06 22:20:23 [ASTIAV] 释放编码包...
2025/06/06 22:20:23 [ASTIAV] 释放缩放上下文...
2025/06/06 22:20:23 [ASTIAV] 释放编码器上下文...
2025/06/06 22:20:23 [ASTIAV] 释放解码器上下文...
2025/06/06 22:20:23 [ASTIAV] 关闭输入上下文...
2025/06/06 22:20:23 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:20:24 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:20:24 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:20:24 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:20:24 [MEDIA] 视频捕获循环结束
2025/06/06 22:20:24 [PEER] 关闭媒体播放器...
2025/06/06 22:20:24 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:20:24 [H264] ffplay已关闭
2025/06/06 22:20:24 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:20:24 [JPEG] ffplay已关闭
2025/06/06 22:20:24 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:20:24 [PLAYER] 媒体播放器已关闭
2025/06/06 22:20:24 [PEER] 关闭WebRTC管理器...
2025/06/06 22:20:24 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:20:24 [WEBRTC] 关闭数据通道...
2025/06/06 22:20:24 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:20:24 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:20:24 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:20:24 等待资源释放...
2025/06/06 22:20:24 [WEBRTC] 连接状态变化: closed
2025/06/06 22:20:26 应用程序已安全关闭
2025/06/06 22:20:26 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:20:26 [PEER] 离开房间...
2025/06/06 22:20:26 [PEER] 离开房间
2025/06/06 22:20:26 [PEER] 已离开房间
2025/06/06 22:20:26 [PEER] 关闭媒体捕获器...
2025/06/06 22:20:26 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:20:26 [PEER] 关闭媒体播放器...
2025/06/06 22:20:26 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:20:26 [H264] ffplay已关闭
2025/06/06 22:20:26 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:20:26 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:20:26 [JPEG] ffplay已关闭
2025/06/06 22:20:26 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:20:26 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:20:26 [PLAYER] 媒体播放器已关闭
2025/06/06 22:20:26 [PEER] 关闭WebRTC管理器...
2025/06/06 22:20:26 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:20:26 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:20:26 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:20:27 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:20:27 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:20:27 配置文件: config/default.yaml
2025/06/06 22:20:27 调试模式: false
2025/06/06 22:20:27 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:20:27 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:20:27 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:20:27 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:20:27 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:20:27 [PEER] 初始化视频通话对等端
2025/06/06 22:20:27 [MEDIA] 初始化媒体捕获器
2025/06/06 22:20:27 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:20:27 [WEBRTC] 数据通道创建成功
2025/06/06 22:20:27 [WEBRTC] PeerConnection创建成功
2025/06/06 22:20:27 [PEER] 视频通话对等端初始化完成
2025/06/06 22:20:27 视频通话对等端初始化完成
2025/06/06 22:20:33 [PEER] 加入房间: abc009
2025/06/06 22:20:33 [SIGNALING] 连接到房间: abc009
2025/06/06 22:20:33 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:20:34 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"abc009","clientid":"36053028"}
2025/06/06 22:20:34 [SIGNALING] 成功连接到房间: abc009, ClientID: 36053028, IsInitiator: true
2025/06/06 22:20:34 [MEDIA] 开始媒体捕获
2025/06/06 22:20:34 [PEER] 信令服务器已连接
2025/06/06 22:20:34 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:20:34 [MEDIA] 启动音频捕获循环
2025/06/06 22:20:34 [MEDIA] 启动视频捕获循环
2025/06/06 22:20:34 [WEBRTC] 添加视频轨道: video
2025/06/06 22:20:34 [PLAYER] 开始媒体播放
2025/06/06 22:20:34 [PEER] 成功加入房间: abc009
2025/06/06 22:20:34 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:20:34 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:20:34 [ASTIAV] 调试模式已启用
2025/06/06 22:20:34 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:20:34 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:20:34 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:20:34 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:20:34 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:20:34 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:20:34 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:20:34 [ASTIAV] 打开输入: 0:none
2025/06/06 22:20:35 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:20:35 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:20:35 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:20:35 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:20:35 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:20:35 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:20:35 [ASTIAV] 捕获循环已启动
2025/06/06 22:20:35 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7572 bytes, 分辨率: 1024x600)
2025/06/06 22:20:35 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:20:37 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10328 bytes
2025/06/06 22:20:37 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10457 bytes, 分辨率: 1024x600)
2025/06/06 22:20:37 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:20:40 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10607 bytes
2025/06/06 22:20:40 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10668 bytes, 分辨率: 1024x600)
2025/06/06 22:20:40 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:20:42 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10748 bytes
2025/06/06 22:20:42 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10690 bytes, 分辨率: 1024x600)
2025/06/06 22:20:42 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:20:44 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10738 bytes
2025/06/06 22:20:44 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10686 bytes, 分辨率: 1024x600)
2025/06/06 22:20:44 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:20:45 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:20:46 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10649 bytes
2025/06/06 22:20:46 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10649 bytes, 分辨率: 1024x600)
2025/06/06 22:20:47 [PEER] 发起呼叫
2025/06/06 22:20:47 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:20:47 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:20:47 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:20:47 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:20:48 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10696 bytes
2025/06/06 22:20:48 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10696 bytes, 分辨率: 1024x600)
2025/06/06 22:20:50 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:20:51 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10617 bytes, 分辨率: 1024x600)
2025/06/06 22:20:51 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10673 bytes
2025/06/06 22:20:53 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10671 bytes, 分辨率: 1024x600)
2025/06/06 22:20:53 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10664 bytes
2025/06/06 22:20:55 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10670 bytes, 分辨率: 1024x600)
2025/06/06 22:20:55 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10691 bytes
2025/06/06 22:20:57 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10636 bytes, 分辨率: 1024x600)
2025/06/06 22:20:57 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10729 bytes
2025/06/06 22:20:59 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10556 bytes, 分辨率: 1024x600)
2025/06/06 22:21:00 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10512 bytes
2025/06/06 22:21:00 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}","error":""}
2025/06/06 22:21:00 [SIGNALING] 收到自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:21:00 [PEER] 处理自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:21:00 [PEER] 创建并发送Offer
2025/06/06 22:21:00 [PEER] 状态变化: Ringing -> Negotiating
2025/06/06 22:21:00 [WEBRTC] 连接状态变化: closed
2025/06/06 22:21:00 [PEER] WebRTC连接状态: closed
2025/06/06 22:21:00 [WEBRTC] 数据通道创建成功
2025/06/06 22:21:00 [WEBRTC] PeerConnection创建成功
2025/06/06 22:21:00 [MEDIA] 数据通道发送器已设置
2025/06/06 22:21:00 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:21:00 [WEBRTC] 信令状态变化: have-local-offer
2025/06/06 22:21:00 [WEBRTC] 创建并设置Offer成功
2025/06/06 22:21:00 [WEBRTC] 生成ICE候选: udp4 host **************:63957 (resolved: **************:63957)
2025/06/06 22:21:00 [WEBRTC] 生成ICE候选: udp4 host *************:51859 (resolved: *************:51859)
2025/06/06 22:21:00 [SIGNALING] 发送ICE候选
2025/06/06 22:21:00 [SIGNALING] 发送ICE候选
2025/06/06 22:21:00 [SIGNALING] 发送SDP offer
2025/06/06 22:21:00 [PEER] Offer已发送
2025/06/06 22:21:01 [WEBRTC] 生成ICE候选: udp4 srflx ***************:55241 related 0.0.0.0:55241 (resolved: ***************:55241)
2025/06/06 22:21:01 [WEBRTC] 生成ICE候选: udp4 srflx ***************:49197 related 0.0.0.0:49197 (resolved: ***************:49197)
2025/06/06 22:21:01 [SIGNALING] 发送ICE候选
2025/06/06 22:21:01 [SIGNALING] 发送ICE候选
2025/06/06 22:21:02 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10602 bytes, 分辨率: 1024x600)
2025/06/06 22:21:02 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10542 bytes
2025/06/06 22:21:04 [MEDIA] AstiAV成功捕获摄像头帧: 390 (大小: 10702 bytes, 分辨率: 1024x600)
2025/06/06 22:21:04 [ASTIAV] 已捕获 390 帧, 当前帧大小: 10673 bytes
2025/06/06 22:21:06 [MEDIA] AstiAV成功捕获摄像头帧: 420 (大小: 10656 bytes, 分辨率: 1024x600)
2025/06/06 22:21:06 [ASTIAV] 已捕获 420 帧, 当前帧大小: 10631 bytes
2025/06/06 22:21:08 [MEDIA] AstiAV成功捕获摄像头帧: 450 (大小: 10656 bytes, 分辨率: 1024x600)
2025/06/06 22:21:08 [ASTIAV] 已捕获 450 帧, 当前帧大小: 10595 bytes
2025/06/06 22:21:11 [MEDIA] AstiAV成功捕获摄像头帧: 480 (大小: 10676 bytes, 分辨率: 1024x600)
2025/06/06 22:21:11 [ASTIAV] 已捕获 480 帧, 当前帧大小: 10596 bytes
2025/06/06 22:21:13 [MEDIA] AstiAV成功捕获摄像头帧: 510 (大小: 10687 bytes, 分辨率: 1024x600)
2025/06/06 22:21:13 [ASTIAV] 已捕获 510 帧, 当前帧大小: 10648 bytes
2025/06/06 22:21:13 收到信号: interrupt
2025/06/06 22:21:13 正在关闭应用程序...
2025/06/06 22:21:13 正在关闭视频通话对等端...
2025/06/06 22:21:13 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:21:13 [PEER] 离开房间...
2025/06/06 22:21:13 [PEER] 离开房间
2025/06/06 22:21:13 [MEDIA] 停止媒体捕获
2025/06/06 22:21:13 [PLAYER] 停止媒体播放
2025/06/06 22:21:13 [SIGNALING] 断开信令连接
2025/06/06 22:21:13 [SIGNALING] WebSocket读取错误: read tcp *************:58061->52.81.131.193:8089: use of closed network connection
2025/06/06 22:21:13 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:21:13 [PEER] 已离开房间
2025/06/06 22:21:13 [PEER] 关闭媒体捕获器...
2025/06/06 22:21:13 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:21:13 [PEER] 信令错误: read tcp *************:58061->52.81.131.193:8089: use of closed network connection
2025/06/06 22:21:13 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:21:13 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:21:13 [ASTIAV] 开始清理资源...
2025/06/06 22:21:13 [ASTIAV] 释放输入包...
2025/06/06 22:21:13 [ASTIAV] 释放解码帧...
2025/06/06 22:21:13 [ASTIAV] 释放缩放帧...
2025/06/06 22:21:13 [ASTIAV] 释放编码包...
2025/06/06 22:21:13 [ASTIAV] 释放缩放上下文...
2025/06/06 22:21:13 [ASTIAV] 释放编码器上下文...
2025/06/06 22:21:13 [ASTIAV] 释放解码器上下文...
2025/06/06 22:21:13 [ASTIAV] 关闭输入上下文...
2025/06/06 22:21:13 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:21:13 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:21:13 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:21:13 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:21:13 [PEER] 关闭媒体播放器...
2025/06/06 22:21:13 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:21:13 [H264] ffplay已关闭
2025/06/06 22:21:13 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:21:13 [JPEG] ffplay已关闭
2025/06/06 22:21:13 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:21:13 [PLAYER] 媒体播放器已关闭
2025/06/06 22:21:13 [PEER] 关闭WebRTC管理器...
2025/06/06 22:21:13 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:21:13 [WEBRTC] 关闭数据通道...
2025/06/06 22:21:13 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:21:13 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:21:13 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:21:13 等待资源释放...
2025/06/06 22:21:13 [WEBRTC] 连接状态变化: closed
2025/06/06 22:21:14 [MEDIA] 视频捕获循环结束
2025/06/06 22:21:15 应用程序已安全关闭
2025/06/06 22:21:15 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:21:15 [PEER] 离开房间...
2025/06/06 22:21:15 [PEER] 离开房间
2025/06/06 22:21:15 [PEER] 已离开房间
2025/06/06 22:21:15 [PEER] 关闭媒体捕获器...
2025/06/06 22:21:15 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:21:15 [PEER] 关闭媒体播放器...
2025/06/06 22:21:15 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:21:15 [H264] ffplay已关闭
2025/06/06 22:21:15 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:21:15 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:21:15 [JPEG] ffplay已关闭
2025/06/06 22:21:15 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:21:15 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:21:15 [PLAYER] 媒体播放器已关闭
2025/06/06 22:21:15 [PEER] 关闭WebRTC管理器...
2025/06/06 22:21:15 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:21:15 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:21:15 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:25:59 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:25:59 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:25:59 配置文件: config/default.yaml
2025/06/06 22:25:59 调试模式: false
2025/06/06 22:25:59 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:25:59 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:25:59 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:26:00 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:26:00 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:26:00 [PEER] 初始化视频通话对等端
2025/06/06 22:26:00 [MEDIA] 初始化媒体捕获器
2025/06/06 22:26:00 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:26:00 [WEBRTC] 数据通道创建成功
2025/06/06 22:26:00 [WEBRTC] PeerConnection创建成功
2025/06/06 22:26:00 [PEER] 视频通话对等端初始化完成
2025/06/06 22:26:00 视频通话对等端初始化完成
2025/06/06 22:26:09 [PEER] 加入房间: 11112222
2025/06/06 22:26:09 [SIGNALING] 连接到房间: 11112222
2025/06/06 22:26:09 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:26:09 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"11112222","clientid":"39100820"}
2025/06/06 22:26:09 [SIGNALING] 成功连接到房间: 11112222, ClientID: 39100820, IsInitiator: true
2025/06/06 22:26:09 [MEDIA] 开始媒体捕获
2025/06/06 22:26:09 [PEER] 信令服务器已连接
2025/06/06 22:26:09 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:26:09 [MEDIA] 启动音频捕获循环
2025/06/06 22:26:09 [MEDIA] 启动视频捕获循环
2025/06/06 22:26:09 [WEBRTC] 添加视频轨道: video
2025/06/06 22:26:09 [PLAYER] 开始媒体播放
2025/06/06 22:26:09 [PEER] 成功加入房间: 11112222
2025/06/06 22:26:09 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:26:09 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:26:09 [ASTIAV] 调试模式已启用
2025/06/06 22:26:09 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:26:09 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:26:09 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:26:09 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:26:09 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:26:09 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:26:09 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:26:09 [ASTIAV] 打开输入: 0:none
2025/06/06 22:26:11 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:26:11 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:26:11 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:26:11 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:26:11 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:26:11 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:26:11 [ASTIAV] 捕获循环已启动
2025/06/06 22:26:11 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7615 bytes, 分辨率: 1024x600)
2025/06/06 22:26:11 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:26:13 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10293 bytes
2025/06/06 22:26:14 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10306 bytes, 分辨率: 1024x600)
2025/06/06 22:26:14 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:26:16 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10420 bytes
2025/06/06 22:26:16 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10050 bytes, 分辨率: 1024x600)
2025/06/06 22:26:16 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:26:18 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10368 bytes
2025/06/06 22:26:18 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10559 bytes, 分辨率: 1024x600)
2025/06/06 22:26:18 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:26:20 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10581 bytes
2025/06/06 22:26:20 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10632 bytes, 分辨率: 1024x600)
2025/06/06 22:26:20 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:26:22 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10675 bytes
2025/06/06 22:26:22 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10643 bytes, 分辨率: 1024x600)
2025/06/06 22:26:25 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10333 bytes
2025/06/06 22:26:25 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10490 bytes, 分辨率: 1024x600)
2025/06/06 22:26:27 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10553 bytes
2025/06/06 22:26:27 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10553 bytes, 分辨率: 1024x600)
2025/06/06 22:26:29 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10620 bytes
2025/06/06 22:26:29 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10620 bytes, 分辨率: 1024x600)
2025/06/06 22:26:31 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10669 bytes, 分辨率: 1024x600)
2025/06/06 22:26:31 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10713 bytes
2025/06/06 22:26:32 [PEER] 发起呼叫
2025/06/06 22:26:32 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:26:32 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:26:32 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:26:32 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:26:34 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10733 bytes, 分辨率: 1024x600)
2025/06/06 22:26:34 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10553 bytes
2025/06/06 22:26:36 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10587 bytes, 分辨率: 1024x600)
2025/06/06 22:26:36 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}","error":""}
2025/06/06 22:26:36 [SIGNALING] 收到自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:26:36 [PEER] 处理自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:26:36 [PEER] 创建并发送Offer
2025/06/06 22:26:36 [PEER] 当前WebRTC连接状态: new
2025/06/06 22:26:36 [PEER] 状态变化: Ringing -> Negotiating
2025/06/06 22:26:36 [WEBRTC] 连接状态变化: closed
2025/06/06 22:26:36 [PEER] WebRTC连接状态: closed
2025/06/06 22:26:36 [WEBRTC] 数据通道创建成功
2025/06/06 22:26:36 [WEBRTC] PeerConnection创建成功
2025/06/06 22:26:36 [MEDIA] 数据通道发送器已设置
2025/06/06 22:26:36 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:26:36 [WEBRTC] 创建并设置Offer成功
2025/06/06 22:26:36 [WEBRTC] 信令状态变化: have-local-offer
2025/06/06 22:26:36 [WEBRTC] 生成ICE候选: udp4 host **************:62830 (resolved: **************:62830)
2025/06/06 22:26:36 [WEBRTC] 生成ICE候选: udp4 host *************:64107 (resolved: *************:64107)
2025/06/06 22:26:36 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10477 bytes
2025/06/06 22:26:36 [SIGNALING] 发送ICE候选
2025/06/06 22:26:36 [SIGNALING] 发送SDP offer
2025/06/06 22:26:36 [PEER] Offer已发送
2025/06/06 22:26:36 [SIGNALING] 发送ICE候选
2025/06/06 22:26:37 [WEBRTC] 生成ICE候选: udp4 srflx ***************:52817 related 0.0.0.0:52817 (resolved: ***************:52817)
2025/06/06 22:26:37 [WEBRTC] 生成ICE候选: udp4 srflx ***************:64560 related 0.0.0.0:64560 (resolved: ***************:64560)
2025/06/06 22:26:37 [SIGNALING] 发送ICE候选
2025/06/06 22:26:37 [SIGNALING] 发送ICE候选
2025/06/06 22:26:37 [SIGNALING] 收到消息: {"msg":"{\"type\":\"answer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:0\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693230847 *************** 60582 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15129343 ************* 59098 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129438463 ************** 60582 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:1\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693230847 *************** 60582 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15129343 ************* 59098 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129438463 ************** 60582 typ host\\r\\n\"}","error":""}
2025/06/06 22:26:37 [PEER] 处理RTC消息: answer
2025/06/06 22:26:37 [PEER] 处理接收到的Answer
2025/06/06 22:26:37 [WEBRTC] 信令状态变化: stable
2025/06/06 22:26:37 [WEBRTC] 设置远程answer描述成功
2025/06/06 22:26:37 [PEER] Answer已处理
2025/06/06 22:26:37 [WEBRTC] ICE连接状态变化: checking
2025/06/06 22:26:37 [WEBRTC] 连接状态变化: connecting
2025/06/06 22:26:37 [PEER] WebRTC连接状态: connecting
2025/06/06 22:26:37 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:26:37 [WEBRTC] ICE连接状态变化: connected
2025/06/06 22:26:38 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10805 bytes, 分辨率: 1024x600)
2025/06/06 22:26:38 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10553 bytes
2025/06/06 22:26:40 [MEDIA] AstiAV成功捕获摄像头帧: 390 (大小: 10835 bytes, 分辨率: 1024x600)
2025/06/06 22:26:41 [ASTIAV] 已捕获 390 帧, 当前帧大小: 10752 bytes
2025/06/06 22:26:43 [MEDIA] AstiAV成功捕获摄像头帧: 420 (大小: 10677 bytes, 分辨率: 1024x600)
2025/06/06 22:26:43 [ASTIAV] 已捕获 420 帧, 当前帧大小: 10540 bytes
2025/06/06 22:26:45 [MEDIA] AstiAV成功捕获摄像头帧: 450 (大小: 10764 bytes, 分辨率: 1024x600)
2025/06/06 22:26:45 [ASTIAV] 已捕获 450 帧, 当前帧大小: 10779 bytes
2025/06/06 22:26:46 [WEBRTC] ICE连接状态变化: disconnected
2025/06/06 22:26:46 [WEBRTC] 连接状态变化: disconnected
2025/06/06 22:26:46 [PEER] WebRTC连接状态: disconnected
2025/06/06 22:26:47 [MEDIA] AstiAV成功捕获摄像头帧: 480 (大小: 10816 bytes, 分辨率: 1024x600)
2025/06/06 22:26:47 [ASTIAV] 已捕获 480 帧, 当前帧大小: 10734 bytes
2025/06/06 22:26:49 [MEDIA] AstiAV成功捕获摄像头帧: 510 (大小: 10825 bytes, 分辨率: 1024x600)
2025/06/06 22:26:50 [ASTIAV] 已捕获 510 帧, 当前帧大小: 10763 bytes
2025/06/06 22:26:52 [MEDIA] AstiAV成功捕获摄像头帧: 540 (大小: 10934 bytes, 分辨率: 1024x600)
2025/06/06 22:26:52 [ASTIAV] 已捕获 540 帧, 当前帧大小: 11031 bytes
2025/06/06 22:26:52 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:26:54 [MEDIA] AstiAV成功捕获摄像头帧: 570 (大小: 10797 bytes, 分辨率: 1024x600)
2025/06/06 22:26:54 [ASTIAV] 已捕获 570 帧, 当前帧大小: 10669 bytes
2025/06/06 22:26:56 [MEDIA] AstiAV成功捕获摄像头帧: 600 (大小: 10693 bytes, 分辨率: 1024x600)
2025/06/06 22:26:56 [ASTIAV] 已捕获 600 帧, 当前帧大小: 10650 bytes
2025/06/06 22:26:57 收到信号: interrupt
2025/06/06 22:26:57 正在关闭应用程序...
2025/06/06 22:26:57 正在关闭视频通话对等端...
2025/06/06 22:26:57 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:26:57 [PEER] 离开房间...
2025/06/06 22:26:57 [PEER] 离开房间
2025/06/06 22:26:57 [MEDIA] 停止媒体捕获
2025/06/06 22:26:57 [PLAYER] 停止媒体播放
2025/06/06 22:26:57 [SIGNALING] 断开信令连接
2025/06/06 22:26:57 [SIGNALING] WebSocket读取错误: read tcp *************:59291->52.81.131.193:8089: use of closed network connection
2025/06/06 22:26:57 [PEER] 已离开房间
2025/06/06 22:26:57 [PEER] 关闭媒体捕获器...
2025/06/06 22:26:57 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:26:57 [PEER] 信令错误: read tcp *************:59291->52.81.131.193:8089: use of closed network connection
2025/06/06 22:26:57 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:26:57 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:26:57 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:26:57 [ASTIAV] 开始清理资源...
2025/06/06 22:26:57 [ASTIAV] 释放输入包...
2025/06/06 22:26:57 [ASTIAV] 释放解码帧...
2025/06/06 22:26:57 [ASTIAV] 释放缩放帧...
2025/06/06 22:26:57 [ASTIAV] 释放编码包...
2025/06/06 22:26:57 [ASTIAV] 释放缩放上下文...
2025/06/06 22:26:57 [ASTIAV] 释放编码器上下文...
2025/06/06 22:26:57 [ASTIAV] 释放解码器上下文...
2025/06/06 22:26:57 [ASTIAV] 关闭输入上下文...
2025/06/06 22:26:57 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:26:57 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:26:57 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:26:57 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:26:57 [PEER] 关闭媒体播放器...
2025/06/06 22:26:57 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:26:57 [MEDIA] 视频捕获循环结束
2025/06/06 22:26:57 [H264] ffplay已关闭
2025/06/06 22:26:57 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:26:57 [JPEG] ffplay已关闭
2025/06/06 22:26:57 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:26:57 [PLAYER] 媒体播放器已关闭
2025/06/06 22:26:57 [PEER] 关闭WebRTC管理器...
2025/06/06 22:26:57 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:26:57 [WEBRTC] 关闭数据通道...
2025/06/06 22:26:57 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:26:57 [WEBRTC] 连接状态变化: closed
2025/06/06 22:26:57 [PEER] WebRTC连接状态: closed
2025/06/06 22:26:57 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:26:57 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:26:57 等待资源释放...
2025/06/06 22:26:57 [WEBRTC] ICE连接状态变化: closed
2025/06/06 22:26:59 应用程序已安全关闭
2025/06/06 22:26:59 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:26:59 [PEER] 离开房间...
2025/06/06 22:26:59 [PEER] 离开房间
2025/06/06 22:26:59 [PEER] 已离开房间
2025/06/06 22:26:59 [PEER] 关闭媒体捕获器...
2025/06/06 22:26:59 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:26:59 [PEER] 关闭媒体播放器...
2025/06/06 22:26:59 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:26:59 [H264] ffplay已关闭
2025/06/06 22:26:59 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:26:59 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:26:59 [JPEG] ffplay已关闭
2025/06/06 22:26:59 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:26:59 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:26:59 [PLAYER] 媒体播放器已关闭
2025/06/06 22:26:59 [PEER] 关闭WebRTC管理器...
2025/06/06 22:26:59 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:26:59 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:26:59 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:30:32 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:30:32 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:30:32 配置文件: config/default.yaml
2025/06/06 22:30:32 调试模式: false
2025/06/06 22:30:32 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:30:32 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:30:32 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:30:33 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:30:33 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:30:33 [PEER] 初始化视频通话对等端
2025/06/06 22:30:33 [MEDIA] 初始化媒体捕获器
2025/06/06 22:30:33 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:30:33 [WEBRTC] 数据通道创建成功
2025/06/06 22:30:33 [WEBRTC] 初始数据通道状态: connecting
2025/06/06 22:30:33 [WEBRTC] PeerConnection创建成功
2025/06/06 22:30:33 [PEER] 视频通话对等端初始化完成
2025/06/06 22:30:33 视频通话对等端初始化完成
2025/06/06 22:30:35 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:37 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:39 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:41 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:43 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:45 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:47 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:49 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:50 [PEER] 加入房间: 22223333
2025/06/06 22:30:50 [SIGNALING] 连接到房间: 22223333
2025/06/06 22:30:50 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:30:50 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"22223333","clientid":"29966174"}
2025/06/06 22:30:50 [SIGNALING] 成功连接到房间: 22223333, ClientID: 29966174, IsInitiator: true
2025/06/06 22:30:50 [MEDIA] 开始媒体捕获
2025/06/06 22:30:50 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:30:50 [MEDIA] 启动视频捕获循环
2025/06/06 22:30:50 [PEER] 信令服务器已连接
2025/06/06 22:30:50 [MEDIA] 启动音频捕获循环
2025/06/06 22:30:50 [WEBRTC] 添加视频轨道: video
2025/06/06 22:30:50 [PLAYER] 开始媒体播放
2025/06/06 22:30:50 [PEER] 成功加入房间: 22223333
2025/06/06 22:30:50 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:30:50 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:30:50 [ASTIAV] 调试模式已启用
2025/06/06 22:30:50 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:30:50 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:30:50 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:30:50 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:30:50 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:30:50 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:30:50 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:30:50 [ASTIAV] 打开输入: 0:none
2025/06/06 22:30:51 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:52 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:30:52 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:30:52 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:30:52 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:30:52 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:30:52 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:30:52 [ASTIAV] 捕获循环已启动
2025/06/06 22:30:52 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7665 bytes, 分辨率: 1024x600)
2025/06/06 22:30:52 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:30:53 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:54 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10818 bytes
2025/06/06 22:30:54 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10801 bytes, 分辨率: 1024x600)
2025/06/06 22:30:54 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:30:55 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:56 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10481 bytes
2025/06/06 22:30:56 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10486 bytes, 分辨率: 1024x600)
2025/06/06 22:30:56 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:30:57 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:30:59 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10378 bytes
2025/06/06 22:30:59 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:30:59 [MEDIA] 生成高质量测试图像: 红色, 帧 90 (大小: 2865 bytes)
2025/06/06 22:30:59 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:01 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10841 bytes
2025/06/06 22:31:01 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10841 bytes, 分辨率: 1024x600)
2025/06/06 22:31:01 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:31:01 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:03 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10785 bytes
2025/06/06 22:31:03 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10785 bytes, 分辨率: 1024x600)
2025/06/06 22:31:03 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:05 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:05 [PEER] 发起呼叫
2025/06/06 22:31:05 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:31:05 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:31:05 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:31:05 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:31:05 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10655 bytes, 分辨率: 1024x600)
2025/06/06 22:31:05 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10671 bytes
2025/06/06 22:31:07 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:08 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10590 bytes, 分辨率: 1024x600)
2025/06/06 22:31:08 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10467 bytes
2025/06/06 22:31:08 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}","error":""}
2025/06/06 22:31:08 [SIGNALING] 收到自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:31:08 [PEER] 处理自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:31:08 [PEER] 创建并发送Offer
2025/06/06 22:31:08 [PEER] 当前WebRTC连接状态: new
2025/06/06 22:31:08 [PEER] 状态变化: Ringing -> Negotiating
2025/06/06 22:31:08 [WEBRTC] 连接状态变化: closed
2025/06/06 22:31:08 [PEER] WebRTC连接状态: closed
2025/06/06 22:31:08 [WEBRTC] 数据通道创建成功
2025/06/06 22:31:08 [WEBRTC] 初始数据通道状态: connecting
2025/06/06 22:31:08 [WEBRTC] PeerConnection创建成功
2025/06/06 22:31:08 [MEDIA] 数据通道发送器已设置
2025/06/06 22:31:08 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:31:08 [WEBRTC] 创建并设置Offer成功
2025/06/06 22:31:08 [WEBRTC] 信令状态变化: have-local-offer
2025/06/06 22:31:08 [WEBRTC] 生成ICE候选: udp4 host **************:55294 (resolved: **************:55294)
2025/06/06 22:31:08 [WEBRTC] 生成ICE候选: udp4 host *************:54235 (resolved: *************:54235)
2025/06/06 22:31:09 [SIGNALING] 发送ICE候选
2025/06/06 22:31:09 [SIGNALING] 发送ICE候选
2025/06/06 22:31:09 [SIGNALING] 发送SDP offer
2025/06/06 22:31:09 [PEER] Offer已发送
2025/06/06 22:31:09 [WEBRTC] 生成ICE候选: udp4 srflx ***************:64978 related 0.0.0.0:64978 (resolved: ***************:64978)
2025/06/06 22:31:09 [WEBRTC] 生成ICE候选: udp4 srflx ***************:50235 related 0.0.0.0:50235 (resolved: ***************:50235)
2025/06/06 22:31:09 [WEBRTC] 数据通道状态监控: closed
2025/06/06 22:31:09 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/06 22:31:09 [SIGNALING] 发送ICE候选
2025/06/06 22:31:09 [SIGNALING] 发送ICE候选
2025/06/06 22:31:09 [SIGNALING] 收到消息: {"msg":"{\"type\":\"answer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:0\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693231103 *************** 60583 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15106303 ************* 59008 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129438719 ************** 60583 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:1\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693231103 *************** 60583 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15106303 ************* 59008 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129438719 ************** 60583 typ host\\r\\n\"}","error":""}
2025/06/06 22:31:09 [PEER] 处理RTC消息: answer
2025/06/06 22:31:09 [PEER] 处理接收到的Answer
2025/06/06 22:31:09 [WEBRTC] 信令状态变化: stable
2025/06/06 22:31:09 [WEBRTC] 设置远程answer描述成功
2025/06/06 22:31:09 [PEER] Answer已处理
2025/06/06 22:31:09 [WEBRTC] ICE连接状态变化: checking
2025/06/06 22:31:09 [WEBRTC] 连接状态变化: connecting
2025/06/06 22:31:09 [PEER] WebRTC连接状态: connecting
2025/06/06 22:31:09 [WEBRTC] ICE连接状态变化: connected
2025/06/06 22:31:10 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10782 bytes, 分辨率: 1024x600)
2025/06/06 22:31:10 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10739 bytes
2025/06/06 22:31:10 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:12 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10769 bytes, 分辨率: 1024x600)
2025/06/06 22:31:12 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10712 bytes
2025/06/06 22:31:12 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:14 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10781 bytes, 分辨率: 1024x600)
2025/06/06 22:31:14 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:14 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10774 bytes
2025/06/06 22:31:16 [WEBRTC] ICE连接状态变化: disconnected
2025/06/06 22:31:16 [WEBRTC] 连接状态变化: disconnected
2025/06/06 22:31:16 [PEER] WebRTC连接状态: disconnected
2025/06/06 22:31:16 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:17 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10799 bytes, 分辨率: 1024x600)
2025/06/06 22:31:17 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10809 bytes
2025/06/06 22:31:18 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:19 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10768 bytes, 分辨率: 1024x600)
2025/06/06 22:31:19 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10687 bytes
2025/06/06 22:31:20 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:21 [MEDIA] AstiAV成功捕获摄像头帧: 390 (大小: 10978 bytes, 分辨率: 1024x600)
2025/06/06 22:31:21 [ASTIAV] 已捕获 390 帧, 当前帧大小: 10925 bytes
2025/06/06 22:31:22 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:31:22 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:23 [MEDIA] AstiAV成功捕获摄像头帧: 420 (大小: 10655 bytes, 分辨率: 1024x600)
2025/06/06 22:31:24 [ASTIAV] 已捕获 420 帧, 当前帧大小: 10732 bytes
2025/06/06 22:31:24 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:31:25 收到信号: interrupt
2025/06/06 22:31:25 正在关闭应用程序...
2025/06/06 22:31:25 正在关闭视频通话对等端...
2025/06/06 22:31:25 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:31:25 [PEER] 离开房间...
2025/06/06 22:31:25 [PEER] 离开房间
2025/06/06 22:31:25 [MEDIA] 停止媒体捕获
2025/06/06 22:31:25 [PLAYER] 停止媒体播放
2025/06/06 22:31:25 [SIGNALING] 断开信令连接
2025/06/06 22:31:25 [SIGNALING] WebSocket读取错误: read tcp *************:60322->52.81.131.193:8089: use of closed network connection
2025/06/06 22:31:25 [PEER] 信令错误: read tcp *************:60322->52.81.131.193:8089: use of closed network connection
2025/06/06 22:31:25 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:31:25 [PEER] 已离开房间
2025/06/06 22:31:25 [PEER] 关闭媒体捕获器...
2025/06/06 22:31:25 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:31:25 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:31:25 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:31:25 [ASTIAV] 开始清理资源...
2025/06/06 22:31:25 [ASTIAV] 释放输入包...
2025/06/06 22:31:25 [ASTIAV] 释放解码帧...
2025/06/06 22:31:25 [ASTIAV] 释放缩放帧...
2025/06/06 22:31:25 [ASTIAV] 释放编码包...
2025/06/06 22:31:25 [ASTIAV] 释放缩放上下文...
2025/06/06 22:31:25 [ASTIAV] 释放编码器上下文...
2025/06/06 22:31:25 [ASTIAV] 释放解码器上下文...
2025/06/06 22:31:25 [ASTIAV] 关闭输入上下文...
2025/06/06 22:31:25 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:31:25 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:31:25 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:31:25 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:31:25 [PEER] 关闭媒体播放器...
2025/06/06 22:31:25 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:31:25 [H264] ffplay已关闭
2025/06/06 22:31:25 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:31:25 [JPEG] ffplay已关闭
2025/06/06 22:31:25 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:31:25 [PLAYER] 媒体播放器已关闭
2025/06/06 22:31:25 [PEER] 关闭WebRTC管理器...
2025/06/06 22:31:25 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:31:25 [WEBRTC] 关闭数据通道...
2025/06/06 22:31:25 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:31:25 [WEBRTC] 连接状态变化: closed
2025/06/06 22:31:25 [PEER] WebRTC连接状态: closed
2025/06/06 22:31:25 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:31:25 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:31:25 [WEBRTC] ICE连接状态变化: closed
2025/06/06 22:31:25 等待资源释放...
2025/06/06 22:31:25 [MEDIA] 视频捕获循环结束
2025/06/06 22:31:26 [WEBRTC] 数据通道状态监控: closed
2025/06/06 22:31:26 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/06 22:31:27 应用程序已安全关闭
2025/06/06 22:31:27 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:31:27 [PEER] 离开房间...
2025/06/06 22:31:27 [PEER] 离开房间
2025/06/06 22:31:27 [PEER] 已离开房间
2025/06/06 22:31:27 [PEER] 关闭媒体捕获器...
2025/06/06 22:31:27 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:31:27 [PEER] 关闭媒体播放器...
2025/06/06 22:31:27 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:31:27 [H264] ffplay已关闭
2025/06/06 22:31:27 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:31:27 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:31:27 [JPEG] ffplay已关闭
2025/06/06 22:31:27 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:31:27 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:31:27 [PLAYER] 媒体播放器已关闭
2025/06/06 22:31:27 [PEER] 关闭WebRTC管理器...
2025/06/06 22:31:27 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:31:27 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:31:27 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:38:19 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:38:19 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:38:19 配置文件: config/default.yaml
2025/06/06 22:38:19 调试模式: false
2025/06/06 22:38:19 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:38:19 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:38:19 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:38:20 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:38:20 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:38:20 [PEER] 初始化视频通话对等端
2025/06/06 22:38:20 [MEDIA] 初始化媒体捕获器
2025/06/06 22:38:20 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:38:20 [WEBRTC] 数据通道创建成功
2025/06/06 22:38:20 [WEBRTC] 初始数据通道状态: connecting
2025/06/06 22:38:20 [WEBRTC] PeerConnection创建成功
2025/06/06 22:38:20 [PEER] 视频通话对等端初始化完成
2025/06/06 22:38:20 视频通话对等端初始化完成
2025/06/06 22:38:22 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:24 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:26 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:28 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:30 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:31 [PEER] 加入房间: 12345678
2025/06/06 22:38:31 [SIGNALING] 连接到房间: 12345678
2025/06/06 22:38:31 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:38:31 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"12345678","clientid":"60447344"}
2025/06/06 22:38:31 [SIGNALING] 成功连接到房间: 12345678, ClientID: 60447344, IsInitiator: true
2025/06/06 22:38:31 [MEDIA] 开始媒体捕获
2025/06/06 22:38:31 [PEER] 信令服务器已连接
2025/06/06 22:38:31 [MEDIA] 启动视频捕获循环
2025/06/06 22:38:31 [MEDIA] 启动音频捕获循环
2025/06/06 22:38:31 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:38:31 [WEBRTC] 添加视频轨道: video
2025/06/06 22:38:31 [PLAYER] 开始媒体播放
2025/06/06 22:38:31 [PEER] 成功加入房间: 12345678
2025/06/06 22:38:31 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:38:31 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:38:31 [ASTIAV] 调试模式已启用
2025/06/06 22:38:31 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:38:31 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:38:31 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:38:31 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:38:31 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:38:31 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:38:31 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:38:31 [ASTIAV] 打开输入: 0:none
2025/06/06 22:38:32 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:33 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:38:33 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:38:33 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:38:33 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:38:33 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:38:33 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:38:33 [ASTIAV] 捕获循环已启动
2025/06/06 22:38:33 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7592 bytes, 分辨率: 1024x600)
2025/06/06 22:38:33 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:38:34 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:35 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10413 bytes
2025/06/06 22:38:35 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10404 bytes, 分辨率: 1024x600)
2025/06/06 22:38:35 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:38:36 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:38 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:38 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10557 bytes
2025/06/06 22:38:38 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10512 bytes, 分辨率: 1024x600)
2025/06/06 22:38:38 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:38:40 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:40 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10583 bytes
2025/06/06 22:38:40 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10511 bytes, 分辨率: 1024x600)
2025/06/06 22:38:40 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:38:42 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:42 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10507 bytes
2025/06/06 22:38:42 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10554 bytes, 分辨率: 1024x600)
2025/06/06 22:38:42 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:38:44 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:44 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10634 bytes
2025/06/06 22:38:44 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10497 bytes, 分辨率: 1024x600)
2025/06/06 22:38:44 [PEER] 发起呼叫
2025/06/06 22:38:44 [PEER] 提前创建WebRTC连接
2025/06/06 22:38:44 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:38:44 [WEBRTC] 连接状态变化: closed
2025/06/06 22:38:44 [PEER] WebRTC连接状态: closed
2025/06/06 22:38:44 [WEBRTC] 数据通道创建成功
2025/06/06 22:38:44 [WEBRTC] 初始数据通道状态: connecting
2025/06/06 22:38:44 [WEBRTC] PeerConnection创建成功
2025/06/06 22:38:44 [MEDIA] 数据通道发送器已设置
2025/06/06 22:38:44 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:38:44 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:38:44 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:38:44 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:38:46 [WEBRTC] 数据通道状态监控: closed
2025/06/06 22:38:46 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/06 22:38:46 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:47 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10745 bytes
2025/06/06 22:38:47 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10673 bytes, 分辨率: 1024x600)
2025/06/06 22:38:48 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}","error":""}
2025/06/06 22:38:48 [SIGNALING] 收到自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:38:48 [PEER] 处理自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:38:48 [PEER] 创建并发送Offer
2025/06/06 22:38:48 [PEER] 当前WebRTC连接状态: new
2025/06/06 22:38:48 [PEER] 状态变化: Ringing -> Negotiating
2025/06/06 22:38:48 [WEBRTC] 连接状态变化: closed
2025/06/06 22:38:48 [PEER] WebRTC连接状态: closed
2025/06/06 22:38:48 [WEBRTC] 数据通道创建成功
2025/06/06 22:38:48 [WEBRTC] 初始数据通道状态: connecting
2025/06/06 22:38:48 [WEBRTC] PeerConnection创建成功
2025/06/06 22:38:48 [MEDIA] 数据通道发送器已设置
2025/06/06 22:38:48 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:38:48 [WEBRTC] 创建并设置Offer成功
2025/06/06 22:38:48 [WEBRTC] 信令状态变化: have-local-offer
2025/06/06 22:38:48 [WEBRTC] 生成ICE候选: udp4 host **************:58917 (resolved: **************:58917)
2025/06/06 22:38:48 [WEBRTC] 生成ICE候选: udp4 host *************:61037 (resolved: *************:61037)
2025/06/06 22:38:49 [SIGNALING] 发送ICE候选
2025/06/06 22:38:49 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:38:48 [WEBRTC] 数据通道状态监控: closed
2025/06/06 22:38:49 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/06 22:38:49 [SIGNALING] 发送ICE候选
2025/06/06 22:38:49 [SIGNALING] 发送SDP offer
2025/06/06 22:38:49 [PEER] Offer已发送
2025/06/06 22:38:49 [WEBRTC] 生成ICE候选: udp4 srflx ***************:58647 related 0.0.0.0:58647 (resolved: ***************:58647)
2025/06/06 22:38:49 [WEBRTC] 生成ICE候选: udp4 srflx ***************:63495 related 0.0.0.0:63495 (resolved: ***************:63495)
2025/06/06 22:38:49 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10657 bytes
2025/06/06 22:38:49 [SIGNALING] 发送ICE候选
2025/06/06 22:38:49 [SIGNALING] 发送ICE候选
2025/06/06 22:38:49 [SIGNALING] 收到消息: {"msg":"{\"type\":\"answer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:0\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693231359 *************** 60584 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15121663 ************* 59068 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129438975 ************** 60584 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:1\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693231359 *************** 60584 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15121663 ************* 59068 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129438975 ************** 60584 typ host\\r\\n\"}","error":""}
2025/06/06 22:38:49 [PEER] 处理RTC消息: answer
2025/06/06 22:38:49 [PEER] 处理接收到的Answer
2025/06/06 22:38:49 [WEBRTC] 信令状态变化: stable
2025/06/06 22:38:49 [WEBRTC] 设置远程answer描述成功
2025/06/06 22:38:49 [PEER] Answer已处理
2025/06/06 22:38:49 [WEBRTC] ICE连接状态变化: checking
2025/06/06 22:38:49 [WEBRTC] 连接状态变化: connecting
2025/06/06 22:38:49 [PEER] WebRTC连接状态: connecting
2025/06/06 22:38:49 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10657 bytes, 分辨率: 1024x600)
2025/06/06 22:38:50 [WEBRTC] ICE连接状态变化: connected
2025/06/06 22:38:50 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:51 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10538 bytes
2025/06/06 22:38:51 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10538 bytes, 分辨率: 1024x600)
2025/06/06 22:38:52 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:54 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10580 bytes
2025/06/06 22:38:54 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10580 bytes, 分辨率: 1024x600)
2025/06/06 22:38:54 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:56 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10807 bytes
2025/06/06 22:38:56 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10807 bytes, 分辨率: 1024x600)
2025/06/06 22:38:56 [WEBRTC] ICE连接状态变化: disconnected
2025/06/06 22:38:56 [WEBRTC] 连接状态变化: disconnected
2025/06/06 22:38:56 [PEER] WebRTC连接状态: disconnected
2025/06/06 22:38:56 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:38:58 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10649 bytes
2025/06/06 22:38:58 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10705 bytes, 分辨率: 1024x600)
2025/06/06 22:38:58 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:39:00 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10644 bytes, 分辨率: 1024x600)
2025/06/06 22:39:00 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10666 bytes
2025/06/06 22:39:00 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:39:01 收到信号: interrupt
2025/06/06 22:39:01 正在关闭应用程序...
2025/06/06 22:39:01 正在关闭视频通话对等端...
2025/06/06 22:39:01 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:39:01 [PEER] 离开房间...
2025/06/06 22:39:01 [PEER] 离开房间
2025/06/06 22:39:01 [MEDIA] 停止媒体捕获
2025/06/06 22:39:01 [PLAYER] 停止媒体播放
2025/06/06 22:39:01 [SIGNALING] 断开信令连接
2025/06/06 22:39:01 [SIGNALING] WebSocket读取错误: read tcp *************:62015->52.81.131.193:8089: use of closed network connection
2025/06/06 22:39:01 [PEER] 已离开房间
2025/06/06 22:39:01 [PEER] 关闭媒体捕获器...
2025/06/06 22:39:01 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:39:01 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:39:01 [PEER] 信令错误: read tcp *************:62015->52.81.131.193:8089: use of closed network connection
2025/06/06 22:39:01 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:39:01 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:39:01 [ASTIAV] 开始清理资源...
2025/06/06 22:39:01 [ASTIAV] 释放输入包...
2025/06/06 22:39:01 [ASTIAV] 释放解码帧...
2025/06/06 22:39:01 [ASTIAV] 释放缩放帧...
2025/06/06 22:39:01 [ASTIAV] 释放编码包...
2025/06/06 22:39:01 [ASTIAV] 释放缩放上下文...
2025/06/06 22:39:01 [ASTIAV] 释放编码器上下文...
2025/06/06 22:39:01 [ASTIAV] 释放解码器上下文...
2025/06/06 22:39:01 [ASTIAV] 关闭输入上下文...
2025/06/06 22:39:01 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:39:02 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:39:02 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:39:02 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:39:02 [PEER] 关闭媒体播放器...
2025/06/06 22:39:02 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:39:02 [H264] ffplay已关闭
2025/06/06 22:39:02 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:39:02 [JPEG] ffplay已关闭
2025/06/06 22:39:02 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:39:02 [PLAYER] 媒体播放器已关闭
2025/06/06 22:39:02 [PEER] 关闭WebRTC管理器...
2025/06/06 22:39:02 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:39:02 [WEBRTC] 关闭数据通道...
2025/06/06 22:39:02 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:39:02 [WEBRTC] 连接状态变化: closed
2025/06/06 22:39:02 [PEER] WebRTC连接状态: closed
2025/06/06 22:39:02 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:39:02 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:39:02 等待资源释放...
2025/06/06 22:39:02 [WEBRTC] ICE连接状态变化: closed
2025/06/06 22:39:02 [WEBRTC] 数据通道状态监控: closed
2025/06/06 22:39:02 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/06 22:39:04 应用程序已安全关闭
2025/06/06 22:39:04 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:39:04 [PEER] 离开房间...
2025/06/06 22:39:04 [PEER] 离开房间
2025/06/06 22:39:04 [PEER] 已离开房间
2025/06/06 22:39:04 [PEER] 关闭媒体捕获器...
2025/06/06 22:39:04 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:39:04 [PEER] 关闭媒体播放器...
2025/06/06 22:39:04 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:39:04 [H264] ffplay已关闭
2025/06/06 22:39:04 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:39:04 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:39:04 [JPEG] ffplay已关闭
2025/06/06 22:39:04 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:39:04 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:39:04 [PLAYER] 媒体播放器已关闭
2025/06/06 22:39:04 [PEER] 关闭WebRTC管理器...
2025/06/06 22:39:04 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:39:04 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:39:04 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:41:50 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:41:50 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:41:50 配置文件: config/default.yaml
2025/06/06 22:41:50 调试模式: false
2025/06/06 22:41:50 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:41:50 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:41:50 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:41:50 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:41:50 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:41:50 [PEER] 初始化视频通话对等端
2025/06/06 22:41:50 [MEDIA] 初始化媒体捕获器
2025/06/06 22:41:50 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:41:50 [WEBRTC] 数据通道创建成功
2025/06/06 22:41:50 [WEBRTC] 初始数据通道状态: connecting
2025/06/06 22:41:50 [WEBRTC] PeerConnection创建成功
2025/06/06 22:41:50 [PEER] 视频通话对等端初始化完成
2025/06/06 22:41:50 视频通话对等端初始化完成
2025/06/06 22:41:52 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:41:54 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:41:56 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:41:58 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:41:59 [PEER] 加入房间: 333444555
2025/06/06 22:41:59 [SIGNALING] 连接到房间: 333444555
2025/06/06 22:41:59 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:41:59 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"333444555","clientid":"15774538"}
2025/06/06 22:41:59 [SIGNALING] 成功连接到房间: 333444555, ClientID: 15774538, IsInitiator: true
2025/06/06 22:41:59 [MEDIA] 开始媒体捕获
2025/06/06 22:41:59 [PEER] 信令服务器已连接
2025/06/06 22:41:59 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:41:59 [MEDIA] 启动视频捕获循环
2025/06/06 22:41:59 [WEBRTC] 添加视频轨道: video
2025/06/06 22:41:59 [PLAYER] 开始媒体播放
2025/06/06 22:41:59 [MEDIA] 启动音频捕获循环
2025/06/06 22:41:59 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:41:59 [PEER] 成功加入房间: 333444555
2025/06/06 22:41:59 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:41:59 [ASTIAV] 调试模式已启用
2025/06/06 22:41:59 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:41:59 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:41:59 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:41:59 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:41:59 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:41:59 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:41:59 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:41:59 [ASTIAV] 打开输入: 0:none
2025/06/06 22:42:00 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:00 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:42:00 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:42:00 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:42:00 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:42:00 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:42:00 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:42:00 [ASTIAV] 捕获循环已启动
2025/06/06 22:42:01 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7657 bytes, 分辨率: 1024x600)
2025/06/06 22:42:01 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:42:02 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:03 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10510 bytes
2025/06/06 22:42:03 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10510 bytes, 分辨率: 1024x600)
2025/06/06 22:42:03 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:42:04 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:05 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10488 bytes
2025/06/06 22:42:05 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10488 bytes, 分辨率: 1024x600)
2025/06/06 22:42:05 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:42:06 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:07 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10440 bytes
2025/06/06 22:42:07 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10440 bytes, 分辨率: 1024x600)
2025/06/06 22:42:07 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:42:08 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:09 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10855 bytes
2025/06/06 22:42:09 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10855 bytes, 分辨率: 1024x600)
2025/06/06 22:42:09 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:42:10 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:11 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:42:12 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10862 bytes
2025/06/06 22:42:12 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10681 bytes, 分辨率: 1024x600)
2025/06/06 22:42:12 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:14 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10705 bytes, 分辨率: 1024x600)
2025/06/06 22:42:14 [PEER] 发起呼叫
2025/06/06 22:42:14 [PEER] 提前创建WebRTC连接
2025/06/06 22:42:14 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:42:14 [WEBRTC] 连接状态变化: closed
2025/06/06 22:42:14 [PEER] WebRTC连接状态: closed
2025/06/06 22:42:14 [WEBRTC] 数据通道创建成功
2025/06/06 22:42:14 [WEBRTC] 初始数据通道状态: connecting
2025/06/06 22:42:14 [WEBRTC] PeerConnection创建成功
2025/06/06 22:42:14 [MEDIA] 数据通道发送器已设置
2025/06/06 22:42:14 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:42:14 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:42:14 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:42:14 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:42:14 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10684 bytes
2025/06/06 22:42:14 [WEBRTC] 数据通道状态监控: closed
2025/06/06 22:42:14 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/06 22:42:16 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:16 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10365 bytes, 分辨率: 1024x600)
2025/06/06 22:42:16 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10496 bytes
2025/06/06 22:42:18 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}","error":""}
2025/06/06 22:42:18 [SIGNALING] 收到自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:42:18 [PEER] 处理自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:42:18 [PEER] 创建并发送Offer
2025/06/06 22:42:18 [PEER] 当前WebRTC连接状态: new
2025/06/06 22:42:18 [PEER] ⚠️ 警告：连接状态为new，这不应该发生！
2025/06/06 22:42:18 [PEER] 状态变化: Ringing -> Negotiating
2025/06/06 22:42:18 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:18 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10532 bytes, 分辨率: 1024x600)
2025/06/06 22:42:18 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10663 bytes
2025/06/06 22:42:18 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:42:20 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:21 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10627 bytes, 分辨率: 1024x600)
2025/06/06 22:42:21 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10694 bytes
2025/06/06 22:42:22 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:23 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10357 bytes, 分辨率: 1024x600)
2025/06/06 22:42:23 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10477 bytes
2025/06/06 22:42:24 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:25 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10629 bytes, 分辨率: 1024x600)
2025/06/06 22:42:25 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10585 bytes
2025/06/06 22:42:26 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:27 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10553 bytes, 分辨率: 1024x600)
2025/06/06 22:42:28 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10627 bytes
2025/06/06 22:42:28 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:30 [MEDIA] AstiAV成功捕获摄像头帧: 390 (大小: 10507 bytes, 分辨率: 1024x600)
2025/06/06 22:42:30 [ASTIAV] 已捕获 390 帧, 当前帧大小: 10534 bytes
2025/06/06 22:42:30 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:32 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:42:32 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:32 [MEDIA] AstiAV成功捕获摄像头帧: 420 (大小: 10527 bytes, 分辨率: 1024x600)
2025/06/06 22:42:32 [ASTIAV] 已捕获 420 帧, 当前帧大小: 10524 bytes
2025/06/06 22:42:34 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:34 [MEDIA] AstiAV成功捕获摄像头帧: 450 (大小: 10537 bytes, 分辨率: 1024x600)
2025/06/06 22:42:34 [ASTIAV] 已捕获 450 帧, 当前帧大小: 10525 bytes
2025/06/06 22:42:36 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:36 [MEDIA] AstiAV成功捕获摄像头帧: 480 (大小: 10674 bytes, 分辨率: 1024x600)
2025/06/06 22:42:37 [ASTIAV] 已捕获 480 帧, 当前帧大小: 10636 bytes
2025/06/06 22:42:38 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:38 [MEDIA] AstiAV成功捕获摄像头帧: 510 (大小: 10745 bytes, 分辨率: 1024x600)
2025/06/06 22:42:39 [ASTIAV] 已捕获 510 帧, 当前帧大小: 10826 bytes
2025/06/06 22:42:40 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:42:40 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:41 [MEDIA] AstiAV成功捕获摄像头帧: 540 (大小: 10826 bytes, 分辨率: 1024x600)
2025/06/06 22:42:41 [ASTIAV] 已捕获 540 帧, 当前帧大小: 10914 bytes
2025/06/06 22:42:42 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:43 [MEDIA] AstiAV成功捕获摄像头帧: 570 (大小: 10816 bytes, 分辨率: 1024x600)
2025/06/06 22:42:43 [ASTIAV] 已捕获 570 帧, 当前帧大小: 10761 bytes
2025/06/06 22:42:44 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:45 [MEDIA] AstiAV成功捕获摄像头帧: 600 (大小: 10855 bytes, 分辨率: 1024x600)
2025/06/06 22:42:45 [ASTIAV] 已捕获 600 帧, 当前帧大小: 10871 bytes
2025/06/06 22:42:46 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:47 [MEDIA] AstiAV成功捕获摄像头帧: 630 (大小: 10816 bytes, 分辨率: 1024x600)
2025/06/06 22:42:48 [ASTIAV] 已捕获 630 帧, 当前帧大小: 10834 bytes
2025/06/06 22:42:48 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:49 [MEDIA] AstiAV成功捕获摄像头帧: 660 (大小: 10805 bytes, 分辨率: 1024x600)
2025/06/06 22:42:50 [ASTIAV] 已捕获 660 帧, 当前帧大小: 10692 bytes
2025/06/06 22:42:50 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:52 [MEDIA] AstiAV成功捕获摄像头帧: 690 (大小: 10813 bytes, 分辨率: 1024x600)
2025/06/06 22:42:52 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:52 [ASTIAV] 已捕获 690 帧, 当前帧大小: 10828 bytes
2025/06/06 22:42:54 [MEDIA] AstiAV成功捕获摄像头帧: 720 (大小: 10617 bytes, 分辨率: 1024x600)
2025/06/06 22:42:54 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:42:54 收到信号: interrupt
2025/06/06 22:42:54 正在关闭应用程序...
2025/06/06 22:42:54 正在关闭视频通话对等端...
2025/06/06 22:42:54 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:42:54 [PEER] 离开房间...
2025/06/06 22:42:54 [PEER] 离开房间
2025/06/06 22:42:54 [MEDIA] 停止媒体捕获
2025/06/06 22:42:54 [PLAYER] 停止媒体播放
2025/06/06 22:42:54 [SIGNALING] 断开信令连接
2025/06/06 22:42:54 [SIGNALING] WebSocket读取错误: read tcp *************:62784->52.81.131.193:8089: use of closed network connection
2025/06/06 22:42:54 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:42:54 [PEER] 已离开房间
2025/06/06 22:42:54 [PEER] 关闭媒体捕获器...
2025/06/06 22:42:54 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:42:54 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:42:54 [PEER] 信令错误: read tcp *************:62784->52.81.131.193:8089: use of closed network connection
2025/06/06 22:42:54 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:42:54 [ASTIAV] 开始清理资源...
2025/06/06 22:42:54 [ASTIAV] 释放输入包...
2025/06/06 22:42:54 [ASTIAV] 释放解码帧...
2025/06/06 22:42:54 [ASTIAV] 释放缩放帧...
2025/06/06 22:42:54 [ASTIAV] 释放编码包...
2025/06/06 22:42:54 [ASTIAV] 释放缩放上下文...
2025/06/06 22:42:54 [ASTIAV] 释放编码器上下文...
2025/06/06 22:42:54 [ASTIAV] 释放解码器上下文...
2025/06/06 22:42:54 [ASTIAV] 关闭输入上下文...
2025/06/06 22:42:54 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:42:54 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:42:54 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:42:54 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:42:54 [PEER] 关闭媒体播放器...
2025/06/06 22:42:54 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:42:54 [H264] ffplay已关闭
2025/06/06 22:42:54 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:42:54 [JPEG] ffplay已关闭
2025/06/06 22:42:54 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:42:54 [PLAYER] 媒体播放器已关闭
2025/06/06 22:42:54 [PEER] 关闭WebRTC管理器...
2025/06/06 22:42:54 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:42:54 [WEBRTC] 关闭数据通道...
2025/06/06 22:42:54 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:42:54 [WEBRTC] 连接状态变化: closed
2025/06/06 22:42:54 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:42:54 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:42:54 等待资源释放...
2025/06/06 22:42:54 [MEDIA] 视频捕获循环结束
2025/06/06 22:42:56 [WEBRTC] 数据通道状态监控: closed
2025/06/06 22:42:56 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/06 22:42:56 应用程序已安全关闭
2025/06/06 22:42:56 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:42:56 [PEER] 离开房间...
2025/06/06 22:42:56 [PEER] 离开房间
2025/06/06 22:42:56 [PEER] 已离开房间
2025/06/06 22:42:56 [PEER] 关闭媒体捕获器...
2025/06/06 22:42:56 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:42:56 [PEER] 关闭媒体播放器...
2025/06/06 22:42:56 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:42:56 [H264] ffplay已关闭
2025/06/06 22:42:56 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:42:56 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:42:56 [JPEG] ffplay已关闭
2025/06/06 22:42:56 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:42:56 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:42:56 [PLAYER] 媒体播放器已关闭
2025/06/06 22:42:56 [PEER] 关闭WebRTC管理器...
2025/06/06 22:42:56 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:42:56 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:42:56 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:46:10 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/06 22:46:10 启动 Go VideoCall Peer v1.0.0
2025/06/06 22:46:10 配置文件: config/default.yaml
2025/06/06 22:46:10 调试模式: false
2025/06/06 22:46:10 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 22:46:10 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 22:46:10 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 22:46:11 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 22:46:11 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 22:46:11 [PEER] 初始化视频通话对等端
2025/06/06 22:46:11 [MEDIA] 初始化媒体捕获器
2025/06/06 22:46:11 [MEDIA] 媒体捕获器初始化完成
2025/06/06 22:46:11 [WEBRTC] 数据通道创建成功
2025/06/06 22:46:11 [WEBRTC] 初始数据通道状态: connecting
2025/06/06 22:46:11 [WEBRTC] PeerConnection创建成功
2025/06/06 22:46:11 [PEER] 视频通话对等端初始化完成
2025/06/06 22:46:11 视频通话对等端初始化完成
2025/06/06 22:46:13 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:15 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:17 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:19 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:21 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:21 [PEER] 加入房间: 787878
2025/06/06 22:46:21 [SIGNALING] 连接到房间: 787878
2025/06/06 22:46:21 [PEER] 状态变化: Idle -> Connecting
2025/06/06 22:46:21 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"787878","clientid":"04353300"}
2025/06/06 22:46:21 [SIGNALING] 成功连接到房间: 787878, ClientID: 04353300, IsInitiator: true
2025/06/06 22:46:21 [MEDIA] 开始媒体捕获
2025/06/06 22:46:21 [PEER] 信令服务器已连接
2025/06/06 22:46:21 [MEDIA] 启动视频捕获循环
2025/06/06 22:46:21 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:46:21 [MEDIA] 启动音频捕获循环
2025/06/06 22:46:21 [WEBRTC] 添加视频轨道: video
2025/06/06 22:46:21 [PLAYER] 开始媒体播放
2025/06/06 22:46:21 [PEER] 成功加入房间: 787878
2025/06/06 22:46:21 [PEER] 状态变化: Connecting -> Connected
2025/06/06 22:46:21 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/06 22:46:21 [ASTIAV] 调试模式已启用
2025/06/06 22:46:21 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/06 22:46:21 [ASTIAV] macOS AVFoundation 设备:
2025/06/06 22:46:21 [ASTIAV]   0 - 默认摄像头
2025/06/06 22:46:21 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/06 22:46:21 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/06 22:46:21 [MEDIA] AstiAV摄像头已初始化
2025/06/06 22:46:21 [ASTIAV] 启动摄像头捕获...
2025/06/06 22:46:21 [ASTIAV] 打开输入: 0:none
2025/06/06 22:46:23 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/06 22:46:23 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/06 22:46:23 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/06 22:46:23 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/06 22:46:23 [ASTIAV] ✅ 缩放器已初始化
2025/06/06 22:46:23 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/06 22:46:23 [ASTIAV] 捕获循环已启动
2025/06/06 22:46:23 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 7582 bytes, 分辨率: 1024x600)
2025/06/06 22:46:23 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/06 22:46:23 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:25 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:25 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10714 bytes
2025/06/06 22:46:25 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10514 bytes, 分辨率: 1024x600)
2025/06/06 22:46:25 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/06 22:46:27 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:27 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10660 bytes
2025/06/06 22:46:27 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10619 bytes, 分辨率: 1024x600)
2025/06/06 22:46:27 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/06 22:46:29 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:30 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10755 bytes
2025/06/06 22:46:30 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10675 bytes, 分辨率: 1024x600)
2025/06/06 22:46:30 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/06 22:46:31 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:32 [PEER] 发起呼叫
2025/06/06 22:46:32 [PEER] 提前创建WebRTC连接
2025/06/06 22:46:32 [WEBRTC] 连接已存在，当前状态: new
2025/06/06 22:46:32 [WEBRTC] 连接状态正常，跳过重复创建
2025/06/06 22:46:32 [MEDIA] 数据通道发送器已设置
2025/06/06 22:46:32 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/06 22:46:32 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:46:32 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/06 22:46:32 [PEER] 呼叫已发起，等待对方响应
2025/06/06 22:46:32 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/06 22:46:32 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10750 bytes
2025/06/06 22:46:32 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10927 bytes, 分辨率: 1024x600)
2025/06/06 22:46:32 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/06 22:46:32 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:46:33 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:34 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10795 bytes
2025/06/06 22:46:34 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10795 bytes, 分辨率: 1024x600)
2025/06/06 22:46:35 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}","error":""}
2025/06/06 22:46:35 [SIGNALING] 收到自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:46:35 [PEER] 处理自定义命令: ACCEPT_CALL, 数据: 
2025/06/06 22:46:35 [PEER] 创建并发送Offer
2025/06/06 22:46:35 [PEER] 当前WebRTC连接状态: new
2025/06/06 22:46:35 [PEER] 连接状态为new，需要确保连接已创建
2025/06/06 22:46:35 [WEBRTC] 连接已存在，当前状态: new
2025/06/06 22:46:35 [PEER] 状态变化: Ringing -> Negotiating
2025/06/06 22:46:35 [WEBRTC] 连接状态正常，跳过重复创建
2025/06/06 22:46:35 [MEDIA] 数据通道发送器已设置
2025/06/06 22:46:35 [WEBRTC] 添加音频轨道: audio
2025/06/06 22:46:35 [WEBRTC] 创建并设置Offer成功
2025/06/06 22:46:35 [WEBRTC] 信令状态变化: have-local-offer
2025/06/06 22:46:35 [WEBRTC] 生成ICE候选: udp4 host **************:54602 (resolved: **************:54602)
2025/06/06 22:46:35 [WEBRTC] 生成ICE候选: udp4 host *************:58655 (resolved: *************:58655)
2025/06/06 22:46:35 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:35 [WEBRTC] 生成ICE候选: udp4 srflx ***************:61397 related 0.0.0.0:61397 (resolved: ***************:61397)
2025/06/06 22:46:35 [WEBRTC] 生成ICE候选: udp4 srflx ***************:57594 related 0.0.0.0:57594 (resolved: ***************:57594)
2025/06/06 22:46:37 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10811 bytes
2025/06/06 22:46:37 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10811 bytes, 分辨率: 1024x600)
2025/06/06 22:46:37 [SIGNALING] 发送ICE候选
2025/06/06 22:46:37 [SIGNALING] 发送ICE候选
2025/06/06 22:46:37 [SIGNALING] 发送ICE候选
2025/06/06 22:46:37 [SIGNALING] 发送SDP offer
2025/06/06 22:46:37 [SIGNALING] 发送ICE候选
2025/06/06 22:46:37 [PEER] Offer已发送
2025/06/06 22:46:37 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:37 [SIGNALING] 收到消息: {"msg":"{\"type\":\"answer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1 2\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:0\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693231615 *************** 60585 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15127039 ************* 59089 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129439231 ************** 60585 typ host\\r\\nm=video 9 UDP/TLS/RTP/SAVPF 102 106\\r\\na=rtpmap:102 H264/90000\\r\\na=rtcp-fb:102 goog-remb\\r\\na=rtcp-fb:102 transport-cc\\r\\na=rtcp-fb:102 ccm fir\\r\\na=rtcp-fb:102 nack\\r\\na=rtcp-fb:102 nack pli\\r\\na=fmtp:102 level-asymmetry-allowed=1\\r\\na=rtpmap:106 H264/90000\\r\\na=rtcp-fb:106 goog-remb\\r\\na=rtcp-fb:106 transport-cc\\r\\na=rtcp-fb:106 ccm fir\\r\\na=rtcp-fb:106 nack\\r\\na=rtcp-fb:106 nack pli\\r\\na=fmtp:106 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\\r\\na=ssrc:1 cname:webrtc-h264\\r\\na=sendrecv\\r\\na=mid:1\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp-mux\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693231615 *************** 60585 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15127039 ************* 59089 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129439231 ************** 60585 typ host\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693231615 *************** 60585 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15127039 ************* 59089 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129439231 ************** 60585 typ host\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:3\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 1F:BA:5D:E0:ED:FC:9C:4C:7D:6B:04:D1:93:08:69:72:5E:3B:EE:46:A6:2B:63:6C:4D:44:6E:82:50:1E:B1:42\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1693231615 *************** 60585 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15127039 ************* 59089 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2129439231 ************** 60585 typ host\\r\\n\"}","error":""}
2025/06/06 22:46:37 [PEER] 处理RTC消息: answer
2025/06/06 22:46:37 [PEER] 处理接收到的Answer
2025/06/06 22:46:37 [WEBRTC] 信令状态变化: stable
2025/06/06 22:46:37 [WEBRTC] 设置远程answer描述成功
2025/06/06 22:46:37 [PEER] Answer已处理
2025/06/06 22:46:37 [WEBRTC] ICE连接状态变化: checking
2025/06/06 22:46:37 [WEBRTC] 连接状态变化: connecting
2025/06/06 22:46:37 [PEER] WebRTC连接状态: connecting
2025/06/06 22:46:38 [WEBRTC] ICE连接状态变化: connected
2025/06/06 22:46:39 [ASTIAV] 已捕获 210 帧, 当前帧大小: 11020 bytes
2025/06/06 22:46:39 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 11020 bytes, 分辨率: 1024x600)
2025/06/06 22:46:39 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:41 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:41 [ASTIAV] 已捕获 240 帧, 当前帧大小: 11029 bytes
2025/06/06 22:46:41 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 11029 bytes, 分辨率: 1024x600)
2025/06/06 22:46:43 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:43 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10969 bytes
2025/06/06 22:46:43 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10969 bytes, 分辨率: 1024x600)
2025/06/06 22:46:45 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/06 22:46:45 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:46 [ASTIAV] 已捕获 300 帧, 当前帧大小: 11061 bytes
2025/06/06 22:46:46 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 11111 bytes, 分辨率: 1024x600)
2025/06/06 22:46:46 [WEBRTC] ICE连接状态变化: disconnected
2025/06/06 22:46:46 [WEBRTC] 连接状态变化: disconnected
2025/06/06 22:46:46 [PEER] WebRTC连接状态: disconnected
2025/06/06 22:46:47 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:48 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10918 bytes, 分辨率: 1024x600)
2025/06/06 22:46:48 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10863 bytes
2025/06/06 22:46:49 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:50 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10792 bytes, 分辨率: 1024x600)
2025/06/06 22:46:50 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10944 bytes
2025/06/06 22:46:51 [WEBRTC] 数据通道状态监控: connecting
2025/06/06 22:46:51 收到信号: interrupt
2025/06/06 22:46:51 正在关闭应用程序...
2025/06/06 22:46:51 正在关闭视频通话对等端...
2025/06/06 22:46:51 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:46:51 [PEER] 离开房间...
2025/06/06 22:46:51 [PEER] 离开房间
2025/06/06 22:46:51 [MEDIA] 停止媒体捕获
2025/06/06 22:46:51 [PLAYER] 停止媒体播放
2025/06/06 22:46:51 [SIGNALING] 断开信令连接
2025/06/06 22:46:51 [SIGNALING] WebSocket读取错误: read tcp *************:63783->52.81.131.193:8089: use of closed network connection
2025/06/06 22:46:51 [PEER] 已离开房间
2025/06/06 22:46:51 [PEER] 关闭媒体捕获器...
2025/06/06 22:46:51 [ASTIAV] 停止摄像头捕获...
2025/06/06 22:46:51 [PEER] 信令服务器断开连接: <nil>
2025/06/06 22:46:51 [PEER] 信令错误: read tcp *************:63783->52.81.131.193:8089: use of closed network connection
2025/06/06 22:46:51 [ASTIAV] 等待捕获循环退出...
2025/06/06 22:46:51 [ASTIAV] 开始清理AstiAV资源...
2025/06/06 22:46:51 [ASTIAV] 开始清理资源...
2025/06/06 22:46:51 [ASTIAV] 释放输入包...
2025/06/06 22:46:51 [ASTIAV] 释放解码帧...
2025/06/06 22:46:51 [ASTIAV] 释放缩放帧...
2025/06/06 22:46:51 [ASTIAV] 释放编码包...
2025/06/06 22:46:51 [ASTIAV] 释放缩放上下文...
2025/06/06 22:46:51 [ASTIAV] 释放编码器上下文...
2025/06/06 22:46:51 [ASTIAV] 释放解码器上下文...
2025/06/06 22:46:51 [ASTIAV] 关闭输入上下文...
2025/06/06 22:46:51 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/06 22:46:51 [ASTIAV] ✅ 资源清理完成
2025/06/06 22:46:51 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/06 22:46:51 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:46:51 [PEER] 关闭媒体播放器...
2025/06/06 22:46:51 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:46:51 [H264] ffplay已关闭
2025/06/06 22:46:51 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:46:51 [JPEG] ffplay已关闭
2025/06/06 22:46:51 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 22:46:51 [PLAYER] 媒体播放器已关闭
2025/06/06 22:46:51 [PEER] 关闭WebRTC管理器...
2025/06/06 22:46:51 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:46:51 [WEBRTC] 关闭数据通道...
2025/06/06 22:46:51 [WEBRTC] 关闭PeerConnection...
2025/06/06 22:46:51 [WEBRTC] 连接状态变化: closed
2025/06/06 22:46:51 [PEER] WebRTC连接状态: closed
2025/06/06 22:46:51 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:46:51 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 22:46:51 等待资源释放...
2025/06/06 22:46:51 [WEBRTC] ICE连接状态变化: closed
2025/06/06 22:46:53 [WEBRTC] 数据通道状态监控: closed
2025/06/06 22:46:53 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/06 22:46:53 应用程序已安全关闭
2025/06/06 22:46:53 [PEER] 开始关闭视频通话对等端...
2025/06/06 22:46:53 [PEER] 离开房间...
2025/06/06 22:46:53 [PEER] 离开房间
2025/06/06 22:46:53 [PEER] 已离开房间
2025/06/06 22:46:53 [PEER] 关闭媒体捕获器...
2025/06/06 22:46:53 [MEDIA] 媒体捕获器已关闭
2025/06/06 22:46:53 [PEER] 关闭媒体播放器...
2025/06/06 22:46:53 [H264] 视频文件已保存: 0 bytes
2025/06/06 22:46:53 [H264] ffplay已关闭
2025/06/06 22:46:53 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 22:46:53 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 22:46:53 [JPEG] ffplay已关闭
2025/06/06 22:46:53 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 22:46:53 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 22:46:53 [PLAYER] 媒体播放器已关闭
2025/06/06 22:46:53 [PEER] 关闭WebRTC管理器...
2025/06/06 22:46:53 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 22:46:53 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 22:46:53 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/07 12:16:26 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/07 12:16:26 启动 Go VideoCall Peer v1.0.0
2025/06/07 12:16:26 配置文件: config/default.yaml
2025/06/07 12:16:26 调试模式: false
2025/06/07 12:16:26 [H264] 视频将保存到: debug/received_video.h264
2025/06/07 12:16:26 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/07 12:16:26 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/07 12:16:26 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/07 12:16:26 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/07 12:16:26 [PEER] 初始化视频通话对等端
2025/06/07 12:16:26 [MEDIA] 初始化媒体捕获器
2025/06/07 12:16:26 [MEDIA] 媒体捕获器初始化完成
2025/06/07 12:16:26 [WEBRTC] 数据通道创建成功
2025/06/07 12:16:26 [WEBRTC] 初始数据通道状态: connecting
2025/06/07 12:16:26 [WEBRTC] PeerConnection创建成功
2025/06/07 12:16:26 [PEER] 视频通话对等端初始化完成
2025/06/07 12:16:26 视频通话对等端初始化完成
2025/06/07 12:16:28 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:30 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:32 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:34 [PEER] 加入房间: abc000
2025/06/07 12:16:34 [SIGNALING] 连接到房间: abc000
2025/06/07 12:16:34 [PEER] 状态变化: Idle -> Connecting
2025/06/07 12:16:34 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"abc000","clientid":"24300876"}
2025/06/07 12:16:34 [SIGNALING] 成功连接到房间: abc000, ClientID: 24300876, IsInitiator: true
2025/06/07 12:16:34 [MEDIA] 开始媒体捕获
2025/06/07 12:16:34 [PEER] 信令服务器已连接
2025/06/07 12:16:34 [WEBRTC] 添加音频轨道: audio
2025/06/07 12:16:34 [MEDIA] 启动音频捕获循环
2025/06/07 12:16:34 [WEBRTC] 添加视频轨道: video
2025/06/07 12:16:34 [PLAYER] 开始媒体播放
2025/06/07 12:16:34 [PEER] 成功加入房间: abc000
2025/06/07 12:16:34 [MEDIA] 启动视频捕获循环
2025/06/07 12:16:34 [PEER] 状态变化: Connecting -> Connected
2025/06/07 12:16:34 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/07 12:16:34 [ASTIAV] 调试模式已启用
2025/06/07 12:16:34 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/07 12:16:34 [ASTIAV] macOS AVFoundation 设备:
2025/06/07 12:16:34 [ASTIAV]   0 - 默认摄像头
2025/06/07 12:16:34 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/07 12:16:34 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/07 12:16:34 [MEDIA] AstiAV摄像头已初始化
2025/06/07 12:16:34 [ASTIAV] 启动摄像头捕获...
2025/06/07 12:16:34 [ASTIAV] 打开输入: 0:none
2025/06/07 12:16:34 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:36 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/07 12:16:36 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/07 12:16:36 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/07 12:16:36 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/07 12:16:36 [ASTIAV] ✅ 缩放器已初始化
2025/06/07 12:16:36 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/07 12:16:36 [ASTIAV] 捕获循环已启动
2025/06/07 12:16:36 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 14856 bytes, 分辨率: 1024x600)
2025/06/07 12:16:36 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/07 12:16:36 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:38 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:39 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10441 bytes
2025/06/07 12:16:39 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10531 bytes, 分辨率: 1024x600)
2025/06/07 12:16:39 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/07 12:16:40 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:41 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10888 bytes
2025/06/07 12:16:41 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10893 bytes, 分辨率: 1024x600)
2025/06/07 12:16:41 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/07 12:16:42 [SIGNALING] 收到消息: {"msg":"{\"error\":\"\"}","error":""}
2025/06/07 12:16:42 [SIGNALING] 未知RTC消息类型: 
2025/06/07 12:16:42 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:43 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10637 bytes
2025/06/07 12:16:43 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10668 bytes, 分辨率: 1024x600)
2025/06/07 12:16:43 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/07 12:16:44 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:45 [PEER] 发起呼叫
2025/06/07 12:16:45 [PEER] 提前创建WebRTC连接（不包含数据通道）
2025/06/07 12:16:45 [WEBRTC] 连接已存在，当前状态: new
2025/06/07 12:16:45 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/07 12:16:45 [WEBRTC] 连接状态正常，跳过重复创建
2025/06/07 12:16:45 [WEBRTC] 添加音频轨道: audio
2025/06/07 12:16:45 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/07 12:16:45 [PEER] 呼叫已发起，等待对方响应
2025/06/07 12:16:45 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/07 12:16:45 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10641 bytes
2025/06/07 12:16:45 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10690 bytes, 分辨率: 1024x600)
2025/06/07 12:16:45 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/07 12:16:46 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:47 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10423 bytes
2025/06/07 12:16:48 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10441 bytes, 分辨率: 1024x600)
2025/06/07 12:16:48 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:49 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}","error":""}
2025/06/07 12:16:49 [SIGNALING] 收到自定义命令: ACCEPT_CALL, 数据: 
2025/06/07 12:16:49 [PEER] 处理自定义命令: ACCEPT_CALL, 数据: 
2025/06/07 12:16:49 [PEER] 创建并发送Offer
2025/06/07 12:16:49 [PEER] 当前WebRTC连接状态: new
2025/06/07 12:16:49 [PEER] 状态变化: Ringing -> Negotiating
2025/06/07 12:16:49 [PEER] 连接状态为new，需要确保连接已创建
2025/06/07 12:16:49 [WEBRTC] 连接已存在，当前状态: new
2025/06/07 12:16:49 [WEBRTC] 连接状态正常，跳过重复创建
2025/06/07 12:16:49 [WEBRTC] 添加音频轨道: audio
2025/06/07 12:16:49 [PEER] 作为发起方，创建数据通道
2025/06/07 12:16:49 [WEBRTC] 数据通道已存在，状态: connecting
2025/06/07 12:16:49 [MEDIA] 数据通道发送器已设置
2025/06/07 12:16:49 [WEBRTC] 创建并设置Offer成功
2025/06/07 12:16:49 [WEBRTC] 信令状态变化: have-local-offer
2025/06/07 12:16:49 [WEBRTC] 生成ICE候选: udp4 host **************:56823 (resolved: **************:56823)
2025/06/07 12:16:49 [WEBRTC] 生成ICE候选: udp4 host *************:61101 (resolved: *************:61101)
2025/06/07 12:16:49 [SIGNALING] 发送ICE候选
2025/06/07 12:16:49 [SIGNALING] 发送ICE候选
2025/06/07 12:16:49 [SIGNALING] 发送SDP offer
2025/06/07 12:16:49 [PEER] Offer已发送
2025/06/07 12:16:49 [WEBRTC] 生成ICE候选: udp4 srflx **************:52282 related 0.0.0.0:52282 (resolved: **************:52282)
2025/06/07 12:16:49 [WEBRTC] 生成ICE候选: udp4 srflx **************:60512 related 0.0.0.0:60512 (resolved: **************:60512)
2025/06/07 12:16:49 [SIGNALING] 发送ICE候选
2025/06/07 12:16:49 [SIGNALING] 发送ICE候选
2025/06/07 12:16:50 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10637 bytes
2025/06/07 12:16:50 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10635 bytes, 分辨率: 1024x600)
2025/06/07 12:16:50 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:52 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10808 bytes
2025/06/07 12:16:52 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10821 bytes, 分辨率: 1024x600)
2025/06/07 12:16:52 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:54 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10837 bytes
2025/06/07 12:16:54 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10839 bytes, 分辨率: 1024x600)
2025/06/07 12:16:54 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:56 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10768 bytes
2025/06/07 12:16:56 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10792 bytes, 分辨率: 1024x600)
2025/06/07 12:16:56 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:58 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:16:59 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10753 bytes
2025/06/07 12:16:59 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10770 bytes, 分辨率: 1024x600)
2025/06/07 12:16:59 收到信号: interrupt
2025/06/07 12:16:59 正在关闭应用程序...
2025/06/07 12:16:59 正在关闭视频通话对等端...
2025/06/07 12:16:59 [PEER] 开始关闭视频通话对等端...
2025/06/07 12:16:59 [PEER] 离开房间...
2025/06/07 12:16:59 [PEER] 离开房间
2025/06/07 12:16:59 [MEDIA] 停止媒体捕获
2025/06/07 12:16:59 [PLAYER] 停止媒体播放
2025/06/07 12:16:59 [SIGNALING] 断开信令连接
2025/06/07 12:16:59 [SIGNALING] WebSocket读取错误: read tcp *************:56366->52.81.131.193:8089: use of closed network connection
2025/06/07 12:16:59 [PEER] 信令错误: read tcp *************:56366->52.81.131.193:8089: use of closed network connection
2025/06/07 12:16:59 [PEER] 信令服务器断开连接: <nil>
2025/06/07 12:16:59 [PEER] 已离开房间
2025/06/07 12:16:59 [PEER] 关闭媒体捕获器...
2025/06/07 12:16:59 [ASTIAV] 停止摄像头捕获...
2025/06/07 12:16:59 [ASTIAV] 等待捕获循环退出...
2025/06/07 12:16:59 [ASTIAV] 开始清理AstiAV资源...
2025/06/07 12:16:59 [ASTIAV] 开始清理资源...
2025/06/07 12:16:59 [ASTIAV] 释放输入包...
2025/06/07 12:16:59 [ASTIAV] 释放解码帧...
2025/06/07 12:16:59 [ASTIAV] 释放缩放帧...
2025/06/07 12:16:59 [ASTIAV] 释放编码包...
2025/06/07 12:16:59 [ASTIAV] 释放缩放上下文...
2025/06/07 12:16:59 [ASTIAV] 释放编码器上下文...
2025/06/07 12:16:59 [ASTIAV] 释放解码器上下文...
2025/06/07 12:16:59 [ASTIAV] 关闭输入上下文...
2025/06/07 12:16:59 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/07 12:16:59 [ASTIAV] ✅ 资源清理完成
2025/06/07 12:16:59 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/07 12:16:59 [MEDIA] 媒体捕获器已关闭
2025/06/07 12:16:59 [MEDIA] 视频捕获循环结束
2025/06/07 12:16:59 [PEER] 关闭媒体播放器...
2025/06/07 12:16:59 [H264] 视频文件已保存: 0 bytes
2025/06/07 12:16:59 [H264] ffplay已关闭
2025/06/07 12:16:59 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/07 12:16:59 [JPEG] ffplay已关闭
2025/06/07 12:16:59 [AUDIO] 音频文件已保存: 0 bytes
2025/06/07 12:16:59 [PLAYER] 媒体播放器已关闭
2025/06/07 12:16:59 [PEER] 关闭WebRTC管理器...
2025/06/07 12:16:59 [WEBRTC] 开始关闭WebRTC连接...
2025/06/07 12:16:59 [WEBRTC] 关闭数据通道...
2025/06/07 12:16:59 [WEBRTC] 关闭PeerConnection...
2025/06/07 12:16:59 [WEBRTC] 连接状态变化: closed
2025/06/07 12:16:59 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/07 12:16:59 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/07 12:16:59 等待资源释放...
2025/06/07 12:17:00 [WEBRTC] 数据通道状态监控: closed
2025/06/07 12:17:00 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/07 12:17:01 应用程序已安全关闭
2025/06/07 12:17:01 [PEER] 开始关闭视频通话对等端...
2025/06/07 12:17:01 [PEER] 离开房间...
2025/06/07 12:17:01 [PEER] 离开房间
2025/06/07 12:17:01 [PEER] 已离开房间
2025/06/07 12:17:01 [PEER] 关闭媒体捕获器...
2025/06/07 12:17:01 [MEDIA] 媒体捕获器已关闭
2025/06/07 12:17:01 [PEER] 关闭媒体播放器...
2025/06/07 12:17:01 [H264] 视频文件已保存: 0 bytes
2025/06/07 12:17:01 [H264] ffplay已关闭
2025/06/07 12:17:01 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/07 12:17:01 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/07 12:17:01 [JPEG] ffplay已关闭
2025/06/07 12:17:01 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/07 12:17:01 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/07 12:17:01 [PLAYER] 媒体播放器已关闭
2025/06/07 12:17:01 [PEER] 关闭WebRTC管理器...
2025/06/07 12:17:01 [WEBRTC] 开始关闭WebRTC连接...
2025/06/07 12:17:01 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/07 12:17:01 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/07 12:20:46 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/07 12:20:46 启动 Go VideoCall Peer v1.0.0
2025/06/07 12:20:46 配置文件: config/default.yaml
2025/06/07 12:20:46 调试模式: false
2025/06/07 12:20:46 [H264] 视频将保存到: debug/received_video.h264
2025/06/07 12:20:46 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/07 12:20:46 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/07 12:20:46 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/07 12:20:46 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/07 12:20:46 [PEER] 初始化视频通话对等端
2025/06/07 12:20:46 [MEDIA] 初始化媒体捕获器
2025/06/07 12:20:46 [MEDIA] 媒体捕获器初始化完成
2025/06/07 12:20:46 [PEER] 视频通话对等端初始化完成（WebRTC连接将在需要时创建）
2025/06/07 12:20:46 视频通话对等端初始化完成
2025/06/07 12:20:53 [PEER] 加入房间: xxx000
2025/06/07 12:20:53 [SIGNALING] 连接到房间: xxx000
2025/06/07 12:20:53 [PEER] 状态变化: Idle -> Connecting
2025/06/07 12:20:53 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"xxx000","clientid":"94824002"}
2025/06/07 12:20:53 [SIGNALING] 成功连接到房间: xxx000, ClientID: 94824002, IsInitiator: true
2025/06/07 12:20:53 [MEDIA] 开始媒体捕获
2025/06/07 12:20:53 [PLAYER] 开始媒体播放
2025/06/07 12:20:53 [MEDIA] 启动视频捕获循环
2025/06/07 12:20:53 [PEER] 成功加入房间: xxx000
2025/06/07 12:20:53 [MEDIA] 启动音频捕获循环
2025/06/07 12:20:53 [PEER] 信令服务器已连接
2025/06/07 12:20:53 [PEER] 状态变化: Connecting -> Connected
2025/06/07 12:20:53 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/07 12:20:53 [ASTIAV] 调试模式已启用
2025/06/07 12:20:53 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/07 12:20:53 [ASTIAV] macOS AVFoundation 设备:
2025/06/07 12:20:53 [ASTIAV]   0 - 默认摄像头
2025/06/07 12:20:53 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/07 12:20:53 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/07 12:20:53 [MEDIA] AstiAV摄像头已初始化
2025/06/07 12:20:53 [ASTIAV] 启动摄像头捕获...
2025/06/07 12:20:53 [ASTIAV] 打开输入: 0:none
2025/06/07 12:20:55 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/07 12:20:55 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/07 12:20:55 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/07 12:20:55 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/07 12:20:55 [ASTIAV] ✅ 缩放器已初始化
2025/06/07 12:20:55 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/07 12:20:55 [ASTIAV] 捕获循环已启动
2025/06/07 12:20:55 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 15207 bytes, 分辨率: 1024x600)
2025/06/07 12:20:55 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/07 12:20:57 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10480 bytes
2025/06/07 12:20:57 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10483 bytes, 分辨率: 1024x600)
2025/06/07 12:20:57 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/07 12:20:59 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10760 bytes
2025/06/07 12:20:59 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10745 bytes, 分辨率: 1024x600)
2025/06/07 12:20:59 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/07 12:21:02 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10949 bytes
2025/06/07 12:21:02 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10934 bytes, 分辨率: 1024x600)
2025/06/07 12:21:02 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/07 12:21:04 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10772 bytes
2025/06/07 12:21:04 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10754 bytes, 分辨率: 1024x600)
2025/06/07 12:21:04 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/07 12:21:05 [PEER] 发起呼叫
2025/06/07 12:21:05 [PEER] 提前创建WebRTC连接（不包含数据通道）
2025/06/07 12:21:05 [PEER] 状态变化: Connected -> OutgoingCall
2025/06/07 12:21:05 [WEBRTC] PeerConnection创建成功（未创建数据通道）
2025/06/07 12:21:05 [WEBRTC] 添加音频轨道: audio
2025/06/07 12:21:05 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"RING\"}"}
2025/06/07 12:21:05 [PEER] 呼叫已发起，等待对方响应
2025/06/07 12:21:05 [PEER] 状态变化: OutgoingCall -> Ringing
2025/06/07 12:21:05 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:21:06 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10783 bytes
2025/06/07 12:21:06 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10783 bytes, 分辨率: 1024x600)
2025/06/07 12:21:08 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10820 bytes
2025/06/07 12:21:08 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10820 bytes, 分辨率: 1024x600)
2025/06/07 12:21:11 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10743 bytes
2025/06/07 12:21:11 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10743 bytes, 分辨率: 1024x600)
2025/06/07 12:21:13 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10728 bytes, 分辨率: 1024x600)
2025/06/07 12:21:13 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10701 bytes
2025/06/07 12:21:14 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}","error":""}
2025/06/07 12:21:14 [SIGNALING] 收到自定义命令: ACCEPT_CALL, 数据: 
2025/06/07 12:21:14 [PEER] 处理自定义命令: ACCEPT_CALL, 数据: 
2025/06/07 12:21:14 [PEER] 创建并发送Offer
2025/06/07 12:21:14 [PEER] 当前WebRTC连接状态: new
2025/06/07 12:21:14 [PEER] 连接状态为new，需要确保连接已创建
2025/06/07 12:21:14 [WEBRTC] 连接已存在，当前状态: new
2025/06/07 12:21:14 [WEBRTC] 连接状态正常，跳过重复创建
2025/06/07 12:21:14 [PEER] 状态变化: Ringing -> Negotiating
2025/06/07 12:21:14 [WEBRTC] 添加音频轨道: audio
2025/06/07 12:21:14 [PEER] 作为发起方，创建数据通道
2025/06/07 12:21:14 [WEBRTC] 开始创建数据通道...
2025/06/07 12:21:14 [WEBRTC] 数据通道创建成功
2025/06/07 12:21:14 [WEBRTC] 初始数据通道状态: connecting
2025/06/07 12:21:14 [WEBRTC] 数据通道创建完成
2025/06/07 12:21:14 [MEDIA] 数据通道发送器已设置
2025/06/07 12:21:14 [WEBRTC] 信令状态变化: have-local-offer
2025/06/07 12:21:14 [WEBRTC] 创建并设置Offer成功
2025/06/07 12:21:14 [WEBRTC] 生成ICE候选: udp4 host **************:56272 (resolved: **************:56272)
2025/06/07 12:21:14 [WEBRTC] 生成ICE候选: udp4 host *************:62115 (resolved: *************:62115)
2025/06/07 12:21:14 [WEBRTC] 生成ICE候选: udp4 srflx **************:61366 related 0.0.0.0:61366 (resolved: **************:61366)
2025/06/07 12:21:14 [WEBRTC] 生成ICE候选: udp4 srflx **************:59286 related 0.0.0.0:59286 (resolved: **************:59286)
2025/06/07 12:21:15 [SIGNALING] 发送SDP offer
2025/06/07 12:21:15 [PEER] Offer已发送
2025/06/07 12:21:15 [SIGNALING] 发送ICE候选
2025/06/07 12:21:15 [SIGNALING] 发送ICE候选
2025/06/07 12:21:15 [SIGNALING] 发送ICE候选
2025/06/07 12:21:15 [SIGNALING] 发送ICE候选
2025/06/07 12:21:15 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10894 bytes, 分辨率: 1024x600)
2025/06/07 12:21:15 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10885 bytes
2025/06/07 12:21:15 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:21:16 [SIGNALING] 收到消息: {"msg":"{\"type\":\"answer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1 2\\r\\nm=video 9 UDP/TLS/RTP/SAVPF 102 106\\r\\na=rtpmap:102 H264/90000\\r\\na=rtcp-fb:102 goog-remb\\r\\na=rtcp-fb:102 transport-cc\\r\\na=rtcp-fb:102 ccm fir\\r\\na=rtcp-fb:102 nack\\r\\na=rtcp-fb:102 nack pli\\r\\na=fmtp:102 level-asymmetry-allowed=1\\r\\na=rtpmap:106 H264/90000\\r\\na=rtcp-fb:106 goog-remb\\r\\na=rtcp-fb:106 transport-cc\\r\\na=rtcp-fb:106 ccm fir\\r\\na=rtcp-fb:106 nack\\r\\na=rtcp-fb:106 nack pli\\r\\na=fmtp:106 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\\r\\na=ssrc:1 cname:webrtc-h264\\r\\na=sendrecv\\r\\na=mid:0\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp-mux\\r\\na=fingerprint:sha-256 ED:B8:61:E8:AD:24:E3:AF:C1:66:CA:06:12:8F:B7:DC:D4:78:1B:FA:F0:37:04:4C:5A:C7:95:EA:5D:B9:DE:EB\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1694075647 ************** 63882 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15117055 ************* 59050 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2130283263 ************** 63882 typ host\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:1\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 ED:B8:61:E8:AD:24:E3:AF:C1:66:CA:06:12:8F:B7:DC:D4:78:1B:FA:F0:37:04:4C:5A:C7:95:EA:5D:B9:DE:EB\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1694075647 ************** 63882 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15117055 ************* 59050 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2130283263 ************** 63882 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:2\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 ED:B8:61:E8:AD:24:E3:AF:C1:66:CA:06:12:8F:B7:DC:D4:78:1B:FA:F0:37:04:4C:5A:C7:95:EA:5D:B9:DE:EB\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1694075647 ************** 63882 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15117055 ************* 59050 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2130283263 ************** 63882 typ host\\r\\n\"}","error":""}
2025/06/07 12:21:16 [PEER] 处理RTC消息: answer
2025/06/07 12:21:16 [PEER] 处理接收到的Answer
2025/06/07 12:21:16 [WEBRTC] 信令状态变化: stable
2025/06/07 12:21:16 [WEBRTC] 设置远程answer描述成功
2025/06/07 12:21:16 [PEER] Answer已处理
2025/06/07 12:21:16 [WEBRTC] ICE连接状态变化: checking
2025/06/07 12:21:16 [WEBRTC] 连接状态变化: connecting
2025/06/07 12:21:16 [PEER] WebRTC连接状态: connecting
2025/06/07 12:21:16 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:21:16 [WEBRTC] ICE连接状态变化: connected
2025/06/07 12:21:17 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10885 bytes, 分辨率: 1024x600)
2025/06/07 12:21:17 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10845 bytes
2025/06/07 12:21:18 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:21:20 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10916 bytes, 分辨率: 1024x600)
2025/06/07 12:21:20 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10972 bytes
2025/06/07 12:21:20 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:21:22 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10895 bytes, 分辨率: 1024x600)
2025/06/07 12:21:22 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:21:22 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10818 bytes
2025/06/07 12:21:22 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:21:24 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:21:24 [MEDIA] AstiAV成功捕获摄像头帧: 390 (大小: 10902 bytes, 分辨率: 1024x600)
2025/06/07 12:21:24 [WEBRTC] ICE连接状态变化: disconnected
2025/06/07 12:21:24 [WEBRTC] 连接状态变化: disconnected
2025/06/07 12:21:24 [PEER] WebRTC连接状态: disconnected
2025/06/07 12:21:24 [ASTIAV] 已捕获 390 帧, 当前帧大小: 10834 bytes
2025/06/07 12:21:26 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:21:26 [MEDIA] AstiAV成功捕获摄像头帧: 420 (大小: 10891 bytes, 分辨率: 1024x600)
2025/06/07 12:21:26 [ASTIAV] 已捕获 420 帧, 当前帧大小: 10923 bytes
2025/06/07 12:21:28 [WEBRTC] 数据通道状态监控: connecting
2025/06/07 12:21:28 [MEDIA] AstiAV成功捕获摄像头帧: 450 (大小: 10964 bytes, 分辨率: 1024x600)
2025/06/07 12:21:29 [ASTIAV] 已捕获 450 帧, 当前帧大小: 10747 bytes
2025/06/07 12:21:29 收到信号: interrupt
2025/06/07 12:21:29 正在关闭应用程序...
2025/06/07 12:21:29 正在关闭视频通话对等端...
2025/06/07 12:21:29 [PEER] 开始关闭视频通话对等端...
2025/06/07 12:21:29 [PEER] 离开房间...
2025/06/07 12:21:29 [PEER] 离开房间
2025/06/07 12:21:29 [MEDIA] 停止媒体捕获
2025/06/07 12:21:29 [PLAYER] 停止媒体播放
2025/06/07 12:21:29 [SIGNALING] 断开信令连接
2025/06/07 12:21:29 [SIGNALING] WebSocket读取错误: read tcp *************:57465->52.81.131.193:8089: use of closed network connection
2025/06/07 12:21:29 [PEER] 已离开房间
2025/06/07 12:21:29 [PEER] 关闭媒体捕获器...
2025/06/07 12:21:29 [ASTIAV] 停止摄像头捕获...
2025/06/07 12:21:29 [ASTIAV] 等待捕获循环退出...
2025/06/07 12:21:29 [PEER] 信令服务器断开连接: <nil>
2025/06/07 12:21:29 [PEER] 信令错误: read tcp *************:57465->52.81.131.193:8089: use of closed network connection
2025/06/07 12:21:29 [ASTIAV] 开始清理AstiAV资源...
2025/06/07 12:21:29 [ASTIAV] 开始清理资源...
2025/06/07 12:21:29 [ASTIAV] 释放输入包...
2025/06/07 12:21:29 [ASTIAV] 释放解码帧...
2025/06/07 12:21:29 [ASTIAV] 释放缩放帧...
2025/06/07 12:21:29 [ASTIAV] 释放编码包...
2025/06/07 12:21:29 [ASTIAV] 释放缩放上下文...
2025/06/07 12:21:29 [ASTIAV] 释放编码器上下文...
2025/06/07 12:21:29 [ASTIAV] 释放解码器上下文...
2025/06/07 12:21:29 [ASTIAV] 关闭输入上下文...
2025/06/07 12:21:29 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/07 12:21:29 [ASTIAV] ✅ 资源清理完成
2025/06/07 12:21:29 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/07 12:21:29 [MEDIA] 媒体捕获器已关闭
2025/06/07 12:21:29 [PEER] 关闭媒体播放器...
2025/06/07 12:21:29 [MEDIA] 视频捕获循环结束
2025/06/07 12:21:29 [H264] 视频文件已保存: 0 bytes
2025/06/07 12:21:29 [H264] ffplay已关闭
2025/06/07 12:21:29 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/07 12:21:29 [JPEG] ffplay已关闭
2025/06/07 12:21:29 [AUDIO] 音频文件已保存: 0 bytes
2025/06/07 12:21:29 [PLAYER] 媒体播放器已关闭
2025/06/07 12:21:29 [PEER] 关闭WebRTC管理器...
2025/06/07 12:21:29 [WEBRTC] 开始关闭WebRTC连接...
2025/06/07 12:21:29 [WEBRTC] 关闭数据通道...
2025/06/07 12:21:29 [WEBRTC] 关闭PeerConnection...
2025/06/07 12:21:29 [WEBRTC] 连接状态变化: closed
2025/06/07 12:21:29 [PEER] WebRTC连接状态: closed
2025/06/07 12:21:29 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/07 12:21:29 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/07 12:21:29 等待资源释放...
2025/06/07 12:21:29 [WEBRTC] ICE连接状态变化: closed
2025/06/07 12:21:30 [WEBRTC] 数据通道状态监控: closed
2025/06/07 12:21:30 [WEBRTC] 💔 数据通道监控确认已关闭
2025/06/07 12:21:31 应用程序已安全关闭
2025/06/07 12:21:31 [PEER] 开始关闭视频通话对等端...
2025/06/07 12:21:31 [PEER] 离开房间...
2025/06/07 12:21:31 [PEER] 离开房间
2025/06/07 12:21:31 [PEER] 已离开房间
2025/06/07 12:21:31 [PEER] 关闭媒体捕获器...
2025/06/07 12:21:31 [MEDIA] 媒体捕获器已关闭
2025/06/07 12:21:31 [PEER] 关闭媒体播放器...
2025/06/07 12:21:31 [H264] 视频文件已保存: 0 bytes
2025/06/07 12:21:31 [H264] ffplay已关闭
2025/06/07 12:21:31 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/07 12:21:31 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/07 12:21:31 [JPEG] ffplay已关闭
2025/06/07 12:21:31 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/07 12:21:31 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/07 12:21:31 [PLAYER] 媒体播放器已关闭
2025/06/07 12:21:31 [PEER] 关闭WebRTC管理器...
2025/06/07 12:21:31 [WEBRTC] 开始关闭WebRTC连接...
2025/06/07 12:21:31 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/07 12:21:31 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/07 12:21:32 main.go:272: 日志将同时输出到控制台和文件: debug/app.log
2025/06/07 12:21:32 启动 Go VideoCall Peer v1.0.0
2025/06/07 12:21:32 配置文件: config/default.yaml
2025/06/07 12:21:32 调试模式: false
2025/06/07 12:21:32 [H264] 视频将保存到: debug/received_video.h264
2025/06/07 12:21:32 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/07 12:21:32 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/07 12:21:33 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/07 12:21:33 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/07 12:21:33 [PEER] 初始化视频通话对等端
2025/06/07 12:21:33 [MEDIA] 初始化媒体捕获器
2025/06/07 12:21:33 [MEDIA] 媒体捕获器初始化完成
2025/06/07 12:21:33 [PEER] 视频通话对等端初始化完成（WebRTC连接将在需要时创建）
2025/06/07 12:21:33 视频通话对等端初始化完成
2025/06/07 12:21:43 [PEER] 加入房间: xxx001
2025/06/07 12:21:43 [SIGNALING] 连接到房间: xxx001
2025/06/07 12:21:43 [PEER] 状态变化: Idle -> Connecting
2025/06/07 12:21:44 [SIGNALING] 发送WS消息: {"cmd":"register","roomid":"xxx001","clientid":"43677317"}
2025/06/07 12:21:44 [SIGNALING] 成功连接到房间: xxx001, ClientID: 43677317, IsInitiator: false
2025/06/07 12:21:44 [MEDIA] 开始媒体捕获
2025/06/07 12:21:44 [PLAYER] 开始媒体播放
2025/06/07 12:21:44 [PEER] 成功加入房间: xxx001
2025/06/07 12:21:44 [MEDIA] 启动视频捕获循环
2025/06/07 12:21:44 [MEDIA] 启动音频捕获循环
2025/06/07 12:21:44 [PEER] 状态变化: Connecting -> Connected
2025/06/07 12:21:44 [PEER] 信令服务器已连接
2025/06/07 12:21:44 [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps, 设备: 0 (avfoundation)
2025/06/07 12:21:44 [ASTIAV] 调试模式已启用
2025/06/07 12:21:44 [ASTIAV] 列出可用设备 (avfoundation):
2025/06/07 12:21:44 [ASTIAV] macOS AVFoundation 设备:
2025/06/07 12:21:44 [ASTIAV]   0 - 默认摄像头
2025/06/07 12:21:44 [ASTIAV]   1 - 第二个摄像头（如果有）
2025/06/07 12:21:44 [ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i ""
2025/06/07 12:21:44 [MEDIA] AstiAV摄像头已初始化
2025/06/07 12:21:44 [ASTIAV] 启动摄像头捕获...
2025/06/07 12:21:44 [ASTIAV] 打开输入: 0:none
2025/06/07 12:21:45 [ASTIAV] 找到视频流: 1280x720, rawvideo
2025/06/07 12:21:45 [ASTIAV] 解码器已初始化: rawvideo 1280x720 uyvy422
2025/06/07 12:21:45 [ASTIAV] 编码器已初始化: MJPEG 1024x600, 质量: 5
2025/06/07 12:21:45 [ASTIAV] 缩放器检查: 1280x720 uyvy422 -> 1024x600 yuvj420p
2025/06/07 12:21:45 [ASTIAV] ✅ 缩放器已初始化
2025/06/07 12:21:45 [ASTIAV] ✅ 摄像头捕获已启动
2025/06/07 12:21:45 [ASTIAV] 捕获循环已启动
2025/06/07 12:21:45 [MEDIA] AstiAV成功捕获摄像头帧: 0 (大小: 15819 bytes, 分辨率: 1024x600)
2025/06/07 12:21:45 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_0.jpg
2025/06/07 12:21:47 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"RING\"}","error":""}
2025/06/07 12:21:47 [SIGNALING] 收到自定义命令: RING, 数据: 
2025/06/07 12:21:47 [PEER] 处理自定义命令: RING, 数据: 
2025/06/07 12:21:47 [PEER] 状态变化: Connected -> IncomingCall
2025/06/07 12:21:47 [ASTIAV] 已捕获 30 帧, 当前帧大小: 10848 bytes
2025/06/07 12:21:47 [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: 10911 bytes, 分辨率: 1024x600)
2025/06/07 12:21:47 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_30.jpg
2025/06/07 12:21:49 [ASTIAV] 已捕获 60 帧, 当前帧大小: 10628 bytes
2025/06/07 12:21:49 [MEDIA] AstiAV成功捕获摄像头帧: 60 (大小: 10664 bytes, 分辨率: 1024x600)
2025/06/07 12:21:49 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_60.jpg
2025/06/07 12:21:51 [PEER] 接受呼叫
2025/06/07 12:21:51 [WEBRTC] PeerConnection创建成功（未创建数据通道）
2025/06/07 12:21:51 [WEBRTC] 添加音频轨道: audio
2025/06/07 12:21:51 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}"}
2025/06/07 12:21:51 [PEER] 呼叫已接受，开始WebRTC协商
2025/06/07 12:21:51 [PEER] 状态变化: IncomingCall -> Negotiating
2025/06/07 12:21:51 [SIGNALING] 收到消息: {"msg":"{\"type\":\"offer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:0\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 ED:B8:61:E8:AD:24:E3:AF:C1:66:CA:06:12:8F:B7:DC:D4:78:1B:FA:F0:37:04:4C:5A:C7:95:EA:5D:B9:DE:EB\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1694075903 ************** 63883 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15107839 ************* 59014 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2130283519 ************** 63883 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:1\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 ED:B8:61:E8:AD:24:E3:AF:C1:66:CA:06:12:8F:B7:DC:D4:78:1B:FA:F0:37:04:4C:5A:C7:95:EA:5D:B9:DE:EB\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1694075903 ************** 63883 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15107839 ************* 59014 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2130283519 ************** 63883 typ host\\r\\n\"}","error":""}
2025/06/07 12:21:51 [PEER] 处理RTC消息: offer
2025/06/07 12:21:51 [PEER] 处理接收到的Offer
2025/06/07 12:21:51 [PEER] 作为被呼叫方，创建WebRTC连接但不创建数据通道
2025/06/07 12:21:51 [WEBRTC] 连接已存在，当前状态: new
2025/06/07 12:21:51 [WEBRTC] 连接状态正常，跳过重复创建
2025/06/07 12:21:51 [WEBRTC] 添加音频轨道: audio
2025/06/07 12:21:51 [WEBRTC] 信令状态变化: have-remote-offer
2025/06/07 12:21:51 [WEBRTC] 设置远程offer描述成功
2025/06/07 12:21:51 [WEBRTC] ICE连接状态变化: checking
2025/06/07 12:21:51 [WEBRTC] 连接状态变化: connecting
2025/06/07 12:21:51 [PEER] WebRTC连接状态: connecting
2025/06/07 12:21:51 [WEBRTC] 信令状态变化: stable
2025/06/07 12:21:51 [WEBRTC] 创建并设置Answer成功
2025/06/07 12:21:51 [WEBRTC] 生成ICE候选: udp4 host **************:59395 (resolved: **************:59395)
2025/06/07 12:21:51 [WEBRTC] 生成ICE候选: udp4 host *************:63062 (resolved: *************:63062)
2025/06/07 12:21:51 [SIGNALING] 发送SDP answer
2025/06/07 12:21:51 [PEER] Answer已发送
2025/06/07 12:21:51 [SIGNALING] 发送ICE候选
2025/06/07 12:21:51 [SIGNALING] 发送ICE候选
2025/06/07 12:21:52 [WEBRTC] ICE连接状态变化: connected
2025/06/07 12:21:52 [WEBRTC] 生成ICE候选: udp4 srflx **************:55225 related 0.0.0.0:55225 (resolved: **************:55225)
2025/06/07 12:21:52 [WEBRTC] 生成ICE候选: udp4 srflx **************:54187 related 0.0.0.0:54187 (resolved: **************:54187)
2025/06/07 12:21:52 [ASTIAV] 已捕获 90 帧, 当前帧大小: 10730 bytes
2025/06/07 12:21:52 [SIGNALING] 发送ICE候选
2025/06/07 12:21:52 [SIGNALING] 发送ICE候选
2025/06/07 12:21:52 [MEDIA] AstiAV成功捕获摄像头帧: 90 (大小: 10765 bytes, 分辨率: 1024x600)
2025/06/07 12:21:52 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_90.jpg
2025/06/07 12:21:53 [WEBRTC] 连接状态变化: connected
2025/06/07 12:21:53 [PEER] WebRTC连接状态: connected
2025/06/07 12:21:53 [PEER] 状态变化: Negotiating -> InCall
2025/06/07 12:21:53 [WEBRTC] 接收到远程轨道:  (类型: audio)
2025/06/07 12:21:53 [PEER] 接收到远程轨道:  (类型: audio)
2025/06/07 12:21:53 [PLAYER] 设置远程音频轨道:  (类型: audio, 编解码器: audio/PCMA, 播放状态: true)
2025/06/07 12:21:53 [PLAYER] 开始播放音频轨道:  (编解码器: audio/PCMA)
2025/06/07 12:21:53 [PLAYER] 收到音频包 #1: 序号=0, 时间戳=0, 大小=160, 负载类型=8
2025/06/07 12:21:53 [PLAYER] 收到音频包: 序号=0, 时间戳=0, 大小=160
2025/06/07 12:21:54 [ASTIAV] 已捕获 120 帧, 当前帧大小: 10852 bytes
2025/06/07 12:21:54 [MEDIA] AstiAV成功捕获摄像头帧: 120 (大小: 10818 bytes, 分辨率: 1024x600)
2025/06/07 12:21:54 [DEBUG] 已保存AstiAV摄像头帧样本: debug/astiav_frame_120.jpg
2025/06/07 12:21:56 [ASTIAV] 已捕获 150 帧, 当前帧大小: 10806 bytes
2025/06/07 12:21:56 [MEDIA] AstiAV成功捕获摄像头帧: 150 (大小: 10842 bytes, 分辨率: 1024x600)
2025/06/07 12:21:58 [ASTIAV] 已捕获 180 帧, 当前帧大小: 10804 bytes
2025/06/07 12:21:58 [MEDIA] AstiAV成功捕获摄像头帧: 180 (大小: 10822 bytes, 分辨率: 1024x600)
2025/06/07 12:22:01 [ASTIAV] 已捕获 210 帧, 当前帧大小: 10800 bytes
2025/06/07 12:22:01 [MEDIA] AstiAV成功捕获摄像头帧: 210 (大小: 10780 bytes, 分辨率: 1024x600)
2025/06/07 12:22:03 [ASTIAV] 已捕获 240 帧, 当前帧大小: 10750 bytes
2025/06/07 12:22:03 [MEDIA] AstiAV成功捕获摄像头帧: 240 (大小: 10772 bytes, 分辨率: 1024x600)
2025/06/07 12:22:03 [PLAYER] 收到音频包 #500: 序号=499, 时间戳=79840, 大小=160, 负载类型=8
2025/06/07 12:22:03 [PLAYER] 收到音频包: 序号=500, 时间戳=80000, 大小=160
2025/06/07 12:22:05 [ASTIAV] 已捕获 270 帧, 当前帧大小: 10692 bytes
2025/06/07 12:22:05 [MEDIA] AstiAV成功捕获摄像头帧: 270 (大小: 10680 bytes, 分辨率: 1024x600)
2025/06/07 12:22:07 [ASTIAV] 已捕获 300 帧, 当前帧大小: 10895 bytes
2025/06/07 12:22:07 [MEDIA] AstiAV成功捕获摄像头帧: 300 (大小: 10875 bytes, 分辨率: 1024x600)
2025/06/07 12:22:09 [ASTIAV] 已捕获 330 帧, 当前帧大小: 10952 bytes
2025/06/07 12:22:09 [MEDIA] AstiAV成功捕获摄像头帧: 330 (大小: 10954 bytes, 分辨率: 1024x600)
2025/06/07 12:22:12 [ASTIAV] 已捕获 360 帧, 当前帧大小: 10924 bytes
2025/06/07 12:22:12 [MEDIA] AstiAV成功捕获摄像头帧: 360 (大小: 10950 bytes, 分辨率: 1024x600)
2025/06/07 12:22:12 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:22:13 [PLAYER] 收到音频包 #1000: 序号=999, 时间戳=159840, 大小=160, 负载类型=8
2025/06/07 12:22:13 [AUDIO] 已保存音频包: 1000, 总字节: 160000
2025/06/07 12:22:13 [PLAYER] 收到音频包: 序号=1000, 时间戳=160000, 大小=160
2025/06/07 12:22:14 [ASTIAV] 已捕获 390 帧, 当前帧大小: 10823 bytes
2025/06/07 12:22:14 [MEDIA] AstiAV成功捕获摄像头帧: 390 (大小: 10823 bytes, 分辨率: 1024x600)
2025/06/07 12:22:16 [ASTIAV] 已捕获 420 帧, 当前帧大小: 10719 bytes
2025/06/07 12:22:16 [MEDIA] AstiAV成功捕获摄像头帧: 420 (大小: 10719 bytes, 分辨率: 1024x600)
2025/06/07 12:22:18 [ASTIAV] 已捕获 450 帧, 当前帧大小: 11057 bytes
2025/06/07 12:22:18 [MEDIA] AstiAV成功捕获摄像头帧: 450 (大小: 11057 bytes, 分辨率: 1024x600)
2025/06/07 12:22:20 [ASTIAV] 已捕获 480 帧, 当前帧大小: 11035 bytes
2025/06/07 12:22:20 [MEDIA] AstiAV成功捕获摄像头帧: 480 (大小: 11035 bytes, 分辨率: 1024x600)
2025/06/07 12:22:23 [ASTIAV] 已捕获 510 帧, 当前帧大小: 10859 bytes
2025/06/07 12:22:23 [MEDIA] AstiAV成功捕获摄像头帧: 510 (大小: 10859 bytes, 分辨率: 1024x600)
2025/06/07 12:22:23 [PLAYER] 收到音频包 #1500: 序号=1499, 时间戳=239840, 大小=160, 负载类型=8
2025/06/07 12:22:23 [PLAYER] 收到音频包: 序号=1500, 时间戳=240000, 大小=160
2025/06/07 12:22:25 [ASTIAV] 已捕获 540 帧, 当前帧大小: 10802 bytes
2025/06/07 12:22:25 [MEDIA] AstiAV成功捕获摄像头帧: 540 (大小: 10802 bytes, 分辨率: 1024x600)
2025/06/07 12:22:27 [ASTIAV] 已捕获 570 帧, 当前帧大小: 10791 bytes
2025/06/07 12:22:27 [MEDIA] AstiAV成功捕获摄像头帧: 570 (大小: 10791 bytes, 分辨率: 1024x600)
2025/06/07 12:22:29 [ASTIAV] 已捕获 600 帧, 当前帧大小: 10798 bytes
2025/06/07 12:22:29 [MEDIA] AstiAV成功捕获摄像头帧: 600 (大小: 10798 bytes, 分辨率: 1024x600)
2025/06/07 12:22:32 [ASTIAV] 已捕获 630 帧, 当前帧大小: 10773 bytes
2025/06/07 12:22:32 [MEDIA] AstiAV成功捕获摄像头帧: 630 (大小: 10773 bytes, 分辨率: 1024x600)
2025/06/07 12:22:33 [PLAYER] 收到音频包 #2000: 序号=1999, 时间戳=319840, 大小=160, 负载类型=8
2025/06/07 12:22:33 [AUDIO] 已保存音频包: 2000, 总字节: 320000
2025/06/07 12:22:33 [PLAYER] 收到音频包: 序号=2000, 时间戳=320000, 大小=160
2025/06/07 12:22:34 [ASTIAV] 已捕获 660 帧, 当前帧大小: 10746 bytes
2025/06/07 12:22:34 [MEDIA] AstiAV成功捕获摄像头帧: 660 (大小: 10746 bytes, 分辨率: 1024x600)
2025/06/07 12:22:36 [ASTIAV] 已捕获 690 帧, 当前帧大小: 10809 bytes
2025/06/07 12:22:36 [MEDIA] AstiAV成功捕获摄像头帧: 690 (大小: 10809 bytes, 分辨率: 1024x600)
2025/06/07 12:22:38 [MEDIA] AstiAV成功捕获摄像头帧: 720 (大小: 10741 bytes, 分辨率: 1024x600)
2025/06/07 12:22:38 [ASTIAV] 已捕获 720 帧, 当前帧大小: 10741 bytes
2025/06/07 12:22:39 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:22:40 [MEDIA] AstiAV成功捕获摄像头帧: 750 (大小: 10678 bytes, 分辨率: 1024x600)
2025/06/07 12:22:41 [ASTIAV] 已捕获 750 帧, 当前帧大小: 10604 bytes
2025/06/07 12:22:43 [MEDIA] AstiAV成功捕获摄像头帧: 780 (大小: 10788 bytes, 分辨率: 1024x600)
2025/06/07 12:22:43 [ASTIAV] 已捕获 780 帧, 当前帧大小: 10723 bytes
2025/06/07 12:22:43 [PLAYER] 收到音频包 #2500: 序号=2499, 时间戳=399840, 大小=160, 负载类型=8
2025/06/07 12:22:43 [PLAYER] 收到音频包: 序号=2500, 时间戳=400000, 大小=160
2025/06/07 12:22:45 [MEDIA] AstiAV成功捕获摄像头帧: 810 (大小: 10684 bytes, 分辨率: 1024x600)
2025/06/07 12:22:45 [ASTIAV] 已捕获 810 帧, 当前帧大小: 10665 bytes
2025/06/07 12:22:47 [MEDIA] AstiAV成功捕获摄像头帧: 840 (大小: 10644 bytes, 分辨率: 1024x600)
2025/06/07 12:22:47 [ASTIAV] 已捕获 840 帧, 当前帧大小: 10645 bytes
2025/06/07 12:22:49 [MEDIA] AstiAV成功捕获摄像头帧: 870 (大小: 10678 bytes, 分辨率: 1024x600)
2025/06/07 12:22:49 [ASTIAV] 已捕获 870 帧, 当前帧大小: 10690 bytes
2025/06/07 12:22:52 [MEDIA] AstiAV成功捕获摄像头帧: 900 (大小: 10659 bytes, 分辨率: 1024x600)
2025/06/07 12:22:52 [ASTIAV] 已捕获 900 帧, 当前帧大小: 10757 bytes
2025/06/07 12:22:53 [PLAYER] 收到音频包 #3000: 序号=2999, 时间戳=479840, 大小=160, 负载类型=8
2025/06/07 12:22:53 [AUDIO] 已保存音频包: 3000, 总字节: 480000
2025/06/07 12:22:53 [PLAYER] 收到音频包: 序号=3000, 时间戳=480000, 大小=160
2025/06/07 12:22:54 [MEDIA] AstiAV成功捕获摄像头帧: 930 (大小: 10782 bytes, 分辨率: 1024x600)
2025/06/07 12:22:54 [ASTIAV] 已捕获 930 帧, 当前帧大小: 10701 bytes
2025/06/07 12:22:56 [MEDIA] AstiAV成功捕获摄像头帧: 960 (大小: 11100 bytes, 分辨率: 1024x600)
2025/06/07 12:22:56 [ASTIAV] 已捕获 960 帧, 当前帧大小: 11117 bytes
2025/06/07 12:22:58 [MEDIA] AstiAV成功捕获摄像头帧: 990 (大小: 10987 bytes, 分辨率: 1024x600)
2025/06/07 12:22:58 [ASTIAV] 已捕获 990 帧, 当前帧大小: 10919 bytes
2025/06/07 12:23:00 [PEER] 挂断呼叫
2025/06/07 12:23:00 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"DENY_CALL\"}"}
2025/06/07 12:23:00 [PEER] 状态变化: InCall -> HangingUp
2025/06/07 12:23:00 [PEER] 呼叫已挂断
2025/06/07 12:23:00 [PEER] 状态变化: HangingUp -> Connected
2025/06/07 12:23:01 [MEDIA] AstiAV成功捕获摄像头帧: 1020 (大小: 10696 bytes, 分辨率: 1024x600)
2025/06/07 12:23:01 [ASTIAV] 已捕获 1020 帧, 当前帧大小: 10740 bytes
2025/06/07 12:23:03 [MEDIA] AstiAV成功捕获摄像头帧: 1050 (大小: 11018 bytes, 分辨率: 1024x600)
2025/06/07 12:23:03 [ASTIAV] 已捕获 1050 帧, 当前帧大小: 10981 bytes
2025/06/07 12:23:05 [MEDIA] AstiAV成功捕获摄像头帧: 1080 (大小: 11053 bytes, 分辨率: 1024x600)
2025/06/07 12:23:05 [ASTIAV] 已捕获 1080 帧, 当前帧大小: 11020 bytes
2025/06/07 12:23:06 [WEBRTC] ICE连接状态变化: disconnected
2025/06/07 12:23:06 [WEBRTC] 连接状态变化: disconnected
2025/06/07 12:23:06 [PEER] WebRTC连接状态: disconnected
2025/06/07 12:23:07 [MEDIA] AstiAV成功捕获摄像头帧: 1110 (大小: 10601 bytes, 分辨率: 1024x600)
2025/06/07 12:23:07 [ASTIAV] 已捕获 1110 帧, 当前帧大小: 10559 bytes
2025/06/07 12:23:08 [SIGNALING] 收到消息: {"msg":"{\"type\":\"customized\",\"data\":\"RING\"}","error":""}
2025/06/07 12:23:08 [SIGNALING] 收到自定义命令: RING, 数据: 
2025/06/07 12:23:08 [PEER] 处理自定义命令: RING, 数据: 
2025/06/07 12:23:08 [PEER] 状态变化: Connected -> IncomingCall
2025/06/07 12:23:09 [MEDIA] AstiAV成功捕获摄像头帧: 1140 (大小: 11028 bytes, 分辨率: 1024x600)
2025/06/07 12:23:09 [ASTIAV] 已捕获 1140 帧, 当前帧大小: 11033 bytes
2025/06/07 12:23:12 [MEDIA] AstiAV成功捕获摄像头帧: 1170 (大小: 10618 bytes, 分辨率: 1024x600)
2025/06/07 12:23:12 [ASTIAV] 已捕获 1170 帧, 当前帧大小: 10708 bytes
2025/06/07 12:23:12 [PEER] 接受呼叫
2025/06/07 12:23:12 [WEBRTC] 连接已存在，当前状态: disconnected
2025/06/07 12:23:12 [WEBRTC] 连接状态正常，跳过重复创建
2025/06/07 12:23:12 [WEBRTC] 添加音频轨道: audio
2025/06/07 12:23:12 [SIGNALING] 发送WS消息: {"cmd":"send","msg":"{\"type\":\"customized\",\"data\":\"ACCEPT_CALL\"}"}
2025/06/07 12:23:12 [PEER] 呼叫已接受，开始WebRTC协商
2025/06/07 12:23:12 [PEER] 状态变化: IncomingCall -> Negotiating
2025/06/07 12:23:13 [SIGNALING] 收到消息: {"msg":"{\"type\":\"offer\",\"sdp\":\"v=0\\r\\no=- 7801614086411001801 2 IN IP4 0.0.0.0\\r\\ns=-\\r\\nt=0 0\\r\\na=msid-semantic: esp-webrtc\\r\\na=group:BUNDLE 0 1\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 8\\r\\nc=IN IP4 0.0.0.0\\r\\na=mid:0\\r\\na=sendrecv\\r\\na=rtcp-mux\\r\\na=rtpmap:8 PCMA/8000\\r\\na=ssrc:4 cname:EspSSpbA0\\r\\na=fingerprint:sha-256 ED:B8:61:E8:AD:24:E3:AF:C1:66:CA:06:12:8F:B7:DC:D4:78:1B:FA:F0:37:04:4C:5A:C7:95:EA:5D:B9:DE:EB\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1694076159 ************** 63884 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15112191 ************* 59031 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2130283775 ************** 63884 typ host\\r\\nm=application 50712 UDP/DTLS/SCTP webrtc-datachannel\\r\\na=mid:1\\r\\na=sctp-port:5000\\r\\nc=IN IP4 0.0.0.0\\r\\na=max-message-size:262144\\r\\na=fingerprint:sha-256 ED:B8:61:E8:AD:24:E3:AF:C1:66:CA:06:12:8F:B7:DC:D4:78:1B:FA:F0:37:04:4C:5A:C7:95:EA:5D:B9:DE:EB\\r\\na=setup:passive\\r\\na=ice-ufrag:WwUl\\r\\na=ice-pwd:Pd9ZwfQgpeN3WiStdYMx5Ru0\\r\\na=candidate:0 1 UDP 1694076159 ************** 63884 typ srflx raddr 0.0.0.0 rport 0\\r\\na=candidate:1 1 UDP 15112191 ************* 59031 typ relay raddr 0.0.0.0 rport 0\\r\\na=candidate:2 1 UDP 2130283775 ************** 63884 typ host\\r\\n\"}","error":""}
2025/06/07 12:23:13 [PEER] 处理RTC消息: offer
2025/06/07 12:23:13 [PEER] 处理接收到的Offer
2025/06/07 12:23:13 [WEBRTC] 信令状态变化: have-remote-offer
2025/06/07 12:23:13 [WEBRTC] 设置远程offer描述成功
2025/06/07 12:23:13 [WEBRTC] 创建并设置Answer成功
2025/06/07 12:23:13 [WEBRTC] 信令状态变化: stable
2025/06/07 12:23:13 [SIGNALING] 发送SDP answer
2025/06/07 12:23:13 [PEER] Answer已发送
2025/06/07 12:23:13 [WEBRTC] ICE连接状态变化: connected
2025/06/07 12:23:13 [WEBRTC] 连接状态变化: connected
2025/06/07 12:23:13 [PEER] WebRTC连接状态: connected
2025/06/07 12:23:13 [PEER] 状态变化: Negotiating -> InCall
2025/06/07 12:23:14 [MEDIA] AstiAV成功捕获摄像头帧: 1200 (大小: 10792 bytes, 分辨率: 1024x600)
2025/06/07 12:23:14 [ASTIAV] 已捕获 1200 帧, 当前帧大小: 10788 bytes
2025/06/07 12:23:16 [MEDIA] AstiAV成功捕获摄像头帧: 1230 (大小: 10744 bytes, 分辨率: 1024x600)
2025/06/07 12:23:16 [ASTIAV] 已捕获 1230 帧, 当前帧大小: 10732 bytes
2025/06/07 12:23:18 [MEDIA] AstiAV成功捕获摄像头帧: 1260 (大小: 10718 bytes, 分辨率: 1024x600)
2025/06/07 12:23:18 [ASTIAV] 已捕获 1260 帧, 当前帧大小: 10684 bytes
2025/06/07 12:23:21 [MEDIA] AstiAV成功捕获摄像头帧: 1290 (大小: 10674 bytes, 分辨率: 1024x600)
2025/06/07 12:23:21 [ASTIAV] 已捕获 1290 帧, 当前帧大小: 10704 bytes
2025/06/07 12:23:23 [MEDIA] AstiAV成功捕获摄像头帧: 1320 (大小: 10806 bytes, 分辨率: 1024x600)
2025/06/07 12:23:23 [ASTIAV] 已捕获 1320 帧, 当前帧大小: 10759 bytes
2025/06/07 12:23:25 [MEDIA] AstiAV成功捕获摄像头帧: 1350 (大小: 10792 bytes, 分辨率: 1024x600)
2025/06/07 12:23:25 [ASTIAV] 已捕获 1350 帧, 当前帧大小: 10859 bytes
2025/06/07 12:23:27 [MEDIA] AstiAV成功捕获摄像头帧: 1380 (大小: 10562 bytes, 分辨率: 1024x600)
2025/06/07 12:23:27 [ASTIAV] 已捕获 1380 帧, 当前帧大小: 10615 bytes
2025/06/07 12:23:30 [MEDIA] AstiAV成功捕获摄像头帧: 1410 (大小: 10814 bytes, 分辨率: 1024x600)
2025/06/07 12:23:30 [ASTIAV] 已捕获 1410 帧, 当前帧大小: 10825 bytes
2025/06/07 12:23:32 [MEDIA] AstiAV成功捕获摄像头帧: 1440 (大小: 10640 bytes, 分辨率: 1024x600)
2025/06/07 12:23:32 [ASTIAV] 已捕获 1440 帧, 当前帧大小: 10622 bytes
2025/06/07 12:23:34 [MEDIA] AstiAV成功捕获摄像头帧: 1470 (大小: 10539 bytes, 分辨率: 1024x600)
2025/06/07 12:23:34 [ASTIAV] 已捕获 1470 帧, 当前帧大小: 10646 bytes
2025/06/07 12:23:36 [MEDIA] AstiAV成功捕获摄像头帧: 1500 (大小: 11132 bytes, 分辨率: 1024x600)
2025/06/07 12:23:36 [ASTIAV] 已捕获 1500 帧, 当前帧大小: 11193 bytes
2025/06/07 12:23:39 [MEDIA] AstiAV成功捕获摄像头帧: 1530 (大小: 11180 bytes, 分辨率: 1024x600)
2025/06/07 12:23:39 [ASTIAV] 已捕获 1530 帧, 当前帧大小: 11151 bytes
2025/06/07 12:23:39 [WEBRTC] ICE连接状态变化: disconnected
2025/06/07 12:23:39 [WEBRTC] 连接状态变化: disconnected
2025/06/07 12:23:39 [PEER] WebRTC连接状态: disconnected
2025/06/07 12:23:41 [MEDIA] AstiAV成功捕获摄像头帧: 1560 (大小: 11103 bytes, 分辨率: 1024x600)
2025/06/07 12:23:41 [ASTIAV] 已捕获 1560 帧, 当前帧大小: 11117 bytes
2025/06/07 12:23:43 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:23:43 [MEDIA] AstiAV成功捕获摄像头帧: 1590 (大小: 11090 bytes, 分辨率: 1024x600)
2025/06/07 12:23:44 [ASTIAV] 已捕获 1590 帧, 当前帧大小: 11051 bytes
2025/06/07 12:23:46 [MEDIA] AstiAV成功捕获摄像头帧: 1620 (大小: 11083 bytes, 分辨率: 1024x600)
2025/06/07 12:23:46 [ASTIAV] 已捕获 1620 帧, 当前帧大小: 11137 bytes
2025/06/07 12:23:48 [MEDIA] AstiAV成功捕获摄像头帧: 1650 (大小: 11137 bytes, 分辨率: 1024x600)
2025/06/07 12:23:48 [ASTIAV] 已捕获 1650 帧, 当前帧大小: 11139 bytes
2025/06/07 12:23:50 [MEDIA] AstiAV成功捕获摄像头帧: 1680 (大小: 11187 bytes, 分辨率: 1024x600)
2025/06/07 12:23:50 [ASTIAV] 已捕获 1680 帧, 当前帧大小: 11191 bytes
2025/06/07 12:23:51 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:23:52 [MEDIA] AstiAV成功捕获摄像头帧: 1710 (大小: 11189 bytes, 分辨率: 1024x600)
2025/06/07 12:23:53 [ASTIAV] 已捕获 1710 帧, 当前帧大小: 11208 bytes
2025/06/07 12:23:55 [MEDIA] AstiAV成功捕获摄像头帧: 1740 (大小: 11181 bytes, 分辨率: 1024x600)
2025/06/07 12:23:55 [ASTIAV] 已捕获 1740 帧, 当前帧大小: 11166 bytes
2025/06/07 12:23:57 [MEDIA] AstiAV成功捕获摄像头帧: 1770 (大小: 11026 bytes, 分辨率: 1024x600)
2025/06/07 12:23:57 [ASTIAV] 已捕获 1770 帧, 当前帧大小: 11109 bytes
2025/06/07 12:23:58 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:23:59 [MEDIA] AstiAV成功捕获摄像头帧: 1800 (大小: 11057 bytes, 分辨率: 1024x600)
2025/06/07 12:24:00 [ASTIAV] 已捕获 1800 帧, 当前帧大小: 11004 bytes
2025/06/07 12:24:02 [MEDIA] AstiAV成功捕获摄像头帧: 1830 (大小: 10988 bytes, 分辨率: 1024x600)
2025/06/07 12:24:02 [ASTIAV] 已捕获 1830 帧, 当前帧大小: 11058 bytes
2025/06/07 12:24:04 [MEDIA] AstiAV成功捕获摄像头帧: 1860 (大小: 11079 bytes, 分辨率: 1024x600)
2025/06/07 12:24:04 [ASTIAV] 已捕获 1860 帧, 当前帧大小: 11090 bytes
2025/06/07 12:24:05 [WEBRTC] ICE连接状态变化: failed
2025/06/07 12:24:05 [WEBRTC] 连接状态变化: failed
2025/06/07 12:24:05 [PEER] WebRTC连接状态: failed
2025/06/07 12:24:06 [MEDIA] AstiAV成功捕获摄像头帧: 1890 (大小: 11001 bytes, 分辨率: 1024x600)
2025/06/07 12:24:07 [ASTIAV] 已捕获 1890 帧, 当前帧大小: 11045 bytes
2025/06/07 12:24:08 [MEDIA] AstiAV成功捕获摄像头帧: 1920 (大小: 10979 bytes, 分辨率: 1024x600)
2025/06/07 12:24:09 [ASTIAV] 已捕获 1920 帧, 当前帧大小: 10955 bytes
2025/06/07 12:24:11 [MEDIA] AstiAV成功捕获摄像头帧: 1950 (大小: 11060 bytes, 分辨率: 1024x600)
2025/06/07 12:24:11 [ASTIAV] 已捕获 1950 帧, 当前帧大小: 11051 bytes
2025/06/07 12:24:13 [MEDIA] AstiAV成功捕获摄像头帧: 1980 (大小: 11076 bytes, 分辨率: 1024x600)
2025/06/07 12:24:14 [ASTIAV] 已捕获 1980 帧, 当前帧大小: 11065 bytes
2025/06/07 12:24:15 [MEDIA] AstiAV成功捕获摄像头帧: 2010 (大小: 11057 bytes, 分辨率: 1024x600)
2025/06/07 12:24:16 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:24:16 [ASTIAV] 已捕获 2010 帧, 当前帧大小: 10952 bytes
2025/06/07 12:24:17 [MEDIA] AstiAV成功捕获摄像头帧: 2040 (大小: 11002 bytes, 分辨率: 1024x600)
2025/06/07 12:24:18 [ASTIAV] 已捕获 2040 帧, 当前帧大小: 11003 bytes
2025/06/07 12:24:20 [MEDIA] AstiAV成功捕获摄像头帧: 2070 (大小: 11043 bytes, 分辨率: 1024x600)
2025/06/07 12:24:20 [ASTIAV] 已捕获 2070 帧, 当前帧大小: 11049 bytes
2025/06/07 12:24:22 [MEDIA] AstiAV成功捕获摄像头帧: 2100 (大小: 11073 bytes, 分辨率: 1024x600)
2025/06/07 12:24:23 [ASTIAV] 已捕获 2100 帧, 当前帧大小: 11073 bytes
2025/06/07 12:24:24 [MEDIA] AstiAV成功捕获摄像头帧: 2130 (大小: 11016 bytes, 分辨率: 1024x600)
2025/06/07 12:24:25 [ASTIAV] 已捕获 2130 帧, 当前帧大小: 11029 bytes
2025/06/07 12:24:26 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:24:27 [MEDIA] AstiAV成功捕获摄像头帧: 2160 (大小: 11016 bytes, 分辨率: 1024x600)
2025/06/07 12:24:27 [ASTIAV] 已捕获 2160 帧, 当前帧大小: 10975 bytes
2025/06/07 12:24:29 [MEDIA] AstiAV成功捕获摄像头帧: 2190 (大小: 11063 bytes, 分辨率: 1024x600)
2025/06/07 12:24:30 [ASTIAV] 已捕获 2190 帧, 当前帧大小: 11080 bytes
2025/06/07 12:24:31 [MEDIA] AstiAV成功捕获摄像头帧: 2220 (大小: 11046 bytes, 分辨率: 1024x600)
2025/06/07 12:24:32 [ASTIAV] 已捕获 2220 帧, 当前帧大小: 11017 bytes
2025/06/07 12:24:34 [MEDIA] AstiAV成功捕获摄像头帧: 2250 (大小: 10986 bytes, 分辨率: 1024x600)
2025/06/07 12:24:34 [ASTIAV] 已捕获 2250 帧, 当前帧大小: 11019 bytes
2025/06/07 12:24:36 [MEDIA] AstiAV成功捕获摄像头帧: 2280 (大小: 11026 bytes, 分辨率: 1024x600)
2025/06/07 12:24:37 [ASTIAV] 已捕获 2280 帧, 当前帧大小: 10967 bytes
2025/06/07 12:24:38 [MEDIA] AstiAV成功捕获摄像头帧: 2310 (大小: 11006 bytes, 分辨率: 1024x600)
2025/06/07 12:24:39 [ASTIAV] 已捕获 2310 帧, 当前帧大小: 11010 bytes
2025/06/07 12:24:40 [MEDIA] AstiAV成功捕获摄像头帧: 2340 (大小: 11041 bytes, 分辨率: 1024x600)
2025/06/07 12:24:41 [ASTIAV] 已捕获 2340 帧, 当前帧大小: 10958 bytes
2025/06/07 12:24:43 [MEDIA] AstiAV成功捕获摄像头帧: 2370 (大小: 11004 bytes, 分辨率: 1024x600)
2025/06/07 12:24:44 [ASTIAV] 已捕获 2370 帧, 当前帧大小: 11030 bytes
2025/06/07 12:24:45 [MEDIA] AstiAV成功捕获摄像头帧: 2400 (大小: 11042 bytes, 分辨率: 1024x600)
2025/06/07 12:24:46 [ASTIAV] 已捕获 2400 帧, 当前帧大小: 11013 bytes
2025/06/07 12:24:47 [MEDIA] AstiAV成功捕获摄像头帧: 2430 (大小: 10983 bytes, 分辨率: 1024x600)
2025/06/07 12:24:48 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:24:48 [ASTIAV] 已捕获 2430 帧, 当前帧大小: 10998 bytes
2025/06/07 12:24:50 [MEDIA] AstiAV成功捕获摄像头帧: 2460 (大小: 11032 bytes, 分辨率: 1024x600)
2025/06/07 12:24:51 [ASTIAV] 已捕获 2460 帧, 当前帧大小: 10974 bytes
2025/06/07 12:24:52 [MEDIA] AstiAV成功捕获摄像头帧: 2490 (大小: 11081 bytes, 分辨率: 1024x600)
2025/06/07 12:24:53 [ASTIAV] 已捕获 2490 帧, 当前帧大小: 10974 bytes
2025/06/07 12:24:54 [MEDIA] AstiAV成功捕获摄像头帧: 2520 (大小: 11001 bytes, 分辨率: 1024x600)
2025/06/07 12:24:56 [ASTIAV] 已捕获 2520 帧, 当前帧大小: 10999 bytes
2025/06/07 12:24:57 [MEDIA] AstiAV成功捕获摄像头帧: 2550 (大小: 10968 bytes, 分辨率: 1024x600)
2025/06/07 12:24:58 [ASTIAV] 已捕获 2550 帧, 当前帧大小: 10986 bytes
2025/06/07 12:24:59 [MEDIA] AstiAV成功捕获摄像头帧: 2580 (大小: 10960 bytes, 分辨率: 1024x600)
2025/06/07 12:25:00 [ASTIAV] 已捕获 2580 帧, 当前帧大小: 10973 bytes
2025/06/07 12:25:01 [MEDIA] AstiAV成功捕获摄像头帧: 2610 (大小: 10958 bytes, 分辨率: 1024x600)
2025/06/07 12:25:01 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:25:03 [ASTIAV] 已捕获 2610 帧, 当前帧大小: 10962 bytes
2025/06/07 12:25:03 [MEDIA] AstiAV成功捕获摄像头帧: 2640 (大小: 10945 bytes, 分辨率: 1024x600)
2025/06/07 12:25:05 [ASTIAV] 已捕获 2640 帧, 当前帧大小: 10950 bytes
2025/06/07 12:25:06 [MEDIA] AstiAV成功捕获摄像头帧: 2670 (大小: 10966 bytes, 分辨率: 1024x600)
2025/06/07 12:25:07 [ASTIAV] 已捕获 2670 帧, 当前帧大小: 11002 bytes
2025/06/07 12:25:08 [MEDIA] AstiAV成功捕获摄像头帧: 2700 (大小: 11004 bytes, 分辨率: 1024x600)
2025/06/07 12:25:09 [ASTIAV] 已捕获 2700 帧, 当前帧大小: 10982 bytes
2025/06/07 12:25:10 [MEDIA] AstiAV成功捕获摄像头帧: 2730 (大小: 10961 bytes, 分辨率: 1024x600)
2025/06/07 12:25:10 [ASTIAV] 捕获帧失败: failed to read frame: Resource temporarily unavailable
2025/06/07 12:25:12 [ASTIAV] 已捕获 2730 帧, 当前帧大小: 10932 bytes
2025/06/07 12:25:13 [MEDIA] AstiAV成功捕获摄像头帧: 2760 (大小: 11049 bytes, 分辨率: 1024x600)
2025/06/07 12:25:15 [ASTIAV] 已捕获 2760 帧, 当前帧大小: 10764 bytes
2025/06/07 12:25:15 [MEDIA] AstiAV成功捕获摄像头帧: 2790 (大小: 10789 bytes, 分辨率: 1024x600)
2025/06/07 12:25:17 [ASTIAV] 已捕获 2790 帧, 当前帧大小: 10708 bytes
2025/06/07 12:25:18 [MEDIA] AstiAV成功捕获摄像头帧: 2820 (大小: 10804 bytes, 分辨率: 1024x600)
2025/06/07 12:25:19 收到信号: interrupt
2025/06/07 12:25:19 正在关闭应用程序...
2025/06/07 12:25:19 正在关闭视频通话对等端...
2025/06/07 12:25:19 [PEER] 开始关闭视频通话对等端...
2025/06/07 12:25:19 [PEER] 离开房间...
2025/06/07 12:25:19 [PEER] 离开房间
2025/06/07 12:25:19 [MEDIA] 停止媒体捕获
2025/06/07 12:25:19 [PLAYER] 停止媒体播放
2025/06/07 12:25:19 [SIGNALING] 断开信令连接
2025/06/07 12:25:19 [SIGNALING] WebSocket读取错误: read tcp *************:57655->52.81.131.193:8089: use of closed network connection
2025/06/07 12:25:19 [PEER] 已离开房间
2025/06/07 12:25:19 [PEER] 信令错误: read tcp *************:57655->52.81.131.193:8089: use of closed network connection
2025/06/07 12:25:19 [PEER] 关闭媒体捕获器...
2025/06/07 12:25:19 [ASTIAV] 停止摄像头捕获...
2025/06/07 12:25:19 [ASTIAV] 等待捕获循环退出...
2025/06/07 12:25:19 [PEER] 信令服务器断开连接: <nil>
2025/06/07 12:25:19 [ASTIAV] 开始清理AstiAV资源...
2025/06/07 12:25:19 [ASTIAV] 开始清理资源...
2025/06/07 12:25:19 [ASTIAV] 释放输入包...
2025/06/07 12:25:19 [ASTIAV] 释放解码帧...
2025/06/07 12:25:19 [ASTIAV] 释放缩放帧...
2025/06/07 12:25:19 [ASTIAV] 释放编码包...
2025/06/07 12:25:19 [ASTIAV] 释放缩放上下文...
2025/06/07 12:25:19 [ASTIAV] 释放编码器上下文...
2025/06/07 12:25:19 [ASTIAV] 释放解码器上下文...
2025/06/07 12:25:19 [ASTIAV] 关闭输入上下文...
2025/06/07 12:25:19 [ASTIAV] 捕获循环在错误处理中检测到停止，退出
2025/06/07 12:25:19 [ASTIAV] ✅ 资源清理完成
2025/06/07 12:25:19 [ASTIAV] ✅ 摄像头捕获已停止
2025/06/07 12:25:19 [MEDIA] 媒体捕获器已关闭
2025/06/07 12:25:19 [PEER] 关闭媒体播放器...
2025/06/07 12:25:19 [H264] 视频文件已保存: 0 bytes
2025/06/07 12:25:19 [H264] ffplay已关闭
2025/06/07 12:25:19 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/07 12:25:19 [JPEG] ffplay已关闭
2025/06/07 12:25:19 [AUDIO] 音频文件已保存: 539520 bytes
2025/06/07 12:25:19 [PLAYER] 媒体播放器已关闭
2025/06/07 12:25:19 [PEER] 关闭WebRTC管理器...
2025/06/07 12:25:19 [WEBRTC] 开始关闭WebRTC连接...
2025/06/07 12:25:19 [WEBRTC] 关闭PeerConnection...
2025/06/07 12:25:19 [PLAYER] 读取音频RTP包失败: EOF
2025/06/07 12:25:19 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/07 12:25:19 [WEBRTC] ICE连接状态变化: closed
2025/06/07 12:25:19 [WEBRTC] 连接状态变化: closed
2025/06/07 12:25:19 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/07 12:25:19 等待资源释放...
2025/06/07 12:25:19 [PLAYER] 音频轨道播放协程退出: 
2025/06/07 12:25:21 应用程序已安全关闭
2025/06/07 12:25:21 [PEER] 开始关闭视频通话对等端...
2025/06/07 12:25:21 [PEER] 离开房间...
2025/06/07 12:25:21 [PEER] 离开房间
2025/06/07 12:25:21 [PEER] 已离开房间
2025/06/07 12:25:21 [PEER] 关闭媒体捕获器...
2025/06/07 12:25:21 [MEDIA] 媒体捕获器已关闭
2025/06/07 12:25:21 [PEER] 关闭媒体播放器...
2025/06/07 12:25:21 [H264] 视频文件已保存: 0 bytes
2025/06/07 12:25:21 [H264] ffplay已关闭
2025/06/07 12:25:21 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/07 12:25:21 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/07 12:25:21 [JPEG] ffplay已关闭
2025/06/07 12:25:21 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/07 12:25:21 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/07 12:25:21 [PLAYER] 媒体播放器已关闭
2025/06/07 12:25:21 [PEER] 关闭WebRTC管理器...
2025/06/07 12:25:21 [WEBRTC] 开始关闭WebRTC连接...
2025/06/07 12:25:21 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/07 12:25:21 [PEER] ✅ 视频通话对等端已完全关闭
