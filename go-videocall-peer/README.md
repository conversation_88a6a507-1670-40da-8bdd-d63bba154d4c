# Go VideoCall Peer

一个基于Go语言的点对点视频通话应用，专门设计用于与ESP32 videocall_demo设备进行双向视频通话。

## 功能特性

### 🎥 双向视频通话
- 与ESP32 videocall_demo设备建立点对点视频连接
- 支持H.264视频编码和PCMA音频编码
- 实时双向音视频传输

### 📡 信令协议兼容
- 完全兼容ESP32 videocall_demo的信令协议
- 支持RING、ACCEPT_CALL、DENY_CALL等自定义命令
- 使用AppRTC信令服务器进行连接协商

### 🎬 媒体处理
- 使用AstiAV（FFmpeg集成）进行真实摄像头捕获
- 支持多种视频格式和分辨率
- 远程视频流接收和本地播放

### 🔧 高级功能
- WebRTC数据通道支持
- 手动连接控制
- 实时连接状态监控
- 媒体质量统计

## 快速开始

### 环境要求
- Go 1.19+
- FFmpeg 4.0+
- 摄像头和麦克风设备

### 安装依赖
```bash
cd go-videocall-peer
go mod tidy
```

### 基本使用
```bash
# 启动应用
go run .

# 连接到房间（与ESP32设备使用相同房间ID）
connect myroom123

# 发起呼叫
call

# 接听来电
accept

# 挂断通话
hangup

# 查看状态
status

# 退出
quit
```

## 项目架构

```
go-videocall-peer/
├── main.go                 # 主程序入口
├── config/
│   ├── config.go          # 配置管理
│   └── default.yaml       # 默认配置
├── internal/
│   ├── videocall/         # 视频通话核心
│   │   ├── peer.go        # 对等连接管理
│   │   ├── signaling.go   # 信令处理
│   │   └── state.go       # 状态管理
│   ├── media/             # 媒体处理
│   │   ├── capture.go     # 媒体捕获
│   │   ├── player.go      # 媒体播放
│   │   └── astiav.go      # AstiAV集成
│   ├── webrtc/            # WebRTC封装
│   │   ├── connection.go  # 连接管理
│   │   └── codec.go       # 编解码器配置
│   └── cli/               # 命令行界面
│       └── commands.go    # 命令处理
├── pkg/
│   └── utils/             # 工具函数
└── docs/                  # 文档
```

## 与ESP32设备通话流程

1. **设备准备**
   - ESP32设备运行videocall_demo固件
   - Go应用启动并连接到相同的AppRTC服务器

2. **加入房间**
   - 两个设备使用相同的房间ID加入房间
   - 建立WebSocket信令连接

3. **发起呼叫**
   - 任一设备发送RING命令
   - 对方设备收到呼叫通知

4. **建立连接**
   - 被叫方发送ACCEPT_CALL响应
   - 开始WebRTC连接协商
   - 交换SDP offer/answer和ICE候选

5. **媒体传输**
   - 建立双向媒体流
   - 开始音视频数据传输

6. **通话管理**
   - 支持通话中的媒体控制
   - 处理网络异常和重连
   - 正常挂断流程

## 配置说明

### 视频配置
- 分辨率：640x480（默认）
- 帧率：15fps
- 编码：H.264
- 码率：自适应

### 音频配置
- 采样率：8000Hz
- 声道：单声道
- 编码：PCMA (G.711 A-law)

### 网络配置
- 信令服务器：AppRTC兼容
- ICE服务器：支持STUN/TURN
- 数据通道：启用

## 故障排除

### 常见问题
1. **摄像头无法访问**
   - 检查设备权限
   - 确认FFmpeg安装正确

2. **连接失败**
   - 检查网络连接
   - 确认房间ID正确
   - 验证信令服务器可达

3. **音视频质量问题**
   - 调整编码参数
   - 检查网络带宽
   - 优化ICE配置

### 调试模式
```bash
# 启用详细日志
go run . --debug

# 启用媒体调试
go run . --media-debug

# 保存调试数据
go run . --save-debug
```

## 开发说明

### 扩展功能
- 添加新的信令命令
- 支持更多编解码器
- 实现群组通话
- 添加录制功能

### 测试
```bash
# 运行单元测试
go test ./...

# 运行集成测试
go test -tags=integration ./...
```

## 许可证

MIT License
