# Go VideoCall Peer 默认配置

# 应用程序配置
app:
  name: "go-videocall-peer"
  version: "1.0.0"
  debug: false

# 信令服务器配置
signaling:
  # AppRTC兼容服务器
  server_url: "https://webrtc.espressif.cn"
  # WebSocket超时设置
  connect_timeout: 30s
  message_timeout: 10s
  heartbeat_interval: 30s

# WebRTC配置
webrtc:
  # ICE服务器配置
  ice_servers:
    - urls: ["stun:stun.l.google.com:19302"]
    - urls: ["stun:stun1.l.google.com:19302"]
    # 如果需要TURN服务器，取消注释并配置
    # - urls: ["turn:your-turn-server:3478"]
    #   username: "your-username"
    #   credential: "your-password"
  
  # 连接配置
  ice_candidate_timeout: 10s
  connection_timeout: 30s
  
  # 数据通道配置
  enable_data_channel: true
  video_over_data_channel: true
  data_channel_buffer_size: 409600  # 400KB

# 视频配置
video:
  # 捕获设备
  device_id: "0"  # 默认摄像头
  input_format: "avfoundation"  # macOS: avfoundation, Linux: v4l2, Windows: dshow
  
  # 视频参数 - 匹配ESP32P4 LCD分辨率
  width: 1024
  height: 600
  fps: 15
  bitrate: 800000  # 800kbps (增加码率以适应更高分辨率)
  
  # 编码配置
  codec: "h264"
  profile: "baseline"
  level: "3.1"
  pixel_format: "yuv420p"
  
  # H.264特定配置
  h264:
    packetization_mode: 1
    profile_level_id: "4d001f"
    level_asymmetry_allowed: true

# 音频配置
audio:
  # 捕获设备
  device_id: ""  # 默认麦克风
  input_format: "avfoundation"  # macOS: avfoundation, Linux: alsa, Windows: dshow
  
  # 音频参数
  sample_rate: 8000
  channels: 1
  bitrate: 64000  # 64kbps
  
  # 编码配置
  codec: "pcma"  # G.711 A-law
  frame_size: 160  # 20ms at 8kHz

# 播放配置
playback:
  # 视频播放
  video:
    enable: true
    renderer: "auto"  # auto, sdl2, console, ffplay, save
    window_title: "VideoCall Peer - Remote Video"

    # 视频保存配置
    save_to_file: true
    save_path: "debug/received_video.h264"

    # ffplay播放配置
    use_ffplay: true
    ffplay_path: "ffplay"  # ffplay可执行文件路径
    ffplay_args: ["-f", "h264", "-framerate", "15", "-"]

  # 音频播放
  audio:
    enable: true
    device: ""  # 默认扬声器
    buffer_size: 1024

    # 音频保存配置
    save_to_file: true
    save_path: "debug/received_audio.pcma"

# 通话配置
call:
  # 超时设置
  ring_timeout: 30s
  connect_timeout: 60s
  
  # 重试配置
  max_retries: 3
  retry_interval: 5s
  
  # 质量监控
  quality_check_interval: 5s
  min_quality_threshold: 0.5

# 日志配置
logging:
  level: "info"  # debug, info, warn, error
  format: "text"  # text, json
  output: "console"  # console, file
  file_path: "logs/videocall.log"
  
  # 组件日志级别
  components:
    webrtc: "info"
    media: "info"
    signaling: "info"
    astiav: "warn"

# 调试配置
debug:
  # 媒体调试
  save_frames: false
  frame_save_path: "debug/frames"
  save_audio: false
  audio_save_path: "debug/audio"
  
  # 网络调试
  save_packets: false
  packet_save_path: "debug/packets"
  
  # 统计信息
  enable_stats: true
  stats_interval: 5s
  stats_save_path: "debug/stats"

# 性能配置
performance:
  # 线程配置
  max_goroutines: 100
  worker_pool_size: 10
  
  # 内存配置
  max_memory_mb: 512
  gc_percent: 100
  
  # 缓冲区配置
  video_buffer_size: 10
  audio_buffer_size: 20
  network_buffer_size: 1024
