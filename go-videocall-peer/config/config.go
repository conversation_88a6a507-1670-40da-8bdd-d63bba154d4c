package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 应用程序配置结构
type Config struct {
	App         AppConfig         `yaml:"app"`
	Signaling   SignalingConfig   `yaml:"signaling"`
	WebRTC      WebRTCConfig      `yaml:"webrtc"`
	Video       VideoConfig       `yaml:"video"`
	Audio       AudioConfig       `yaml:"audio"`
	Playback    PlaybackConfig    `yaml:"playback"`
	Call        CallConfig        `yaml:"call"`
	Logging     LoggingConfig     `yaml:"logging"`
	Debug       DebugConfig       `yaml:"debug"`
	Performance PerformanceConfig `yaml:"performance"`
}

// AppConfig 应用程序基本配置
type AppConfig struct {
	Name    string `yaml:"name"`
	Version string `yaml:"version"`
	Debug   bool   `yaml:"debug"`
}

// SignalingConfig 信令服务器配置
type SignalingConfig struct {
	ServerURL         string        `yaml:"server_url"`
	ConnectTimeout    time.Duration `yaml:"connect_timeout"`
	MessageTimeout    time.Duration `yaml:"message_timeout"`
	HeartbeatInterval time.Duration `yaml:"heartbeat_interval"`
}

// WebRTCConfig WebRTC配置
type WebRTCConfig struct {
	ICEServers            []ICEServerConfig `yaml:"ice_servers"`
	ICECandidateTimeout   time.Duration     `yaml:"ice_candidate_timeout"`
	ConnectionTimeout     time.Duration     `yaml:"connection_timeout"`
	EnableDataChannel     bool              `yaml:"enable_data_channel"`
	VideoOverDataChannel  bool              `yaml:"video_over_data_channel"`
	DataChannelBufferSize int               `yaml:"data_channel_buffer_size"`
}

// ICEServerConfig ICE服务器配置
type ICEServerConfig struct {
	URLs       []string `yaml:"urls"`
	Username   string   `yaml:"username,omitempty"`
	Credential string   `yaml:"credential,omitempty"`
}

// VideoConfig 视频配置
type VideoConfig struct {
	DeviceID    string     `yaml:"device_id"`
	InputFormat string     `yaml:"input_format"`
	Width       int        `yaml:"width"`
	Height      int        `yaml:"height"`
	FPS         int        `yaml:"fps"`
	Bitrate     int        `yaml:"bitrate"`
	Codec       string     `yaml:"codec"`
	Profile     string     `yaml:"profile"`
	Level       string     `yaml:"level"`
	PixelFormat string     `yaml:"pixel_format"`
	H264        H264Config `yaml:"h264"`
}

// H264Config H.264编码器配置
type H264Config struct {
	PacketizationMode     int    `yaml:"packetization_mode"`
	ProfileLevelID        string `yaml:"profile_level_id"`
	LevelAsymmetryAllowed bool   `yaml:"level_asymmetry_allowed"`
}

// AudioConfig 音频配置
type AudioConfig struct {
	DeviceID    string `yaml:"device_id"`
	InputFormat string `yaml:"input_format"`
	SampleRate  int    `yaml:"sample_rate"`
	Channels    int    `yaml:"channels"`
	Bitrate     int    `yaml:"bitrate"`
	Codec       string `yaml:"codec"`
	FrameSize   int    `yaml:"frame_size"`
}

// PlaybackConfig 播放配置
type PlaybackConfig struct {
	Video VideoPlaybackConfig `yaml:"video"`
	Audio AudioPlaybackConfig `yaml:"audio"`
}

// VideoPlaybackConfig 视频播放配置
type VideoPlaybackConfig struct {
	Enable      bool     `yaml:"enable"`
	Renderer    string   `yaml:"renderer"`
	WindowTitle string   `yaml:"window_title"`
	SaveToFile  bool     `yaml:"save_to_file"`
	SavePath    string   `yaml:"save_path"`
	UseFFplay   bool     `yaml:"use_ffplay"`
	FFplayPath  string   `yaml:"ffplay_path"`
	FFplayArgs  []string `yaml:"ffplay_args"`
}

// AudioPlaybackConfig 音频播放配置
type AudioPlaybackConfig struct {
	Enable     bool   `yaml:"enable"`
	Device     string `yaml:"device"`
	BufferSize int    `yaml:"buffer_size"`
	SaveToFile bool   `yaml:"save_to_file"`
	SavePath   string `yaml:"save_path"`
}

// CallConfig 通话配置
type CallConfig struct {
	RingTimeout          time.Duration `yaml:"ring_timeout"`
	ConnectTimeout       time.Duration `yaml:"connect_timeout"`
	MaxRetries           int           `yaml:"max_retries"`
	RetryInterval        time.Duration `yaml:"retry_interval"`
	QualityCheckInterval time.Duration `yaml:"quality_check_interval"`
	MinQualityThreshold  float64       `yaml:"min_quality_threshold"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string            `yaml:"level"`
	Format     string            `yaml:"format"`
	Output     string            `yaml:"output"`
	FilePath   string            `yaml:"file_path"`
	Components map[string]string `yaml:"components"`
}

// DebugConfig 调试配置
type DebugConfig struct {
	SaveFrames     bool          `yaml:"save_frames"`
	FrameSavePath  string        `yaml:"frame_save_path"`
	SaveAudio      bool          `yaml:"save_audio"`
	AudioSavePath  string        `yaml:"audio_save_path"`
	SavePackets    bool          `yaml:"save_packets"`
	PacketSavePath string        `yaml:"packet_save_path"`
	EnableStats    bool          `yaml:"enable_stats"`
	StatsInterval  time.Duration `yaml:"stats_interval"`
	StatsSavePath  string        `yaml:"stats_save_path"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	MaxGoroutines     int `yaml:"max_goroutines"`
	WorkerPoolSize    int `yaml:"worker_pool_size"`
	MaxMemoryMB       int `yaml:"max_memory_mb"`
	GCPercent         int `yaml:"gc_percent"`
	VideoBufferSize   int `yaml:"video_buffer_size"`
	AudioBufferSize   int `yaml:"audio_buffer_size"`
	NetworkBufferSize int `yaml:"network_buffer_size"`
}

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	// 如果没有指定配置文件，使用默认配置
	if configPath == "" {
		configPath = "config/default.yaml"
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML配置
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	return &config, nil
}

// Validate 验证配置的有效性
func (c *Config) Validate() error {
	// 验证信令服务器配置
	if c.Signaling.ServerURL == "" {
		return fmt.Errorf("信令服务器URL不能为空")
	}

	// 验证视频配置
	if c.Video.Width <= 0 || c.Video.Height <= 0 {
		return fmt.Errorf("视频分辨率必须大于0")
	}
	if c.Video.FPS <= 0 {
		return fmt.Errorf("视频帧率必须大于0")
	}

	// 验证音频配置
	if c.Audio.SampleRate <= 0 {
		return fmt.Errorf("音频采样率必须大于0")
	}
	if c.Audio.Channels <= 0 {
		return fmt.Errorf("音频声道数必须大于0")
	}

	return nil
}

// CreateDebugDirs 创建调试目录
func (c *Config) CreateDebugDirs() error {
	dirs := []string{
		c.Debug.FrameSavePath,
		c.Debug.AudioSavePath,
		c.Debug.PacketSavePath,
		filepath.Dir(c.Debug.StatsSavePath),
		filepath.Dir(c.Logging.FilePath),
		filepath.Dir(c.Playback.Video.SavePath),
		filepath.Dir(c.Playback.Audio.SavePath),
	}

	for _, dir := range dirs {
		if dir != "" {
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("创建目录失败 %s: %v", dir, err)
			}
		}
	}

	return nil
}

// GetLogLevel 获取组件日志级别
func (c *Config) GetLogLevel(component string) string {
	if level, exists := c.Logging.Components[component]; exists {
		return level
	}
	return c.Logging.Level
}
