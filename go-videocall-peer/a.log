=== Go VideoCall Peer v1.0.0 ===
与ESP32 videocall_demo设备进行双向视频通话

2025/06/06 21:33:37 启动 Go VideoCall Peer v1.0.0
2025/06/06 21:33:37 配置文件: config/default.yaml
2025/06/06 21:33:37 调试模式: false
2025/06/06 21:33:37 [H264] 视频将保存到: debug/received_video.h264
2025/06/06 21:33:37 [H264] ffplay已启动: ffplay [-f h264 -framerate 15 -]
2025/06/06 21:33:37 [JPEG] JPEG图像将保存到: debug/received_video.mjpeg
2025/06/06 21:33:37 [JPEG] ffplay已启动播放MJPEG流: ffplay [-f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe -window_title ESP32 Camera - MJPEG Stream]
2025/06/06 21:33:37 [AUDIO] 音频将保存到: debug/received_audio.pcma
2025/06/06 21:33:37 [PEER] 初始化视频通话对等端
2025/06/06 21:33:37 [MEDIA] 初始化媒体捕获器
2025/06/06 21:33:37 [MEDIA] 媒体捕获器初始化完成
2025/06/06 21:33:37 [WEBRTC] 数据通道创建成功
2025/06/06 21:33:37 [WEBRTC] PeerConnection创建成功
2025/06/06 21:33:37 [PEER] 视频通话对等端初始化完成
2025/06/06 21:33:37 视频通话对等端初始化完成
=== Go VideoCall Peer ===
与ESP32 videocall_demo设备进行双向视频通话
输入 'help' 查看可用命令

videocall> 2025/06/06 21:33:41 收到信号: interrupt
2025/06/06 21:33:41 正在关闭应用程序...
2025/06/06 21:33:41 正在关闭视频通话对等端...
2025/06/06 21:33:41 [PEER] 开始关闭视频通话对等端...
2025/06/06 21:33:41 [PEER] 离开房间...
2025/06/06 21:33:41 [PEER] 离开房间
2025/06/06 21:33:41 [PEER] 已离开房间
2025/06/06 21:33:41 [PEER] 关闭媒体捕获器...
2025/06/06 21:33:41 [MEDIA] 媒体捕获器已关闭
2025/06/06 21:33:41 [PEER] 关闭媒体播放器...
2025/06/06 21:33:41 [H264] 视频文件已保存: 0 bytes
2025/06/06 21:33:41 [H264] ffplay已关闭
2025/06/06 21:33:41 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 21:33:41 [JPEG] ffplay已关闭
2025/06/06 21:33:41 [AUDIO] 音频文件已保存: 0 bytes
2025/06/06 21:33:41 [PLAYER] 媒体播放器已关闭
2025/06/06 21:33:41 [PEER] 关闭WebRTC管理器...
2025/06/06 21:33:41 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 21:33:41 [WEBRTC] 关闭数据通道...
2025/06/06 21:33:41 [WEBRTC] 关闭PeerConnection...
2025/06/06 21:33:41 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 21:33:41 [PEER] ✅ 视频通话对等端已完全关闭
2025/06/06 21:33:41 等待资源释放...
2025/06/06 21:33:41 [WEBRTC] 连接状态变化: closed
2025/06/06 21:33:43 应用程序已安全关闭
2025/06/06 21:33:43 [PEER] 开始关闭视频通话对等端...
2025/06/06 21:33:43 [PEER] 离开房间...
2025/06/06 21:33:43 [PEER] 离开房间
2025/06/06 21:33:43 [PEER] 已离开房间
2025/06/06 21:33:43 [PEER] 关闭媒体捕获器...
2025/06/06 21:33:43 [MEDIA] 媒体捕获器已关闭
2025/06/06 21:33:43 [PEER] 关闭媒体播放器...
2025/06/06 21:33:43 [H264] 视频文件已保存: 0 bytes
2025/06/06 21:33:43 [H264] ffplay已关闭
2025/06/06 21:33:43 [PLAYER] 关闭H.264处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.h264: file already closed]
2025/06/06 21:33:43 [JPEG] JPEG文件已保存: 0 帧, 0 bytes
2025/06/06 21:33:43 [JPEG] ffplay已关闭
2025/06/06 21:33:43 [PLAYER] 关闭JPEG处理器失败: 关闭时发生错误: [关闭保存文件失败: close debug/received_video.mjpeg: file already closed]
2025/06/06 21:33:43 [PLAYER] 关闭音频处理器失败: 关闭音频保存文件失败: close debug/received_audio.pcma: file already closed
2025/06/06 21:33:43 [PLAYER] 媒体播放器已关闭
2025/06/06 21:33:43 [PEER] 关闭WebRTC管理器...
2025/06/06 21:33:43 [WEBRTC] 开始关闭WebRTC连接...
2025/06/06 21:33:43 [WEBRTC] ✅ WebRTC连接已完全关闭
2025/06/06 21:33:43 [PEER] ✅ 视频通话对等端已完全关闭
