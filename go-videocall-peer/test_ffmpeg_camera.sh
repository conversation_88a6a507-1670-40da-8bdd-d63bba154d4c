#!/bin/bash

# 测试FFmpeg摄像头捕获功能

echo "=== FFmpeg摄像头捕获测试 ==="
echo

# 检查FFmpeg是否安装
if ! command -v ffmpeg >/dev/null 2>&1; then
    echo "❌ FFmpeg未安装，请先安装FFmpeg:"
    echo "   macOS: brew install ffmpeg"
    echo "   Ubuntu: sudo apt install ffmpeg"
    echo "   Windows: 下载FFmpeg并添加到PATH"
    exit 1
fi

echo "✅ FFmpeg已安装: $(ffmpeg -version | head -1)"
echo

# 检查摄像头设备
echo "📹 检查可用的摄像头设备:"
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "macOS系统，使用avfoundation驱动"
    ffmpeg -f avfoundation -list_devices true -i "" 2>&1 | grep -E "\[AVFoundation\].*video"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo "Linux系统，使用v4l2驱动"
    if command -v v4l2-ctl >/dev/null 2>&1; then
        v4l2-ctl --list-devices
    else
        ls /dev/video* 2>/dev/null || echo "未找到摄像头设备"
    fi
else
    echo "Windows系统，使用dshow驱动"
    ffmpeg -f dshow -list_devices true -i dummy 2>&1 | grep -E "DirectShow.*video"
fi

echo
echo "🧪 测试FFmpeg单帧捕获:"

# 测试捕获单帧
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    ffmpeg -f avfoundation -i "0" -vframes 1 -f mjpeg -q:v 2 -s 320x240 -pix_fmt yuvj420p -y test_frame.jpg -loglevel error
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    ffmpeg -f v4l2 -i "/dev/video0" -vframes 1 -f mjpeg -q:v 2 -s 320x240 -pix_fmt yuvj420p -y test_frame.jpg -loglevel error
else
    # Windows (需要根据实际设备名调整)
    ffmpeg -f dshow -i "video=USB2.0 Camera" -vframes 1 -f mjpeg -q:v 2 -s 320x240 -pix_fmt yuvj420p -y test_frame.jpg -loglevel error
fi

if [ -f "test_frame.jpg" ]; then
    size=$(stat -f%z "test_frame.jpg" 2>/dev/null || stat -c%s "test_frame.jpg" 2>/dev/null)
    echo "✅ 成功捕获测试帧: test_frame.jpg (${size} bytes)"
    
    # 检查JPEG头部
    if command -v hexdump >/dev/null 2>&1; then
        echo "📸 JPEG头部信息:"
        hexdump -C test_frame.jpg | head -2
    fi
    
    echo "🖼️  可以用以下命令查看图片:"
    echo "   open test_frame.jpg  (macOS)"
    echo "   xdg-open test_frame.jpg  (Linux)"
    echo "   start test_frame.jpg  (Windows)"
else
    echo "❌ 摄像头捕获失败，可能原因:"
    echo "   1. 摄像头被其他程序占用"
    echo "   2. 摄像头权限问题"
    echo "   3. 摄像头设备名不正确"
    echo "   4. FFmpeg驱动不兼容"
    exit 1
fi

echo
echo "🚀 启动Go程序进行实时摄像头测试..."
echo "请按以下步骤操作："
echo
echo "1. 程序启动后，输入: join d0002"
echo "2. 启用监控: monitor"
echo "3. 在ESP32上加入房间: join d0002"
echo "4. 在ESP32上发起呼叫: b"
echo "5. 在程序中接受呼叫: accept"
echo "6. 观察以下关键日志:"
echo "   - [MEDIA] FFmpeg捕获真实JPEG帧: 30 (大小: xxxx bytes)"
echo "   - [MEDIA] 已通过数据通道发送JPEG帧"
echo "   - [WEBRTC] 连接状态变化: connected"
echo
echo "7. 检查ESP32端:"
echo "   - 应该不再报错: your jpg is a gray style picture"
echo "   - 应该显示: Recv V:[非0:非0]"
echo "   - 应该能看到真实的摄像头画面"
echo
echo "预期效果:"
echo "✅ 使用真实摄像头数据而不是测试数据"
echo "✅ ESP32能正确解码真实JPEG图像"
echo "✅ 双向视频通话显示真实画面"
echo "✅ 完整的视频通话功能"
echo

# 清理之前的调试文件
rm -f debug/received_video.h264 debug/received_video.mjpeg debug/received_audio.pcma debug/monitor.log

echo "✅ 已清理调试文件"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 清理测试文件
rm -f test_frame.jpg

# 检查结果
echo "检查测试结果:"

if [ -f "debug/received_video.mjpeg" ]; then
    size=$(stat -f%z "debug/received_video.mjpeg" 2>/dev/null || stat -c%s "debug/received_video.mjpeg" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 接收MJPEG文件: ${size} bytes (ESP32真实摄像头数据)"
        echo "🎬 播放命令:"
        echo "   ffplay -f mjpeg debug/received_video.mjpeg"
    else
        echo "❌ 接收MJPEG文件: 0 bytes"
    fi
else
    echo "❌ 接收MJPEG文件: 不存在"
fi

echo
echo "FFmpeg摄像头捕获说明:"
echo "- 使用FFmpeg直接从摄像头捕获真实图像"
echo "- 输出标准MJPEG格式，兼容ESP32"
echo "- 320x240分辨率，适合网络传输"
echo "- yuvj420p像素格式，ESP32兼容"
echo "- 高质量JPEG编码 (q:v 2)"
echo
echo "如果仍有问题，请检查:"
echo "1. 摄像头权限和可用性"
echo "2. FFmpeg版本和驱动支持"
echo "3. ESP32的JPEG解码器配置"
echo "4. 网络传输稳定性"
