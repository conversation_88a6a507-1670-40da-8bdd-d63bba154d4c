#!/bin/bash

# 测试FFmpeg测试图像生成功能

echo "=== FFmpeg测试图像生成测试 ==="
echo

# 检查FFmpeg是否安装
if ! command -v ffmpeg >/dev/null 2>&1; then
    echo "❌ FFmpeg未安装，请先安装FFmpeg:"
    echo "   macOS: brew install ffmpeg"
    echo "   Ubuntu: sudo apt install ffmpeg"
    echo "   Windows: 下载FFmpeg并添加到PATH"
    exit 1
fi

echo "✅ FFmpeg已安装: $(ffmpeg -version | head -1)"
echo

# 测试FFmpeg testsrc2滤镜
echo "🧪 测试FFmpeg testsrc2滤镜:"
ffmpeg -f lavfi -i "testsrc2=size=320x240:rate=1:duration=1" -vframes 1 -f mjpeg -q:v 5 -pix_fmt yuvj420p -y test_testsrc.jpg -loglevel error

if [ -f "test_testsrc.jpg" ]; then
    size=$(stat -f%z "test_testsrc.jpg" 2>/dev/null || stat -c%s "test_testsrc.jpg" 2>/dev/null)
    echo "✅ 成功生成测试图像: test_testsrc.jpg (${size} bytes)"
    
    # 检查JPEG头部
    if command -v hexdump >/dev/null 2>&1; then
        echo "📸 JPEG头部信息:"
        hexdump -C test_testsrc.jpg | head -2
    fi
    
    echo "🖼️  可以用以下命令查看图片:"
    echo "   open test_testsrc.jpg  (macOS)"
    echo "   xdg-open test_testsrc.jpg  (Linux)"
    echo "   start test_testsrc.jpg  (Windows)"
else
    echo "❌ FFmpeg测试图像生成失败"
    exit 1
fi

echo
echo "🎨 测试动态颜色变化:"

# 生成几个不同颜色的测试图像
for i in {0..3}; do
    hue=$((i * 90))
    saturation=$((50 + i * 10))
    brightness=$((50 + i * 5))
    
    echo "生成图像 $((i+1)): 色调=${hue}, 饱和度=${saturation}, 亮度=${brightness}"
    ffmpeg -f lavfi -i "testsrc2=size=320x240:rate=1:duration=1" \
           -vf "hue=h=${hue}:s=${saturation}:b=${brightness}" \
           -vframes 1 -f mjpeg -q:v 5 -pix_fmt yuvj420p \
           -y "test_color_${i}.jpg" -loglevel error
    
    if [ -f "test_color_${i}.jpg" ]; then
        size=$(stat -f%z "test_color_${i}.jpg" 2>/dev/null || stat -c%s "test_color_${i}.jpg" 2>/dev/null)
        echo "  ✅ test_color_${i}.jpg (${size} bytes)"
    else
        echo "  ❌ 生成失败"
    fi
done

echo
echo "🚀 启动Go程序进行FFmpeg测试图像传输测试..."
echo "请按以下步骤操作："
echo
echo "1. 程序启动后，输入: join d0002"
echo "2. 启用监控: monitor"
echo "3. 在ESP32上加入房间: join d0002"
echo "4. 在ESP32上发起呼叫: b"
echo "5. 在程序中接受呼叫: accept"
echo "6. 观察以下关键日志:"
echo "   - [MEDIA] FFmpeg生成彩色测试图像: 30 (大小: xxxx bytes, 色调: xxx)"
echo "   - [MEDIA] 已通过数据通道发送JPEG帧"
echo "   - [WEBRTC] 连接状态变化: connected"
echo
echo "7. 检查ESP32端:"
echo "   - 应该不再报错: your jpg is a gray style picture"
echo "   - 应该显示: Recv V:[非0:非0]"
echo "   - 应该能看到彩色的测试图案"
echo
echo "预期效果:"
echo "✅ 使用FFmpeg生成的标准彩色JPEG"
echo "✅ ESP32能正确解码彩色图像"
echo "✅ 动态变化的彩色测试图案"
echo "✅ 避免摄像头权限问题"
echo "✅ 完整的视频通话功能测试"
echo

# 清理之前的调试文件
rm -f debug/received_video.h264 debug/received_video.mjpeg debug/received_audio.pcma debug/monitor.log

echo "✅ 已清理调试文件"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 清理测试文件
rm -f test_testsrc.jpg test_color_*.jpg

# 检查结果
echo "检查测试结果:"

if [ -f "debug/received_video.mjpeg" ]; then
    size=$(stat -f%z "debug/received_video.mjpeg" 2>/dev/null || stat -c%s "debug/received_video.mjpeg" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 接收MJPEG文件: ${size} bytes (ESP32摄像头数据)"
        echo "🎬 播放命令:"
        echo "   ffplay -f mjpeg debug/received_video.mjpeg"
        
        # 检查ESP32发送的JPEG头部
        if command -v hexdump >/dev/null 2>&1; then
            echo "📸 ESP32发送的JPEG头部:"
            hexdump -C debug/received_video.mjpeg | head -2
        fi
    else
        echo "❌ 接收MJPEG文件: 0 bytes"
    fi
else
    echo "❌ 接收MJPEG文件: 不存在"
fi

if [ -f "debug/received_audio.pcma" ]; then
    size=$(stat -f%z "debug/received_audio.pcma" 2>/dev/null || stat -c%s "debug/received_audio.pcma" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 音频文件: ${size} bytes (双向音频正常)"
        echo "🎵 播放命令:"
        echo "   ffplay -f alaw -ar 8000 -ac 1 debug/received_audio.pcma"
    else
        echo "❌ 音频文件: 0 bytes"
    fi
else
    echo "❌ 音频文件: 不存在"
fi

echo
echo "FFmpeg测试图像说明:"
echo "- 使用FFmpeg的testsrc2滤镜生成彩色测试图案"
echo "- 动态变化的色调、饱和度、亮度"
echo "- 标准MJPEG格式，ESP32完全兼容"
echo "- 320x240分辨率，适合网络传输"
echo "- yuvj420p像素格式，ESP32支持"
echo "- 避免摄像头权限和设备问题"
echo
echo "优势:"
echo "✅ 无需摄像头权限"
echo "✅ 跨平台兼容"
echo "✅ 标准JPEG格式"
echo "✅ 动态视觉效果"
echo "✅ 调试友好"
echo
echo "如果仍有问题，请检查:"
echo "1. FFmpeg版本是否支持testsrc2滤镜"
echo "2. ESP32的JPEG解码器配置"
echo "3. 网络传输稳定性"
echo "4. WebRTC连接状态"
