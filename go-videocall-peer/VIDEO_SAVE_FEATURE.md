# 视频保存和播放功能

## 功能概述

新增了接收到的视频和音频流的保存和实时播放功能，帮助调试WebRTC通话问题。

## 主要功能

### 1. H.264视频流保存
- 自动解析RTP包中的H.264数据
- 重组NAL单元（支持STAP-A和FU-A分片）
- 保存为标准H.264文件格式
- 支持SPS、PPS、IDR和P帧的正确处理

### 2. 实时视频播放
- 使用ffplay实时播放接收到的视频流
- 支持自定义ffplay参数
- 自动处理视频流管道

### 3. PCMA音频流保存
- 保存G.711 A-law编码的音频数据
- 支持8kHz单声道音频

### 4. 详细的调试日志
- 关键帧检测和统计
- 包序列号检查
- 数据传输统计

## 配置说明

在`config/default.yaml`中的相关配置：

```yaml
playback:
  video:
    enable: true
    renderer: "auto"
    
    # 视频保存配置
    save_to_file: true
    save_path: "debug/received_video.h264"
    
    # ffplay播放配置
    use_ffplay: true
    ffplay_path: "ffplay"
    ffplay_args: ["-f", "h264", "-framerate", "15", "-"]
    
  audio:
    enable: true
    
    # 音频保存配置
    save_to_file: true
    save_path: "debug/received_audio.pcma"
```

## 使用方法

### 1. 启动程序
```bash
./videocall-peer
```

### 2. 建立通话
```
join abc0002    # 加入房间
call           # 发起呼叫（或等待来电）
```

### 3. 观察输出
程序会显示类似以下的日志：
```
[H264] 视频将保存到: debug/received_video.h264
[H264] ffplay已启动: ffplay ["-f", "h264", "-framerate", "15", "-"]
[AUDIO] 音频将保存到: debug/received_audio.pcma
[H264] 处理SPS帧, 大小: 12 bytes
[H264] 处理PPS帧, 大小: 8 bytes
[H264] 处理IDR帧, 大小: 24 bytes
```

### 4. 播放保存的文件

#### 视频播放
```bash
# 使用ffplay播放
ffplay -f h264 -framerate 15 debug/received_video.h264

# 使用VLC播放
vlc debug/received_video.h264

# 转换为MP4格式
ffmpeg -f h264 -i debug/received_video.h264 -c copy output.mp4
```

#### 音频播放
```bash
# 使用ffplay播放PCMA音频
ffplay -f alaw -ar 8000 -ac 1 debug/received_audio.pcma

# 转换为WAV格式
ffmpeg -f alaw -ar 8000 -ac 1 -i debug/received_audio.pcma output.wav
```

## 调试功能

### 1. 视频流分析
- 检查是否接收到SPS/PPS参数集
- 验证IDR关键帧的接收
- 监控包序列号的连续性

### 2. 统计信息
```bash
# 程序运行时会显示统计信息
[H264] 已处理包: 1000, 帧: 50, 字节: 125000
[AUDIO] 已保存音频包: 2000, 总字节: 320000
```

### 3. 文件验证
```bash
# 检查H.264文件是否有效
ffprobe debug/received_video.h264

# 检查音频文件大小
ls -la debug/received_audio.pcma
```

## 故障排除

### 1. ffplay无法启动
- 确保系统已安装FFmpeg
- 检查ffplay_path配置是否正确
- 查看错误日志

### 2. 视频文件无法播放
- 检查是否接收到SPS/PPS参数集
- 验证H.264数据的完整性
- 尝试不同的播放器

### 3. 音频文件无声音
- 确认音频编码格式为PCMA (G.711 A-law)
- 检查采样率设置（8kHz）
- 验证文件大小是否合理

## 技术细节

### H.264 RTP解析
- 支持单个NAL单元 (Type 1, 5, 7, 8)
- 支持STAP-A聚合包 (Type 24)
- 支持FU-A分片单元 (Type 28)
- 自动添加H.264起始码 (0x00000001)

### 音频处理
- 直接保存RTP负载数据
- 保持G.711 A-law编码格式
- 支持8kHz单声道音频

### 性能优化
- 减少日志输出频率
- 异步文件写入
- 内存缓冲区管理

## 示例输出

成功运行时的典型日志输出：
```
[PLAYER] 创建H.264处理器成功
[PLAYER] 创建音频处理器成功
[H264] 视频将保存到: debug/received_video.h264
[H264] ffplay已启动: ffplay ["-f", "h264", "-framerate", "15", "-"]
[AUDIO] 音频将保存到: debug/received_audio.pcma
[PLAYER] 收到视频包: 序号=100, 时间戳=1500, 大小=1024
[H264] 处理IDR帧, 大小: 1028 bytes
[PLAYER] 收到音频包: 序号=500, 时间戳=8000, 大小=160
```

这个功能将帮助你：
1. 验证是否正确接收到视频和音频数据
2. 分析H.264流的结构和质量
3. 调试WebRTC连接问题
4. 保存通话记录用于后续分析
