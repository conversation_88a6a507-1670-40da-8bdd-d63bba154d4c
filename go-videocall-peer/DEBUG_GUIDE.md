# 视频通话调试指南

## 问题现象
WebRTC连接已建立，但视频文件大小不变，ffplay不播放，没有接收到媒体数据。

## 调试步骤

### 1. 启动程序并加入房间
```bash
./videocall-peer
```

在程序中：
```
join abc0002
```

### 2. 启用调试监控
```
monitor
```
这会启动自动监控，每几秒钟显示统计信息。

### 3. 建立通话连接
在ESP32上：
```
b  # 发起呼叫
```

在Go程序中：
```
accept  # 接受呼叫
```

### 4. 检查调试信息
```
debug  # 显示详细调试信息
files  # 检查文件状态
```

## 关键检查点

### A. WebRTC连接状态
使用 `debug` 命令检查：
- **WebRTC连接状态**: 应该显示 `connected`
- **连接统计**: 检查发送/接收包数量

**如果连接状态不是 `connected`**：
- 检查ICE候选交换
- 验证STUN/TURN服务器配置
- 检查网络防火墙设置

### B. 轨道接收状态
查看日志中是否有：
```
[PLAYER] 设置远程视频轨道: xxx (类型: video, 编解码器: video/H264)
[PLAYER] 开始播放视频轨道: xxx (编解码器: video/H264)
```

**如果没有看到轨道设置日志**：
- ESP32可能没有发送媒体流
- 编解码器协商失败
- SDP交换有问题

### C. RTP包接收状态
查看日志中是否有：
```
[PLAYER] 收到视频包 #1: 序号=xxx, 时间戳=xxx, 大小=xxx
[H264] 处理SPS帧, 大小: xxx bytes
[H264] 处理IDR帧, 大小: xxx bytes
```

**如果没有看到RTP包日志**：
- WebRTC连接可能有问题
- ESP32没有发送数据
- 网络传输问题

### D. 文件保存状态
使用 `files` 命令检查：
```
✅ 视频文件: 12345 bytes
✅ 音频文件: 6789 bytes
```

**如果文件大小为0**：
- H.264处理器可能有问题
- RTP解析失败
- 文件写入权限问题

## 常见问题和解决方案

### 问题1: WebRTC连接状态不是 `connected`
**可能原因**：
- ICE候选交换失败
- STUN/TURN服务器不可达
- 网络NAT/防火墙阻止

**解决方案**：
1. 检查网络连接
2. 验证STUN服务器配置
3. 尝试使用TURN服务器
4. 检查防火墙设置

### 问题2: 连接成功但没有轨道
**可能原因**：
- ESP32没有添加媒体轨道
- 编解码器不匹配
- SDP协商失败

**解决方案**：
1. 检查ESP32日志确认媒体流启动
2. 验证H.264/PCMA编解码器配置
3. 检查SDP内容

### 问题3: 有轨道但没有RTP包
**可能原因**：
- ESP32媒体捕获失败
- 网络传输问题
- WebRTC内部错误

**解决方案**：
1. 重启ESP32设备
2. 检查ESP32媒体捕获日志
3. 重新建立连接

### 问题4: 有RTP包但文件为空
**可能原因**：
- H.264解析错误
- 文件写入失败
- 处理器初始化失败

**解决方案**：
1. 检查debug目录权限
2. 查看H.264处理器错误日志
3. 验证RTP负载格式

## 调试命令参考

### 基本命令
- `join <房间ID>` - 加入房间
- `call` - 发起呼叫
- `accept` - 接受呼叫
- `hangup` - 挂断通话

### 调试命令
- `debug` - 显示详细调试信息
- `monitor` - 启动/停止自动监控
- `files` - 检查保存文件状态
- `stats` - 显示统计信息
- `status` - 显示当前状态

### 快捷键
- `d` = debug
- `m` = monitor  
- `f` = files
- `s` = status

## 日志分析

### 正常流程日志
```
[PEER] 加入房间: abc0002
[SIGNALING] 成功连接到房间: abc0002
[WEBRTC] 连接状态变化: connected
[PLAYER] 设置远程视频轨道: video (类型: video, 编解码器: video/H264)
[PLAYER] 开始播放视频轨道: video
[PLAYER] 收到视频包 #1: 序号=1, 时间戳=1000, 大小=1024
[H264] 处理SPS帧, 大小: 12 bytes
[H264] 处理PPS帧, 大小: 8 bytes
[H264] 处理IDR帧, 大小: 1028 bytes
```

### 异常情况日志
```
[WEBRTC] 连接状态变化: failed
[PLAYER] 读取视频RTP包失败: EOF
[H264] 解析H.264 RTP负载失败: 空的RTP负载
```

## 文件播放验证

### 播放保存的视频
```bash
# 使用ffplay播放
ffplay -f h264 -framerate 15 debug/received_video.h264

# 使用VLC播放
vlc debug/received_video.h264

# 检查文件信息
ffprobe debug/received_video.h264
```

### 播放保存的音频
```bash
# 使用ffplay播放
ffplay -f alaw -ar 8000 -ac 1 debug/received_audio.pcma

# 转换为WAV格式
ffmpeg -f alaw -ar 8000 -ac 1 -i debug/received_audio.pcma output.wav
```

## 高级调试

### 1. 网络抓包
```bash
# 抓取WebRTC流量
sudo tcpdump -i any -w webrtc.pcap port 8089 or port 3478
```

### 2. 检查ESP32日志
确保ESP32输出包含：
```
I (xxx) ESP_CAPTURE: Video source caps negotiate done: 1024x600@10fps
I (xxx) webrtc: PeerConnectionState: 2
```

### 3. 验证编解码器
检查SDP中的编解码器配置：
```
m=video 9 UDP/TLS/RTP/SAVPF 96
a=rtpmap:96 H264/90000
a=fmtp:96 profile-level-id=42001f
```

## 联系支持

如果问题仍然存在，请提供：
1. 完整的程序日志
2. ESP32设备日志  
3. `debug` 命令输出
4. `files` 命令输出
5. 网络环境描述
