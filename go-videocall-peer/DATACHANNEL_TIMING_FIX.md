# 数据通道时序问题修复

## 问题描述
Go程序先加入房间然后呼叫ESP32时，数据通道一直处于`connecting`状态，无法建立连接。

## 根本原因
1. **时序错误**：数据通道在WebRTC连接建立之前就被创建
2. **重复创建**：在多个地方尝试创建数据通道
3. **角色冲突**：发起方和被呼叫方都尝试创建数据通道

## 解决方案

### 1. 延迟WebRTC连接创建
```go
// 修改前：在Initialize()中创建连接
func (vcp *VideoCallPeer) Initialize() error {
    // 创建WebRTC连接
    if err := vcp.webrtcManager.CreatePeerConnection(); err != nil {
        return fmt.Errorf("创建WebRTC连接失败: %v", err)
    }
}

// 修改后：延迟到需要时创建
func (vcp *VideoCallPeer) Initialize() error {
    // 不在初始化时创建WebRTC连接，延迟到需要时创建
    log.Println("[PEER] 视频通话对等端初始化完成（WebRTC连接将在需要时创建）")
    return nil
}
```

### 2. 分离数据通道创建
```go
// 新增方法：创建WebRTC连接但不创建数据通道
func (cm *ConnectionManager) CreatePeerConnectionWithoutDataChannel() error {
    // 创建PeerConnection但跳过数据通道创建
}

// 新增方法：单独创建数据通道
func (cm *ConnectionManager) CreateDataChannelNow() error {
    // 只创建数据通道
}
```

### 3. 明确角色分工
```go
// 发起方（Go程序主动呼叫）
func (vcp *VideoCallPeer) createAndSendOffer() {
    // 作为发起方，创建数据通道
    log.Println("[PEER] 作为发起方，创建数据通道")
    if err := vcp.webrtcManager.CreateDataChannelNow(); err != nil {
        // 处理错误
    }
}

// 被呼叫方（接收对方数据通道）
func (vcp *VideoCallPeer) setupCallbacks() {
    // onDataChannel
    func(dc *webrtc.DataChannel) {
        log.Printf("[PEER] 接收到数据通道: %s", dc.Label())
        // 作为被呼叫方，设置接收到的数据通道为发送器
        vcp.mediaCapture.SetDataChannelSender(vcp.webrtcManager)
    }
}
```

### 4. 正确的时序流程

#### 发起方流程（Go程序主动呼叫）
1. `StartCall()` - 创建WebRTC连接（不含数据通道）
2. 发送`RING`命令
3. 收到`ACCEPT_CALL`响应
4. `createAndSendOffer()` - 创建数据通道
5. 创建并发送SDP offer
6. 数据通道在ICE连接建立后打开

#### 被呼叫方流程（ESP32接受呼叫）
1. 收到`RING`命令
2. 发送`ACCEPT_CALL`响应
3. 收到SDP offer
4. `handleOffer()` - 创建WebRTC连接（不含数据通道）
5. 创建并发送SDP answer
6. 通过`OnDataChannel`回调接收对方的数据通道
7. 数据通道在ICE连接建立后打开

## 技术细节

### WebRTC数据通道状态
- `connecting` - 数据通道已创建但未打开
- `open` - 数据通道已打开，可以发送数据
- `closed` - 数据通道已关闭

### 关键时序点
1. **PeerConnection创建** - 必须在SDP协商之前
2. **数据通道创建** - 必须在SDP offer创建之前
3. **数据通道打开** - 必须在ICE连接建立之后

### 日志验证
成功的日志应该显示：
```
[PEER] 作为发起方，创建数据通道
[WEBRTC] 开始创建数据通道...
[WEBRTC] 数据通道创建成功
[WEBRTC] ✅ 数据通道已打开，可以开始发送视频数据
```

## 测试验证
1. Go程序先加入房间
2. ESP32加入同一房间
3. Go程序发起呼叫
4. ESP32接受呼叫
5. 验证数据通道状态变为`open`
6. 验证视频数据传输正常
