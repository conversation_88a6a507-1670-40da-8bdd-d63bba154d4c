#!/bin/bash

# 测试JPEG数据通道支持

echo "=== JPEG数据通道支持测试 ==="
echo

# 清理之前的调试文件
rm -f debug/received_video.h264 debug/received_video.mjpeg debug/received_audio.pcma debug/monitor.log

echo "✅ 已清理调试文件"
echo

echo "启动程序进行JPEG测试..."
echo "请按以下步骤操作："
echo
echo "1. 程序启动后，输入: join a0005"
echo "2. 启用监控: monitor"
echo "3. 在ESP32上发起呼叫 (输入 b)"
echo "4. 在程序中接受呼叫: accept"
echo "5. 观察以下关键日志:"
echo "   - [JPEG] JPEG图像将保存到: debug/received_video.mjpeg"
echo "   - [JPEG] ffplay已启动播放MJPEG流"
echo "   - [JPEG] 接收JPEG帧 #10, 大小: 20500 bytes, FPS: 10.0"
echo "   - [PLAYER] 数据通道二进制消息被成功处理为JPEG"
echo
echo "6. 使用调试命令检查状态:"
echo "   - debug  (检查视频统计信息)"
echo "   - files  (检查MJPEG文件大小)"
echo
echo "预期结果:"
echo "✅ 应该看到JPEG帧接收日志"
echo "✅ debug/received_video.mjpeg文件应该持续增长"
echo "✅ ffplay应该显示ESP32摄像头画面"
echo "✅ 视频统计应该显示帧数和FPS"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 检查结果
echo "检查测试结果:"

if [ -f "debug/received_video.mjpeg" ]; then
    size=$(stat -f%z "debug/received_video.mjpeg" 2>/dev/null || stat -c%s "debug/received_video.mjpeg" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ MJPEG文件: ${size} bytes (成功接收JPEG数据)"
        
        # 计算大概的帧数（假设每帧20KB）
        frames=$((size / 20000))
        echo "📹 估计接收帧数: ~${frames} 帧"
        
        echo "🎬 播放命令:"
        echo "   ffplay -f mjpeg debug/received_video.mjpeg"
        echo "   vlc debug/received_video.mjpeg"
        
        # 尝试提取第一帧作为单独的JPEG文件
        if command -v dd >/dev/null 2>&1; then
            echo "📸 提取第一帧:"
            # 查找第一个JPEG结束标记
            if dd if=debug/received_video.mjpeg of=debug/first_frame.jpg bs=1 count=25000 2>/dev/null; then
                echo "   debug/first_frame.jpg (前25KB，可能包含第一帧)"
            fi
        fi
    else
        echo "❌ MJPEG文件: 0 bytes (未接收到数据)"
    fi
else
    echo "❌ MJPEG文件: 不存在"
fi

if [ -f "debug/received_audio.pcma" ]; then
    size=$(stat -f%z "debug/received_audio.pcma" 2>/dev/null || stat -c%s "debug/received_audio.pcma" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 音频文件: ${size} bytes (成功接收数据)"
        echo "🎵 播放命令:"
        echo "   ffplay -f alaw -ar 8000 -ac 1 debug/received_audio.pcma"
    else
        echo "❌ 音频文件: 0 bytes (未接收到数据)"
    fi
else
    echo "❌ 音频文件: 不存在"
fi

if [ -f "debug/monitor.log" ]; then
    echo "📊 监控日志已生成: debug/monitor.log"
    echo "   最后几行:"
    tail -5 debug/monitor.log 2>/dev/null || echo "   (无法读取)"
fi

echo
echo "JPEG支持功能说明:"
echo "- ESP32通过数据通道发送JPEG图像"
echo "- 每个JPEG图像约20KB，10FPS"
echo "- 保存为MJPEG格式，可用ffplay/VLC播放"
echo "- 支持实时ffplay显示（如果可用）"
echo
echo "如果仍然有问题，请检查:"
echo "1. ESP32是否正确发送JPEG数据"
echo "2. 数据通道是否正常建立"
echo "3. JPEG文件头是否正确 (FF D8)"
echo "4. ffplay是否正确安装"
