#!/bin/bash

# 测试1024x600分辨率视频生成
# 用于验证ESP32P4 LCD兼容性

echo "=== 测试1024x600分辨率视频生成 ==="

# 确保debug目录存在
mkdir -p debug

# 清理旧的测试文件
rm -f debug/sent_frame_*.jpg
rm -f debug/received_video.*

echo "1. 测试FFmpeg生成1024x600测试图像..."

# 测试各种图案
patterns=("testsrc2" "gradients" "rgbtestsrc" "color=red")
sizes=("1024x600")

for i in "${!patterns[@]}"; do
    pattern="${patterns[$i]}"
    echo "   测试图案 $((i+1)): $pattern"
    
    # 生成测试图像
    if [[ "$pattern" == "color=red" ]]; then
        ffmpeg -f lavfi -i "$pattern:size=1024x600:duration=1" \
               -vf "drawtext=text='Pattern $((i+1)) - 1024x600':x=50:y=50:fontsize=48:fontcolor=white:box=1:boxcolor=black@0.5" \
               -vframes 1 -f mjpeg -q:v 3 -pix_fmt yuvj420p \
               -loglevel quiet "debug/test_pattern_${i}_1024x600.jpg"
    else
        ffmpeg -f lavfi -i "$pattern=size=1024x600:rate=1:duration=1" \
               -vf "drawtext=text='Pattern $((i+1)) - 1024x600':x=50:y=50:fontsize=48:fontcolor=white:box=1:boxcolor=black@0.5" \
               -vframes 1 -f mjpeg -q:v 3 -pix_fmt yuvj420p \
               -loglevel quiet "debug/test_pattern_${i}_1024x600.jpg"
    fi
    
    if [ -f "debug/test_pattern_${i}_1024x600.jpg" ]; then
        size=$(wc -c < "debug/test_pattern_${i}_1024x600.jpg")
        echo "     ✓ 生成成功: debug/test_pattern_${i}_1024x600.jpg (${size} bytes)"
        
        # 验证图像分辨率
        if command -v identify >/dev/null 2>&1; then
            resolution=$(identify "debug/test_pattern_${i}_1024x600.jpg" | grep -o '[0-9]*x[0-9]*')
            echo "     ✓ 分辨率验证: $resolution"
        fi
    else
        echo "     ✗ 生成失败"
    fi
done

echo ""
echo "2. 启动Go程序测试实际视频传输..."
echo "   请在另一个终端运行ESP32设备，然后执行以下命令："
echo "   ./videocall-peer --room aa0003"
echo ""
echo "3. 预期效果："
echo "   - Go程序将生成1024x600分辨率的彩色测试图像"
echo "   - ESP32P4应该能在LCD屏幕上全屏显示视频内容"
echo "   - 不再出现黑屏问题"
echo ""
echo "4. 调试信息："
echo "   - 发送的帧样本将保存在debug/sent_frame_*.jpg"
echo "   - 可以检查这些文件确认分辨率为1024x600"
echo ""

# 显示生成的测试图像信息
echo "=== 生成的测试图像 ==="
for file in debug/test_pattern_*_1024x600.jpg; do
    if [ -f "$file" ]; then
        size=$(wc -c < "$file")
        echo "  $file: ${size} bytes"
    fi
done

echo ""
echo "测试准备完成！现在可以运行videocall-peer进行实际测试。"
