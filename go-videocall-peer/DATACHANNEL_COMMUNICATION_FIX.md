# 数据通道通信问题修复

## 问题描述
1. Go程序先加入房间然后呼叫ESP32时，数据通道一直处于connecting状态
2. ESP32主动呼叫原本可以接通，但修改后也不行了

## 根本原因
**数据通道发送器未正确设置**：在接收到对方的数据通道时，没有设置数据通道发送器，导致被呼叫方无法发送视频数据。

## 解决方案

### 1. 恢复正确的初始化流程
```go
// Initialize() - 创建WebRTC连接和数据通道
func (vcp *VideoCallPeer) Initialize() error {
    // 初始化媒体捕获器
    if err := vcp.mediaCapture.Initialize(); err != nil {
        return fmt.Errorf("初始化媒体捕获器失败: %v", err)
    }

    // 创建WebRTC连接（包含数据通道）
    if err := vcp.webrtcManager.CreatePeerConnection(); err != nil {
        return fmt.Errorf("创建WebRTC连接失败: %v", err)
    }

    return nil
}
```

### 2. 修复数据通道处理
```go
// 关键修复：在接收到数据通道时设置发送器
// onDataChannel
func(dc *webrtc.DataChannel) {
    log.Printf("[PEER] 接收到数据通道: %s", dc.Label())
    // 作为被呼叫方，接收到对方的数据通道后，设置数据通道发送器
    log.Println("[PEER] 作为被呼叫方，设置接收到的数据通道为发送器")
    vcp.mediaCapture.SetDataChannelSender(vcp.webrtcManager)
}
```

### 3. 正确的通信流程

#### Go程序主动呼叫ESP32
1. Go程序：`StartCall()` → 发送RING命令
2. ESP32：收到RING → 发送ACCEPT_CALL
3. Go程序：收到ACCEPT_CALL → `createAndSendOffer()`
4. Go程序：创建WebRTC连接（如果需要）→ 设置数据通道发送器 → 创建SDP offer
5. ESP32：收到offer → 创建answer → 建立WebRTC连接
6. Go程序：通过`OnDataChannel`回调接收ESP32的数据通道
7. **关键**：Go程序设置数据通道发送器，开始双向通信

#### ESP32主动呼叫Go程序
1. ESP32：发送RING命令
2. Go程序：收到RING → 用户接受 → `AcceptCall()`
3. Go程序：创建WebRTC连接 → 设置数据通道发送器 → 发送ACCEPT_CALL
4. ESP32：收到ACCEPT_CALL → 创建offer
5. Go程序：收到offer → `handleOffer()` → 创建answer
6. ESP32：建立WebRTC连接，数据通道打开
7. 双向通信开始

## 技术细节

### 数据通道角色分配
- **发起方**：创建数据通道
- **被呼叫方**：接收数据通道，并设置为发送器

### 关键时序点
1. **WebRTC连接创建**：在Initialize()时创建
2. **数据通道创建**：作为WebRTC连接的一部分自动创建
3. **数据通道发送器设置**：
   - 发起方：在createAndSendOffer()中设置
   - 被呼叫方：在OnDataChannel回调中设置

### 日志验证
成功的日志应该显示：
```
[PEER] 接收到数据通道: videocall
[PEER] 作为被呼叫方，设置接收到的数据通道为发送器
[MEDIA] 数据通道发送器已设置
[WEBRTC] ✅ 数据通道已打开，可以开始发送视频数据
```

## 测试验证

### 测试场景1：Go程序主动呼叫
1. Go程序加入房间
2. ESP32加入同一房间
3. Go程序发起呼叫：`call`
4. ESP32接受呼叫：`b`
5. 验证数据通道双向通信

### 测试场景2：ESP32主动呼叫
1. ESP32加入房间
2. Go程序加入同一房间
3. ESP32发起呼叫：`c`
4. Go程序接受呼叫：`accept`
5. 验证数据通道双向通信

## 预期结果
- 数据通道状态从connecting变为open
- 双向视频数据传输正常
- 两种呼叫方向都能正常工作
