# 控制台乱码输出修复

## 问题描述
在WebRTC通话过程中，控制台出现乱码输出，影响用户体验和调试。

## 问题根因
**数据通道消息被直接转换为字符串并打印到控制台**

在 `internal/videocall/peer.go` 第457行：
```go
log.Printf("[PEER] 数据通道消息: %s", string(msg.Data))
```

当ESP32通过WebRTC数据通道发送二进制数据（如视频帧、音频数据或其他二进制协议数据）时，直接将二进制数据转换为字符串会产生不可打印字符，导致控制台显示乱码。

## 修复方案

### 1. 智能数据类型检测
添加 `isTextData()` 函数来检测数据是否为文本：
- 检查前100个字节是否都是可打印ASCII字符
- 允许常见的控制字符（换行符、制表符等）
- 如果包含不可打印字符，则判定为二进制数据

### 2. 分类处理输出
根据数据类型采用不同的输出格式：

**文本数据**：
```go
log.Printf("[PEER] 数据通道文本消息: %s", string(msg.Data))
```

**二进制数据**：
```go
log.Printf("[PEER] 数据通道二进制消息: %d bytes, 前4字节: %x", len(msg.Data), msg.Data[:min(4, len(msg.Data))])
```

**空数据**：
```go
log.Printf("[PEER] 数据通道空消息")
```

### 3. 修复后的代码
```go
// onDataChannelMessage
func(msg webrtc.DataChannelMessage) {
    // 避免打印二进制数据导致控制台乱码
    if len(msg.Data) > 0 {
        // 检查是否是文本数据
        if isTextData(msg.Data) {
            log.Printf("[PEER] 数据通道文本消息: %s", string(msg.Data))
        } else {
            log.Printf("[PEER] 数据通道二进制消息: %d bytes, 前4字节: %x", len(msg.Data), msg.Data[:min(4, len(msg.Data))])
        }
    } else {
        log.Printf("[PEER] 数据通道空消息")
    }
},
```

### 4. 辅助函数
```go
// isTextData 检查数据是否为文本数据
func isTextData(data []byte) bool {
    if len(data) == 0 {
        return true
    }
    
    // 检查前100个字节是否都是可打印字符
    checkLen := min(100, len(data))
    for i := 0; i < checkLen; i++ {
        b := data[i]
        // 允许可打印ASCII字符、换行符、制表符
        if !((b >= 32 && b <= 126) || b == '\n' || b == '\r' || b == '\t') {
            return false
        }
    }
    return true
}

// min 返回两个整数中的较小值
func min(a, b int) int {
    if a < b {
        return a
    }
    return b
}
```

## 修复效果

### 修复前
```
[PEER] 数据通道消息: ��������g B��T��������
```

### 修复后
```
[PEER] 数据通道二进制消息: 1024 bytes, 前4字节: 00000167
```

## 其他改进

### 1. 调试友好
- 显示数据大小，便于了解传输量
- 显示前几个字节的十六进制值，便于协议分析
- 保持日志的可读性

### 2. 性能优化
- 只检查前100个字节来判断数据类型
- 避免处理大量二进制数据时的性能问题

### 3. 扩展性
- 可以轻松添加更多数据类型的检测
- 可以根据数据通道标签采用不同的处理策略

## 测试验证

创建了测试程序验证修复效果：
```bash
go run test_binary_data.go
```

测试结果：
```
文本数据测试: true
二进制数据测试: false
混合数据测试: false

=== 数据通道消息处理测试 ===
[PEER] 数据通道文本消息: Hello, this is a text message!
[PEER] 数据通道二进制消息: 12 bytes, 前4字节: 00000001
[PEER] 数据通道空消息
```

## 使用建议

1. **监控数据通道流量**：
   - 观察二进制消息的大小和频率
   - 分析前几个字节来识别协议类型

2. **调试二进制协议**：
   - 使用十六进制输出分析协议头
   - 结合ESP32日志确认数据类型

3. **性能监控**：
   - 大量二进制数据传输时关注性能
   - 必要时可以降低日志输出频率

## 相关文件
- `internal/videocall/peer.go` - 主要修复文件
- `test_binary_data.go` - 测试验证程序（已删除）
- `CONSOLE_OUTPUT_FIX.md` - 本文档

## 总结
通过智能检测数据类型并采用适当的输出格式，成功解决了控制台乱码问题，同时保持了调试信息的有用性和可读性。
