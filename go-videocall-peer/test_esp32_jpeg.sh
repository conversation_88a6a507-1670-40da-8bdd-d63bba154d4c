#!/bin/bash

# 测试ESP32兼容的JPEG格式

echo "=== ESP32兼容JPEG格式测试 ==="
echo

# 清理之前的调试文件
rm -f debug/received_video.h264 debug/received_video.mjpeg debug/received_audio.pcma debug/monitor.log

echo "✅ 已清理调试文件"
echo

echo "启动程序进行ESP32兼容JPEG测试..."
echo "请按以下步骤操作："
echo
echo "1. 程序启动后，输入: join d0002"
echo "2. 启用监控: monitor"
echo "3. 在ESP32上加入房间: join d0002"
echo "4. 在ESP32上发起呼叫: b"
echo "5. 在程序中接受呼叫: accept"
echo "6. 观察以下关键日志:"
echo "   - [PEER] 开始发送offer给ESP32"
echo "   - [WEBRTC] 连接状态变化: connected"
echo "   - [MEDIA] 已通过数据通道发送JPEG帧"
echo "   - [JPEG] 接收JPEG帧 (ESP32发送的)"
echo
echo "7. 检查ESP32端是否不再报错:"
echo "   - 不应该看到: jpeg.decoder: wrong, we don't support such sampling mode"
echo "   - 不应该看到: Only baseline-DCT is supported"
echo "   - 不应该看到: Sample precision is not 8"
echo "   - 应该看到: Recv V:[非0:非0] (接收视频数据)"
echo
echo "8. 使用调试命令检查状态:"
echo "   - debug  (检查连接和传输状态)"
echo "   - files  (检查文件大小)"
echo
echo "预期结果:"
echo "✅ WebRTC连接成功建立"
echo "✅ Go程序发送ESP32兼容的JPEG数据"
echo "✅ ESP32能正确解码JPEG数据，不再崩溃"
echo "✅ ESP32显示: Recv V:[非0:非0]"
echo "✅ 双向视频和音频传输正常"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 检查结果
echo "检查测试结果:"

if [ -f "debug/received_video.mjpeg" ]; then
    size=$(stat -f%z "debug/received_video.mjpeg" 2>/dev/null || stat -c%s "debug/received_video.mjpeg" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 接收MJPEG文件: ${size} bytes (ESP32 -> Go)"
        
        # 计算大概的帧数
        frames=$((size / 15000))
        echo "📹 估计接收帧数: ~${frames} 帧"
        
        echo "🎬 播放命令:"
        echo "   ffplay -f mjpeg debug/received_video.mjpeg"
        
        # 检查第一帧的JPEG头部
        if command -v hexdump >/dev/null 2>&1; then
            echo "📸 ESP32发送的JPEG头部:"
            hexdump -C debug/received_video.mjpeg | head -3
        fi
    else
        echo "❌ 接收MJPEG文件: 0 bytes (ESP32 -> Go 失败)"
    fi
else
    echo "❌ 接收MJPEG文件: 不存在"
fi

if [ -f "debug/received_audio.pcma" ]; then
    size=$(stat -f%z "debug/received_audio.pcma" 2>/dev/null || stat -c%s "debug/received_audio.pcma" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 音频文件: ${size} bytes (双向音频正常)"
        echo "🎵 播放命令:"
        echo "   ffplay -f alaw -ar 8000 -ac 1 debug/received_audio.pcma"
    else
        echo "❌ 音频文件: 0 bytes (音频传输失败)"
    fi
else
    echo "❌ 音频文件: 不存在"
fi

echo
echo "ESP32端检查要点:"
echo "1. ESP32应该不再报JPEG解码错误"
echo "2. ESP32应该显示正常的视频接收统计:"
echo "   I (xxx) webrtc: Send V:xxx Recv V:[非0:非0] ← 关键指标"
echo "3. ESP32不应该崩溃或重启"
echo
echo "JPEG格式改进说明:"
echo "- 使用标准的baseline-DCT格式"
echo "- 8位采样精度 (ESP32要求)"
echo "- 标准量化表和霍夫曼表"
echo "- 最小的16x16灰度图像"
echo "- 完全兼容ESP32 JPEG解码器"
echo
echo "如果ESP32仍然报错，可能需要:"
echo "1. 进一步简化JPEG格式"
echo "2. 使用ESP32实际发送的JPEG格式作为模板"
echo "3. 检查ESP32 JPEG解码器的具体要求"
echo "4. 考虑使用原始图像数据而不是JPEG"
echo
echo "如果测试成功，说明:"
echo "✅ WebRTC连接问题已解决"
echo "✅ JPEG格式兼容性问题已解决"
echo "✅ 双向视频通话功能正常工作"
