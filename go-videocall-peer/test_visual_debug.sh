#!/bin/bash

# 视觉调试测试脚本

echo "=== 视觉调试测试 ==="
echo

# 清理之前的文件
rm -f test_enhanced.jpg debug/sent_*.jpg

echo "🎨 生成增强的测试图像样本:"

# 生成4种不同的测试图案
patterns=("testsrc2" "gradients" "rgbtestsrc" "color=red")
pattern_names=("彩色条纹" "彩色渐变" "RGB测试" "红色背景")

for i in {0..3}; do
    pattern=${patterns[$i]}
    name=${pattern_names[$i]}
    
    echo "生成图案 $((i+1)): $name"
    
    if [ "$pattern" = "color=red" ]; then
        input_pattern="color=red:size=320x240:duration=1"
    else
        input_pattern="${pattern}=size=320x240:rate=1:duration=1"
    fi
    
    ffmpeg -f lavfi -i "$input_pattern" \
           -vf "drawtext=text='Pattern $((i+1)) Frame 10':x=10:y=10:fontsize=20:fontcolor=white:box=1:boxcolor=black@0.5" \
           -vframes 1 -f mjpeg -q:v 3 -pix_fmt yuvj420p \
           -y "debug/pattern_${i}.jpg" -loglevel error
    
    if [ -f "debug/pattern_${i}.jpg" ]; then
        size=$(stat -f%z "debug/pattern_${i}.jpg" 2>/dev/null || stat -c%s "debug/pattern_${i}.jpg" 2>/dev/null)
        echo "  ✅ pattern_${i}.jpg (${size} bytes) - $name"
    else
        echo "  ❌ 生成失败"
    fi
done

echo
echo "📸 检查生成的图像:"
echo "可以用以下命令查看生成的测试图像:"
echo "  open debug/pattern_*.jpg  (macOS)"
echo "  xdg-open debug/pattern_*.jpg  (Linux)"
echo

echo "🚀 启动Go程序进行增强视觉测试..."
echo
echo "现在的改进:"
echo "✅ 4种不同的测试图案循环显示"
echo "✅ 文字叠加显示帧数"
echo "✅ 更高的JPEG质量 (q:v 3)"
echo "✅ 动态颜色变化"
echo "✅ 更明显的视觉效果"
echo
echo "预期在ESP32上看到:"
echo "1. 每4帧切换一种图案:"
echo "   - 帧 0,4,8...: 彩色条纹"
echo "   - 帧 1,5,9...: 彩色渐变"
echo "   - 帧 2,6,10..: RGB测试图案"
echo "   - 帧 3,7,11..: 红色背景"
echo "2. 每个图案上都有白色文字显示帧数"
echo "3. 颜色会动态变化"
echo "4. LCD_RENDER应该显示正常的FPS"
echo
echo "如果ESP32仍然看不到图像，可能的原因:"
echo "1. LCD显示器配置问题"
echo "2. 颜色格式转换问题"
echo "3. 显示缓冲区问题"
echo "4. 硬件连接问题"
echo
echo "调试建议:"
echo "1. 检查ESP32的LCD显示器是否正常工作"
echo "2. 查看ESP32是否有其他显示相关的错误日志"
echo "3. 确认LCD_RENDER的配置是否正确"
echo "4. 检查视频解码后的数据是否正确传递给显示器"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 检查是否有保存的发送帧
echo "检查发送的图像帧:"
if ls debug/sent_*.jpg 1> /dev/null 2>&1; then
    for file in debug/sent_*.jpg; do
        if [ -f "$file" ]; then
            size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
            echo "✅ $file (${size} bytes)"
        fi
    done
    echo "🖼️  可以查看发送的实际图像内容"
else
    echo "❌ 没有找到保存的发送帧"
fi

# 检查接收的数据
if [ -f "debug/received_video.mjpeg" ]; then
    size=$(stat -f%z "debug/received_video.mjpeg" 2>/dev/null || stat -c%s "debug/received_video.mjpeg" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 接收MJPEG文件: ${size} bytes (ESP32摄像头数据)"
        echo "🎬 播放命令:"
        echo "   ffplay -f mjpeg debug/received_video.mjpeg"
    else
        echo "❌ 接收MJPEG文件: 0 bytes"
    fi
else
    echo "❌ 接收MJPEG文件: 不存在"
fi

echo
echo "总结:"
echo "从ESP32日志看，技术层面已经成功:"
echo "✅ WebRTC连接建立"
echo "✅ JPEG解码成功"
echo "✅ 视频分辨率识别正确 (320x240)"
echo "✅ LCD渲染器启动"
echo "✅ 正常的FPS显示"
echo "✅ 数据传输正常"
echo
echo "如果仍然看不到图像，问题可能在:"
echo "1. ESP32硬件显示部分"
echo "2. LCD配置或连接"
echo "3. 颜色空间转换"
echo "4. 显示缓冲区管理"
echo
echo "建议下一步:"
echo "1. 检查ESP32的LCD硬件连接"
echo "2. 查看ESP32显示相关的配置"
echo "3. 确认LCD_RENDER模块是否正常工作"
echo "4. 检查是否有其他显示相关的错误日志"
