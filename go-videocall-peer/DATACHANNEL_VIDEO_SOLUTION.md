# 数据通道视频传输解决方案

## 问题分析

### ESP32 videocall_demo的配置
从ESP32日志和代码分析发现：
```c
.video_over_data_channel = true,  // 通过数据通道传输视频
.video_dir = ESP_PEER_MEDIA_DIR_SEND_RECV,  // 双向视频传输
```

### 问题根因
- **ESP32发送**：✅ 通过数据通道发送JPEG图像（我们已能接收）
- **ESP32接收**：❌ 期望通过数据通道接收视频，但Go程序只通过视频轨道发送
- **结果**：ESP32日志显示 `Recv V:[0:0]` - 没有接收到视频数据

## 解决方案

### 核心思路
**让Go程序也通过数据通道发送视频，与ESP32保持一致**

### 实现步骤

#### 1. 添加数据通道发送器接口
```go
// DataChannelSender 数据通道发送器接口
type DataChannelSender interface {
    SendDataChannelMessage(data []byte) error
}
```

#### 2. 修改MediaCapture支持数据通道发送
```go
type MediaCapture struct {
    // 新增数据通道发送器
    dataChannelSender DataChannelSender
    // ... 其他字段
}

// 设置数据通道发送器
func (mc *MediaCapture) SetDataChannelSender(sender DataChannelSender) {
    mc.dataChannelSender = sender
}
```

#### 3. 修改视频捕获逻辑
```go
// 原来：通过视频轨道发送H.264数据
testFrame := mc.generateTestVideoFrame(frameCount)
mc.videoTrack.WriteSample(testFrame)

// 现在：通过数据通道发送JPEG数据
jpegData := mc.generateTestJPEGFrame(frameCount)
mc.dataChannelSender.SendDataChannelMessage(jpegData)
```

#### 4. 生成ESP32兼容的JPEG数据
```go
func (mc *MediaCapture) generateTestJPEGFrame(frameCount uint64) []byte {
    // 生成约20KB的JPEG数据（与ESP32一致）
    jpegSize := 20000 + int(frameCount%1000)
    jpegData := make([]byte, jpegSize)
    
    // JPEG文件头 (SOI + APP0)
    jpegData[0] = 0xFF
    jpegData[1] = 0xD8  // SOI
    jpegData[2] = 0xFF
    jpegData[3] = 0xE0  // APP0
    // ... JFIF标识和数据
    
    // JPEG文件尾 (EOI)
    jpegData[jpegSize-2] = 0xFF
    jpegData[jpegSize-1] = 0xD9  // EOI
    
    return jpegData
}
```

#### 5. 在PeerConnection创建时设置发送器
```go
// 创建WebRTC PeerConnection
vcp.webrtcManager.CreatePeerConnection()

// 设置数据通道发送器（关键步骤）
vcp.mediaCapture.SetDataChannelSender(vcp.webrtcManager)

// 添加轨道（音频轨道仍然需要）
vcp.webrtcManager.AddLocalTracks(videoTrack, audioTrack)
```

## 技术细节

### 数据流向
```
Go程序 ←→ ESP32 videocall_demo

发送方向 (Go → ESP32):
Go MediaCapture → DataChannel → ESP32接收器

接收方向 (ESP32 → Go):
ESP32发送器 → DataChannel → Go MediaPlayer
```

### JPEG数据格式
```
文件头: FF D8 FF E0 (JPEG SOI + APP0)
JFIF标识: "JFIF\0"
数据大小: ~20KB (与ESP32一致)
文件尾: FF D9 (JPEG EOI)
```

### 日志输出
```
发送端 (Go程序):
[MEDIA] 数据通道发送器已设置
[MEDIA] 已通过数据通道发送JPEG帧: 30 (大小: 20123 bytes)

接收端 (Go程序):
[PEER] 数据通道二进制消息: 20500 bytes, 前4字节: ffd8ffe0
[JPEG] 接收JPEG帧 #10, 大小: 20500 bytes, FPS: 10.0

ESP32端 (期望结果):
Send V:xxx [帧数:字节数]  ← 发送正常
Recv V:[非0:非0]         ← 接收正常 (关键指标)
```

## 配置兼容性

### ESP32 videocall_demo配置
```c
esp_peer_config_t config = {
    .video_dir = ESP_PEER_MEDIA_DIR_SEND_RECV,  // 双向视频
    .video_over_data_channel = true,            // 数据通道视频
    .audio_dir = ESP_PEER_MEDIA_DIR_SEND_RECV,  // 双向音频
    // ... 其他配置
};
```

### Go程序配置
```yaml
capture:
  video:
    enable: true
    fps: 10
    # 通过数据通道发送，不再使用视频轨道

playback:
  video:
    enable: true
    # 接收数据通道JPEG数据
```

## 测试验证

### 测试步骤
1. 启动Go程序：`./videocall-peer`
2. 加入房间：`join a0005`
3. 启用监控：`monitor`
4. ESP32发起呼叫：`b`
5. 接受呼叫：`accept`

### 成功指标
- ✅ Go程序发送JPEG数据通道消息
- ✅ ESP32接收视频数据：`Recv V:[非0:非0]`
- ✅ 双向音频正常：`Send A:xxx Recv A:xxx`
- ✅ 双向视频正常：`Send V:xxx Recv V:xxx`

### 调试命令
```bash
debug   # 检查双向传输统计
files   # 检查接收文件大小
monitor # 实时监控传输状态
```

## 优势

### 1. 协议兼容
- 与ESP32 videocall_demo完全兼容
- 无需修改ESP32代码
- 遵循ESP32的设计模式

### 2. 双向传输
- Go → ESP32：JPEG数据通道
- ESP32 → Go：JPEG数据通道
- 音频：双向RTP轨道

### 3. 灵活性
- 保留视频轨道支持（向后兼容）
- 可根据对端配置选择传输方式
- 支持多种视频格式

### 4. 调试友好
- 详细的传输日志
- 文件保存和播放支持
- 实时统计监控

## 注意事项

### 1. 数据通道建立
- 必须在PeerConnection创建后设置发送器
- 确保数据通道在发送前已打开
- 处理数据通道连接失败情况

### 2. JPEG格式兼容
- 保持与ESP32相同的JPEG格式
- 文件头和尾必须正确
- 数据大小应该合理（~20KB）

### 3. 性能考虑
- 数据通道传输可能有延迟
- 大数据包可能需要分片
- 监控传输错误和重试

## 总结

通过让Go程序也使用数据通道发送视频数据，实现了与ESP32 videocall_demo的完全兼容。现在ESP32应该能够接收到Go程序发送的视频数据，日志中的`Recv V:[0:0]`问题应该得到解决。

这个解决方案保持了协议的一致性，避免了修改ESP32代码的复杂性，是一个优雅且实用的解决方案。
