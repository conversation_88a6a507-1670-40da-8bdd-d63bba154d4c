package webrtc

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"go-videocall-peer/config"

	"github.com/pion/webrtc/v4"
)

// ConnectionManager WebRTC连接管理器
type ConnectionManager struct {
	config   *config.Config
	peerConn *webrtc.PeerConnection
	api      *webrtc.API
	ctx      context.Context
	cancel   context.CancelFunc
	mutex    sync.RWMutex

	// 媒体轨道
	localVideoTrack  *webrtc.TrackLocalStaticSample
	localAudioTrack  *webrtc.TrackLocalStaticSample
	remoteVideoTrack *webrtc.TrackRemote
	remoteAudioTrack *webrtc.TrackRemote

	// 数据通道
	dataChannel *webrtc.DataChannel

	// 回调函数
	onConnectionStateChange func(webrtc.PeerConnectionState)
	onICECandidate          func(*webrtc.ICECandidate)
	onTrack                 func(*webrtc.TrackRemote, *webrtc.RTPReceiver)
	onDataChannel           func(*webrtc.DataChannel)
	onDataChannelMessage    func(webrtc.DataChannelMessage)

	// 统计信息
	stats ConnectionStats
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	ConnectedAt     time.Time
	BytesSent       uint64
	BytesReceived   uint64
	PacketsSent     uint64
	PacketsReceived uint64
	PacketsLost     uint64
	Jitter          float64
	RTT             time.Duration
	Quality         float64
	mutex           sync.RWMutex
}

// NewConnectionManager 创建新的连接管理器
func NewConnectionManager(cfg *config.Config) (*ConnectionManager, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建媒体引擎
	mediaEngine := &webrtc.MediaEngine{}

	// 注册ESP32兼容的编解码器
	if err := registerESP32Codecs(mediaEngine); err != nil {
		cancel()
		return nil, fmt.Errorf("注册编解码器失败: %v", err)
	}

	// 创建API实例
	api := webrtc.NewAPI(webrtc.WithMediaEngine(mediaEngine))

	cm := &ConnectionManager{
		config: cfg,
		api:    api,
		ctx:    ctx,
		cancel: cancel,
	}

	return cm, nil
}

// CreatePeerConnection 创建WebRTC对等连接
func (cm *ConnectionManager) CreatePeerConnection() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cm.peerConn != nil {
		cm.peerConn.Close()
		cm.peerConn = nil
	}

	// 构建ICE服务器配置
	iceServers := make([]webrtc.ICEServer, 0, len(cm.config.WebRTC.ICEServers))
	for _, server := range cm.config.WebRTC.ICEServers {
		ice := webrtc.ICEServer{
			URLs: server.URLs,
		}
		if server.Username != "" {
			ice.Username = server.Username
			ice.Credential = server.Credential
		}
		iceServers = append(iceServers, ice)
	}

	config := webrtc.Configuration{
		ICEServers: iceServers,
	}

	pc, err := cm.api.NewPeerConnection(config)
	if err != nil {
		return fmt.Errorf("创建PeerConnection失败: %v", err)
	}

	cm.peerConn = pc

	// 设置事件处理器
	cm.setupEventHandlers()

	// 创建数据通道（如果启用）
	if cm.config.WebRTC.EnableDataChannel {
		if err := cm.createDataChannel(); err != nil {
			log.Printf("[WEBRTC] 创建数据通道失败: %v", err)
		}
	}

	log.Println("[WEBRTC] PeerConnection创建成功")
	return nil
}

// AddLocalTracks 添加本地媒体轨道
func (cm *ConnectionManager) AddLocalTracks(videoTrack, audioTrack *webrtc.TrackLocalStaticSample) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cm.peerConn == nil {
		return fmt.Errorf("PeerConnection未创建")
	}

	// 添加音频轨道（ESP32兼容性：先音频）
	if audioTrack != nil {
		if _, err := cm.peerConn.AddTrack(audioTrack); err != nil {
			return fmt.Errorf("添加音频轨道失败: %v", err)
		}
		cm.localAudioTrack = audioTrack
		log.Printf("[WEBRTC] 添加音频轨道: %s", audioTrack.ID())
	}

	// 添加视频轨道
	if videoTrack != nil {
		if _, err := cm.peerConn.AddTrack(videoTrack); err != nil {
			return fmt.Errorf("添加视频轨道失败: %v", err)
		}
		cm.localVideoTrack = videoTrack
		log.Printf("[WEBRTC] 添加视频轨道: %s", videoTrack.ID())
	}

	return nil
}

// CreateOffer 创建SDP Offer
func (cm *ConnectionManager) CreateOffer() (*webrtc.SessionDescription, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.peerConn == nil {
		return nil, fmt.Errorf("PeerConnection未创建")
	}

	offer, err := cm.peerConn.CreateOffer(nil)
	if err != nil {
		return nil, fmt.Errorf("创建Offer失败: %v", err)
	}

	if err := cm.peerConn.SetLocalDescription(offer); err != nil {
		return nil, fmt.Errorf("设置本地描述失败: %v", err)
	}

	log.Println("[WEBRTC] 创建并设置Offer成功")
	return &offer, nil
}

// CreateAnswer 创建SDP Answer
func (cm *ConnectionManager) CreateAnswer() (*webrtc.SessionDescription, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.peerConn == nil {
		return nil, fmt.Errorf("PeerConnection未创建")
	}

	answer, err := cm.peerConn.CreateAnswer(nil)
	if err != nil {
		return nil, fmt.Errorf("创建Answer失败: %v", err)
	}

	if err := cm.peerConn.SetLocalDescription(answer); err != nil {
		return nil, fmt.Errorf("设置本地描述失败: %v", err)
	}

	log.Println("[WEBRTC] 创建并设置Answer成功")
	return &answer, nil
}

// SetRemoteDescription 设置远程SDP描述
func (cm *ConnectionManager) SetRemoteDescription(sdp webrtc.SessionDescription) error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.peerConn == nil {
		return fmt.Errorf("PeerConnection未创建")
	}

	if err := cm.peerConn.SetRemoteDescription(sdp); err != nil {
		return fmt.Errorf("设置远程描述失败: %v", err)
	}

	log.Printf("[WEBRTC] 设置远程%s描述成功", sdp.Type)
	return nil
}

// AddICECandidate 添加ICE候选
func (cm *ConnectionManager) AddICECandidate(candidate webrtc.ICECandidateInit) error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.peerConn == nil {
		return fmt.Errorf("PeerConnection未创建")
	}

	if err := cm.peerConn.AddICECandidate(candidate); err != nil {
		return fmt.Errorf("添加ICE候选失败: %v", err)
	}

	log.Println("[WEBRTC] 添加ICE候选成功")
	return nil
}

// SendDataChannelMessage 发送数据通道消息
func (cm *ConnectionManager) SendDataChannelMessage(data []byte) error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.dataChannel == nil {
		return fmt.Errorf("数据通道未创建")
	}

	if cm.dataChannel.ReadyState() != webrtc.DataChannelStateOpen {
		return fmt.Errorf("数据通道未打开")
	}

	return cm.dataChannel.Send(data)
}

// IsDataChannelOpen 检查数据通道是否打开
func (cm *ConnectionManager) IsDataChannelOpen() bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.dataChannel == nil {
		return false
	}

	return cm.dataChannel.ReadyState() == webrtc.DataChannelStateOpen
}

// GetConnectionState 获取连接状态
func (cm *ConnectionManager) GetConnectionState() webrtc.PeerConnectionState {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if cm.peerConn == nil {
		return webrtc.PeerConnectionStateNew
	}

	return cm.peerConn.ConnectionState()
}

// GetStats 获取连接统计信息
func (cm *ConnectionManager) GetStats() ConnectionStats {
	cm.stats.mutex.RLock()
	defer cm.stats.mutex.RUnlock()

	// 返回副本
	return cm.stats
}

// Close 关闭连接
func (cm *ConnectionManager) Close() {
	log.Println("[WEBRTC] 开始关闭WebRTC连接...")

	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 取消上下文，停止所有goroutine
	cm.cancel()

	// 关闭数据通道
	if cm.dataChannel != nil {
		log.Println("[WEBRTC] 关闭数据通道...")
		cm.dataChannel.Close()
		cm.dataChannel = nil
	}

	// 关闭PeerConnection
	if cm.peerConn != nil {
		log.Println("[WEBRTC] 关闭PeerConnection...")
		cm.peerConn.Close()
		cm.peerConn = nil
	}

	// 清理轨道引用
	cm.localVideoTrack = nil
	cm.localAudioTrack = nil
	cm.remoteVideoTrack = nil
	cm.remoteAudioTrack = nil

	// 清理回调函数，避免回调中的goroutine继续运行
	cm.onConnectionStateChange = nil
	cm.onICECandidate = nil
	cm.onTrack = nil
	cm.onDataChannel = nil
	cm.onDataChannelMessage = nil

	log.Println("[WEBRTC] ✅ WebRTC连接已完全关闭")
}

// SetCallbacks 设置回调函数
func (cm *ConnectionManager) SetCallbacks(
	onConnectionStateChange func(webrtc.PeerConnectionState),
	onICECandidate func(*webrtc.ICECandidate),
	onTrack func(*webrtc.TrackRemote, *webrtc.RTPReceiver),
	onDataChannel func(*webrtc.DataChannel),
	onDataChannelMessage func(webrtc.DataChannelMessage),
) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.onConnectionStateChange = onConnectionStateChange
	cm.onICECandidate = onICECandidate
	cm.onTrack = onTrack
	cm.onDataChannel = onDataChannel
	cm.onDataChannelMessage = onDataChannelMessage
}

// setupEventHandlers 设置事件处理器
func (cm *ConnectionManager) setupEventHandlers() {
	// 连接状态变化
	cm.peerConn.OnConnectionStateChange(func(state webrtc.PeerConnectionState) {
		log.Printf("[WEBRTC] 连接状态变化: %s", state.String())

		if state == webrtc.PeerConnectionStateConnected {
			cm.stats.mutex.Lock()
			cm.stats.ConnectedAt = time.Now()
			cm.stats.mutex.Unlock()
		}

		if cm.onConnectionStateChange != nil {
			go cm.onConnectionStateChange(state)
		}
	})

	// ICE候选
	cm.peerConn.OnICECandidate(func(candidate *webrtc.ICECandidate) {
		if candidate != nil {
			log.Printf("[WEBRTC] 生成ICE候选: %s", candidate.String())
			if cm.onICECandidate != nil {
				go cm.onICECandidate(candidate)
			}
		}
	})

	// 远程轨道
	cm.peerConn.OnTrack(func(track *webrtc.TrackRemote, receiver *webrtc.RTPReceiver) {
		log.Printf("[WEBRTC] 接收到远程轨道: %s (类型: %s)", track.ID(), track.Kind())

		cm.mutex.Lock()
		if track.Kind() == webrtc.RTPCodecTypeVideo {
			cm.remoteVideoTrack = track
		} else if track.Kind() == webrtc.RTPCodecTypeAudio {
			cm.remoteAudioTrack = track
		}
		cm.mutex.Unlock()

		if cm.onTrack != nil {
			go cm.onTrack(track, receiver)
		}
	})

	// 数据通道
	cm.peerConn.OnDataChannel(func(dc *webrtc.DataChannel) {
		log.Printf("[WEBRTC] 接收到数据通道: %s", dc.Label())

		if cm.onDataChannel != nil {
			go cm.onDataChannel(dc)
		}

		// 设置数据通道消息处理
		dc.OnMessage(func(msg webrtc.DataChannelMessage) {
			if cm.onDataChannelMessage != nil {
				go cm.onDataChannelMessage(msg)
			}
		})
	})

	// ICE连接状态变化
	cm.peerConn.OnICEConnectionStateChange(func(state webrtc.ICEConnectionState) {
		log.Printf("[WEBRTC] ICE连接状态变化: %s", state.String())
	})

	// 信令状态变化
	cm.peerConn.OnSignalingStateChange(func(state webrtc.SignalingState) {
		log.Printf("[WEBRTC] 信令状态变化: %s", state.String())
	})
}

// createDataChannel 创建数据通道
func (cm *ConnectionManager) createDataChannel() error {
	options := &webrtc.DataChannelInit{
		Ordered: &[]bool{true}[0],
	}

	dc, err := cm.peerConn.CreateDataChannel("videocall", options)
	if err != nil {
		return fmt.Errorf("创建数据通道失败: %v", err)
	}

	cm.dataChannel = dc

	// 设置数据通道事件处理
	dc.OnOpen(func() {
		log.Println("[WEBRTC] ✅ 数据通道已打开，可以开始发送视频数据")
	})

	dc.OnClose(func() {
		log.Println("[WEBRTC] ❌ 数据通道已关闭")
	})

	// 添加数据通道状态变化监听
	go func() {
		for {
			state := dc.ReadyState()
			log.Printf("[WEBRTC] 数据通道状态: %s", state.String())

			if state == webrtc.DataChannelStateOpen {
				log.Println("[WEBRTC] 🎉 数据通道已就绪，视频传输可以开始")
				break
			} else if state == webrtc.DataChannelStateClosed {
				log.Println("[WEBRTC] 💔 数据通道已关闭，停止监听")
				break
			}

			time.Sleep(500 * time.Millisecond)
		}
	}()

	dc.OnMessage(func(msg webrtc.DataChannelMessage) {
		if cm.onDataChannelMessage != nil {
			go cm.onDataChannelMessage(msg)
		}
	})

	log.Println("[WEBRTC] 数据通道创建成功")
	return nil
}
