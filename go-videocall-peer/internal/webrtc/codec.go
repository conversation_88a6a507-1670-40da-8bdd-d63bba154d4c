package webrtc

import (
	"fmt"

	"github.com/pion/webrtc/v4"
)

// registerESP32Codecs 注册ESP32兼容的编解码器
func registerESP32Codecs(mediaEngine *webrtc.MediaEngine) error {
	// 按照ESP32的顺序注册编解码器：先音频，后视频
	
	// 1. PCMA音频编解码器 - 与ESP32兼容（优先级最高）
	if err := mediaEngine.RegisterCodec(webrtc.RTPCodecParameters{
		RTPCodecCapability: webrtc.RTPCodecCapability{
			MimeType:  webrtc.MimeTypePCMA,
			ClockRate: 8000,
			Channels:  1,
		},
		PayloadType: 8,
	}, webrtc.RTPCodecTypeAudio); err != nil {
		return fmt.Errorf("注册PCMA编解码器失败: %v", err)
	}
	
	// 2. PCMU音频编解码器 - 备用音频编解码器
	if err := mediaEngine.RegisterCodec(webrtc.RTPCodecParameters{
		RTPCodecCapability: webrtc.RTPCodecCapability{
			MimeType:  webrtc.MimeTypePCMU,
			ClockRate: 8000,
			Channels:  1,
		},
		PayloadType: 0,
	}, webrtc.RTPCodecTypeAudio); err != nil {
		return fmt.Errorf("注册PCMU编解码器失败: %v", err)
	}
	
	// 3. H.264视频编解码器 - 与ESP32兼容的配置（packetization-mode=1）
	if err := mediaEngine.RegisterCodec(webrtc.RTPCodecParameters{
		RTPCodecCapability: webrtc.RTPCodecCapability{
			MimeType:    webrtc.MimeTypeH264,
			ClockRate:   90000,
			Channels:    0,
			SDPFmtpLine: "level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f",
		},
		PayloadType: 102,
	}, webrtc.RTPCodecTypeVideo); err != nil {
		return fmt.Errorf("注册H.264编解码器(mode 1)失败: %v", err)
	}
	
	// 4. 添加第二个H.264编解码器配置（packetization-mode=0）
	if err := mediaEngine.RegisterCodec(webrtc.RTPCodecParameters{
		RTPCodecCapability: webrtc.RTPCodecCapability{
			MimeType:    webrtc.MimeTypeH264,
			ClockRate:   90000,
			Channels:    0,
			SDPFmtpLine: "level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f",
		},
		PayloadType: 106,
	}, webrtc.RTPCodecTypeVideo); err != nil {
		return fmt.Errorf("注册H.264编解码器(mode 0)失败: %v", err)
	}
	
	// 5. MJPEG视频编解码器 - ESP32 videocall_demo使用
	if err := mediaEngine.RegisterCodec(webrtc.RTPCodecParameters{
		RTPCodecCapability: webrtc.RTPCodecCapability{
			MimeType:  "video/JPEG",
			ClockRate: 90000,
			Channels:  0,
		},
		PayloadType: 26,
	}, webrtc.RTPCodecTypeVideo); err != nil {
		return fmt.Errorf("注册MJPEG编解码器失败: %v", err)
	}
	
	return nil
}

// GetSupportedVideoCodecs 获取支持的视频编解码器列表
func GetSupportedVideoCodecs() []string {
	return []string{
		"H264",
		"MJPEG",
	}
}

// GetSupportedAudioCodecs 获取支持的音频编解码器列表
func GetSupportedAudioCodecs() []string {
	return []string{
		"PCMA",
		"PCMU",
	}
}

// GetPreferredVideoCodec 获取首选视频编解码器
func GetPreferredVideoCodec() string {
	return "H264"
}

// GetPreferredAudioCodec 获取首选音频编解码器
func GetPreferredAudioCodec() string {
	return "PCMA"
}

// ValidateCodecConfig 验证编解码器配置
func ValidateCodecConfig(videoCodec, audioCodec string) error {
	// 验证视频编解码器
	supportedVideo := GetSupportedVideoCodecs()
	videoSupported := false
	for _, codec := range supportedVideo {
		if codec == videoCodec {
			videoSupported = true
			break
		}
	}
	if !videoSupported {
		return fmt.Errorf("不支持的视频编解码器: %s", videoCodec)
	}
	
	// 验证音频编解码器
	supportedAudio := GetSupportedAudioCodecs()
	audioSupported := false
	for _, codec := range supportedAudio {
		if codec == audioCodec {
			audioSupported = true
			break
		}
	}
	if !audioSupported {
		return fmt.Errorf("不支持的音频编解码器: %s", audioCodec)
	}
	
	return nil
}

// GetCodecMimeType 获取编解码器的MIME类型
func GetCodecMimeType(codec string, isVideo bool) string {
	if isVideo {
		switch codec {
		case "H264":
			return webrtc.MimeTypeH264
		case "MJPEG":
			return "video/JPEG"
		default:
			return webrtc.MimeTypeH264 // 默认
		}
	} else {
		switch codec {
		case "PCMA":
			return webrtc.MimeTypePCMA
		case "PCMU":
			return webrtc.MimeTypePCMU
		default:
			return webrtc.MimeTypePCMA // 默认
		}
	}
}

// GetCodecClockRate 获取编解码器的时钟频率
func GetCodecClockRate(codec string, isVideo bool) uint32 {
	if isVideo {
		return 90000 // 视频编解码器标准时钟频率
	} else {
		return 8000 // 音频编解码器时钟频率（G.711）
	}
}

// GetCodecChannels 获取编解码器的声道数
func GetCodecChannels(codec string, isVideo bool) uint16 {
	if isVideo {
		return 0 // 视频没有声道概念
	} else {
		return 1 // 单声道音频
	}
}

// BuildH264SDPFmtpLine 构建H.264的SDP fmtp行
func BuildH264SDPFmtpLine(packetizationMode int, profileLevelID string, levelAsymmetryAllowed bool) string {
	fmtp := fmt.Sprintf("packetization-mode=%d;profile-level-id=%s", packetizationMode, profileLevelID)
	if levelAsymmetryAllowed {
		fmtp += ";level-asymmetry-allowed=1"
	}
	return fmtp
}

// GetDefaultH264Config 获取默认的H.264配置
func GetDefaultH264Config() map[string]interface{} {
	return map[string]interface{}{
		"packetization_mode":       1,
		"profile_level_id":         "4d001f",
		"level_asymmetry_allowed":  true,
		"max_mbps":                 40500,
		"max_fs":                   1344,
		"max_cpb":                  20000,
		"max_dpb":                  20000,
		"max_br":                   10000,
	}
}

// GetESP32CompatibleConfig 获取ESP32兼容的编解码器配置
func GetESP32CompatibleConfig() map[string]interface{} {
	return map[string]interface{}{
		"video": map[string]interface{}{
			"codec":              "H264",
			"profile":            "baseline",
			"level":              "3.1",
			"packetization_mode": 1,
			"profile_level_id":   "4d001f",
			"max_bitrate":        500000, // 500kbps
			"max_framerate":      15,
			"max_width":          640,
			"max_height":         480,
		},
		"audio": map[string]interface{}{
			"codec":       "PCMA",
			"sample_rate": 8000,
			"channels":    1,
			"bitrate":     64000, // 64kbps
			"frame_size":  160,   // 20ms at 8kHz
		},
	}
}
