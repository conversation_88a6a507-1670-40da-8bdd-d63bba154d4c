package media

import (
	"fmt"
	"log"
	"os"
	"sync"

	"github.com/pion/rtp"
	"go-videocall-peer/config"
)

// AudioProcessor 音频处理器
type AudioProcessor struct {
	config *config.AudioPlaybackConfig
	mutex  sync.RWMutex

	// 文件保存
	saveFile   *os.File
	saveToFile bool

	// 统计信息
	packetsReceived uint64
	bytesWritten    uint64
	lastSequence    uint16
}

// NewAudioProcessor 创建新的音频处理器
func NewAudioProcessor(cfg *config.AudioPlaybackConfig) (*AudioProcessor, error) {
	processor := &AudioProcessor{
		config:     cfg,
		saveToFile: cfg.SaveToFile,
	}

	// 初始化文件保存
	if processor.saveToFile && cfg.SavePath != "" {
		file, err := os.Create(cfg.SavePath)
		if err != nil {
			return nil, fmt.Errorf("创建音频保存文件失败: %v", err)
		}
		processor.saveFile = file
		log.Printf("[AUDIO] 音频将保存到: %s", cfg.SavePath)
	}

	return processor, nil
}

// ProcessRTPPacket 处理RTP包
func (a *AudioProcessor) ProcessRTPPacket(packet *rtp.Packet) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	a.packetsReceived++

	// 检查包序列号
	if a.packetsReceived > 1 {
		expectedSeq := a.lastSequence + 1
		if packet.SequenceNumber != expectedSeq {
			log.Printf("[AUDIO] 包序列号不连续: 期望=%d, 实际=%d", expectedSeq, packet.SequenceNumber)
		}
	}
	a.lastSequence = packet.SequenceNumber

	// 保存PCMA音频数据到文件
	if a.saveToFile && a.saveFile != nil {
		n, err := a.saveFile.Write(packet.Payload)
		if err != nil {
			return fmt.Errorf("写入音频文件失败: %v", err)
		}
		a.bytesWritten += uint64(n)

		// 每1000个包打印一次日志
		if a.packetsReceived%1000 == 0 {
			log.Printf("[AUDIO] 已保存音频包: %d, 总字节: %d", a.packetsReceived, a.bytesWritten)
		}
	}

	return nil
}

// GetStats 获取统计信息
func (a *AudioProcessor) GetStats() (packetsReceived, bytesWritten uint64) {
	a.mutex.RLock()
	defer a.mutex.RUnlock()
	return a.packetsReceived, a.bytesWritten
}

// Close 关闭处理器
func (a *AudioProcessor) Close() error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if a.saveFile != nil {
		if err := a.saveFile.Close(); err != nil {
			return fmt.Errorf("关闭音频保存文件失败: %v", err)
		}
		log.Printf("[AUDIO] 音频文件已保存: %d bytes", a.bytesWritten)
	}

	return nil
}
