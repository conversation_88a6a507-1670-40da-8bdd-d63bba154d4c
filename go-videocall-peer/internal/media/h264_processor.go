package media

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"sync"

	"github.com/pion/rtp"
	"go-videocall-peer/config"
)

// H264Processor H.264流处理器
type H264Processor struct {
	config *config.VideoPlaybackConfig
	mutex  sync.RWMutex

	// 文件保存
	saveFile   *os.File
	saveToFile bool

	// ffplay播放
	ffplayCmd   *exec.Cmd
	ffplayStdin io.WriteCloser
	useFFplay   bool

	// RTP处理
	sequenceBuffer map[uint16][]byte
	lastSequence   uint16
	nalBuffer      bytes.Buffer

	// 统计信息
	packetsReceived uint64
	framesProcessed uint64
	bytesWritten    uint64
}

// NewH264Processor 创建新的H.264处理器
func NewH264Processor(cfg *config.VideoPlaybackConfig) (*H264Processor, error) {
	processor := &H264Processor{
		config:         cfg,
		saveToFile:     cfg.SaveToFile,
		useFFplay:      cfg.UseFFplay,
		sequenceBuffer: make(map[uint16][]byte),
	}

	// 初始化文件保存
	if processor.saveToFile && cfg.SavePath != "" {
		file, err := os.Create(cfg.SavePath)
		if err != nil {
			return nil, fmt.Errorf("创建视频保存文件失败: %v", err)
		}
		processor.saveFile = file
		log.Printf("[H264] 视频将保存到: %s", cfg.SavePath)
	}

	// 初始化ffplay
	if processor.useFFplay {
		if err := processor.startFFplay(); err != nil {
			log.Printf("[H264] 启动ffplay失败: %v", err)
			processor.useFFplay = false
		}
	}

	return processor, nil
}

// ProcessRTPPacket 处理RTP包
func (h *H264Processor) ProcessRTPPacket(packet *rtp.Packet) error {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.packetsReceived++

	// 检查包序列号
	if h.packetsReceived > 1 {
		expectedSeq := h.lastSequence + 1
		if packet.SequenceNumber != expectedSeq {
			log.Printf("[H264] 包序列号不连续: 期望=%d, 实际=%d", expectedSeq, packet.SequenceNumber)
		}
	}
	h.lastSequence = packet.SequenceNumber

	// 解析H.264 RTP负载
	nalUnits, err := h.parseH264RTPPayload(packet.Payload)
	if err != nil {
		return fmt.Errorf("解析H.264 RTP负载失败: %v", err)
	}

	// 处理每个NAL单元
	for _, nalUnit := range nalUnits {
		if err := h.processNALUnit(nalUnit); err != nil {
			log.Printf("[H264] 处理NAL单元失败: %v", err)
		}
	}

	return nil
}

// parseH264RTPPayload 解析H.264 RTP负载
func (h *H264Processor) parseH264RTPPayload(payload []byte) ([][]byte, error) {
	if len(payload) == 0 {
		return nil, fmt.Errorf("空的RTP负载")
	}

	var nalUnits [][]byte

	// 检查NAL单元类型
	nalType := payload[0] & 0x1F

	switch nalType {
	case 1, 5: // 单个NAL单元 (P帧或IDR帧)
		nalUnits = append(nalUnits, payload)

	case 7: // SPS
		nalUnits = append(nalUnits, payload)

	case 8: // PPS
		nalUnits = append(nalUnits, payload)

	case 24: // STAP-A (单时间聚合包)
		nalUnits = h.parseSTAPA(payload)

	case 28: // FU-A (分片单元)
		nalUnit := h.parseFUA(payload)
		if nalUnit != nil {
			nalUnits = append(nalUnits, nalUnit)
		}

	default:
		log.Printf("[H264] 未知NAL类型: %d", nalType)
		nalUnits = append(nalUnits, payload)
	}

	return nalUnits, nil
}

// parseSTAPA 解析STAP-A包
func (h *H264Processor) parseSTAPA(payload []byte) [][]byte {
	var nalUnits [][]byte
	offset := 1 // 跳过STAP-A头

	for offset < len(payload) {
		if offset+2 > len(payload) {
			break
		}

		// 读取NAL单元大小
		nalSize := int(payload[offset])<<8 | int(payload[offset+1])
		offset += 2

		if offset+nalSize > len(payload) {
			break
		}

		// 提取NAL单元
		nalUnit := payload[offset : offset+nalSize]
		nalUnits = append(nalUnits, nalUnit)
		offset += nalSize
	}

	return nalUnits
}

// parseFUA 解析FU-A包
func (h *H264Processor) parseFUA(payload []byte) []byte {
	if len(payload) < 2 {
		return nil
	}

	fuIndicator := payload[0]
	fuHeader := payload[1]

	start := (fuHeader & 0x80) != 0
	end := (fuHeader & 0x40) != 0
	nalType := fuHeader & 0x1F

	if start {
		// 开始分片，重建NAL头
		nalHeader := (fuIndicator & 0xE0) | nalType
		h.nalBuffer.Reset()
		h.nalBuffer.WriteByte(nalHeader)
		h.nalBuffer.Write(payload[2:])
	} else if end {
		// 结束分片
		h.nalBuffer.Write(payload[2:])
		nalUnit := make([]byte, h.nalBuffer.Len())
		copy(nalUnit, h.nalBuffer.Bytes())
		h.nalBuffer.Reset()
		return nalUnit
	} else {
		// 中间分片
		h.nalBuffer.Write(payload[2:])
	}

	return nil
}

// processNALUnit 处理NAL单元
func (h *H264Processor) processNALUnit(nalUnit []byte) error {
	if len(nalUnit) == 0 {
		return nil
	}

	nalType := nalUnit[0] & 0x1F

	// 添加起始码
	nalWithStartCode := append([]byte{0x00, 0x00, 0x00, 0x01}, nalUnit...)

	// 保存到文件
	if h.saveToFile && h.saveFile != nil {
		n, err := h.saveFile.Write(nalWithStartCode)
		if err != nil {
			return fmt.Errorf("写入文件失败: %v", err)
		}
		h.bytesWritten += uint64(n)
	}

	// 发送到ffplay
	if h.useFFplay && h.ffplayStdin != nil {
		_, err := h.ffplayStdin.Write(nalWithStartCode)
		if err != nil {
			log.Printf("[H264] 写入ffplay失败: %v", err)
			h.useFFplay = false
		}
	}

	// 统计关键帧
	if nalType == 5 || nalType == 7 || nalType == 8 {
		h.framesProcessed++
		nalTypeStr := "Unknown"
		switch nalType {
		case 5:
			nalTypeStr = "IDR"
		case 7:
			nalTypeStr = "SPS"
		case 8:
			nalTypeStr = "PPS"
		}
		log.Printf("[H264] 处理%s帧, 大小: %d bytes", nalTypeStr, len(nalWithStartCode))
	}

	return nil
}

// startFFplay 启动ffplay进程
func (h *H264Processor) startFFplay() error {
	args := h.config.FFplayArgs
	if len(args) == 0 {
		args = []string{"-f", "h264", "-framerate", "15", "-"}
	}

	h.ffplayCmd = exec.Command(h.config.FFplayPath, args...)
	
	stdin, err := h.ffplayCmd.StdinPipe()
	if err != nil {
		return fmt.Errorf("创建ffplay stdin管道失败: %v", err)
	}
	h.ffplayStdin = stdin

	if err := h.ffplayCmd.Start(); err != nil {
		return fmt.Errorf("启动ffplay失败: %v", err)
	}

	log.Printf("[H264] ffplay已启动: %s %v", h.config.FFplayPath, args)
	return nil
}

// GetStats 获取统计信息
func (h *H264Processor) GetStats() (packetsReceived, framesProcessed, bytesWritten uint64) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.packetsReceived, h.framesProcessed, h.bytesWritten
}

// Close 关闭处理器
func (h *H264Processor) Close() error {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	var errors []error

	// 关闭文件
	if h.saveFile != nil {
		if err := h.saveFile.Close(); err != nil {
			errors = append(errors, fmt.Errorf("关闭保存文件失败: %v", err))
		}
		log.Printf("[H264] 视频文件已保存: %d bytes", h.bytesWritten)
	}

	// 关闭ffplay
	if h.ffplayStdin != nil {
		h.ffplayStdin.Close()
	}
	if h.ffplayCmd != nil && h.ffplayCmd.Process != nil {
		h.ffplayCmd.Process.Kill()
		h.ffplayCmd.Wait()
		log.Println("[H264] ffplay已关闭")
	}

	if len(errors) > 0 {
		return fmt.Errorf("关闭时发生错误: %v", errors)
	}

	return nil
}
