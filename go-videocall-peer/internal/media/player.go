package media

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"go-videocall-peer/config"

	"github.com/pion/rtp"
	"github.com/pion/webrtc/v4"
)

// MediaPlayer 媒体播放器
type MediaPlayer struct {
	config *config.Config
	ctx    context.Context
	cancel context.CancelFunc
	mutex  sync.RWMutex

	// 播放状态
	playing bool

	// 远程轨道
	remoteVideoTrack *webrtc.TrackRemote
	remoteAudioTrack *webrtc.TrackRemote

	// 媒体处理器
	h264Processor  *H264Processor
	jpegProcessor  *JPEGProcessor
	audioProcessor *AudioProcessor

	// 统计信息
	stats PlayerStats
}

// PlayerStats 播放器统计信息
type PlayerStats struct {
	VideoFrames   uint64
	AudioFrames   uint64
	VideoBytes    uint64
	AudioBytes    uint64
	DroppedFrames uint64
	Errors        uint64
	StartTime     time.Time
	LastFrameTime time.Time
	FPS           float64
	mutex         sync.RWMutex
}

// NewMediaPlayer 创建新的媒体播放器
func NewMediaPlayer(cfg *config.Config) *MediaPlayer {
	ctx, cancel := context.WithCancel(context.Background())

	player := &MediaPlayer{
		config: cfg,
		ctx:    ctx,
		cancel: cancel,
	}

	// 初始化H.264处理器
	if cfg.Playback.Video.Enable {
		h264Processor, err := NewH264Processor(&cfg.Playback.Video)
		if err != nil {
			log.Printf("[PLAYER] 创建H.264处理器失败: %v", err)
		} else {
			player.h264Processor = h264Processor
		}

		// 初始化JPEG处理器
		jpegProcessor, err := NewJPEGProcessor(&cfg.Playback.Video)
		if err != nil {
			log.Printf("[PLAYER] 创建JPEG处理器失败: %v", err)
		} else {
			player.jpegProcessor = jpegProcessor
		}
	}

	// 初始化音频处理器
	if cfg.Playback.Audio.Enable {
		audioProcessor, err := NewAudioProcessor(&cfg.Playback.Audio)
		if err != nil {
			log.Printf("[PLAYER] 创建音频处理器失败: %v", err)
		} else {
			player.audioProcessor = audioProcessor
		}
	}

	return player
}

// SetRemoteVideoTrack 设置远程视频轨道
func (mp *MediaPlayer) SetRemoteVideoTrack(track *webrtc.TrackRemote) {
	mp.mutex.Lock()
	defer mp.mutex.Unlock()

	mp.remoteVideoTrack = track
	log.Printf("[PLAYER] 设置远程视频轨道: %s (类型: %s, 编解码器: %s, 播放状态: %t)",
		track.ID(), track.Kind(), track.Codec().MimeType, mp.playing)

	// 无论播放状态如何，都启动视频播放协程
	go mp.playVideoTrack(track)
}

// SetRemoteAudioTrack 设置远程音频轨道
func (mp *MediaPlayer) SetRemoteAudioTrack(track *webrtc.TrackRemote) {
	mp.mutex.Lock()
	defer mp.mutex.Unlock()

	mp.remoteAudioTrack = track
	log.Printf("[PLAYER] 设置远程音频轨道: %s (类型: %s, 编解码器: %s, 播放状态: %t)",
		track.ID(), track.Kind(), track.Codec().MimeType, mp.playing)

	// 无论播放状态如何，都启动音频播放协程
	go mp.playAudioTrack(track)
}

// StartPlayback 开始播放
func (mp *MediaPlayer) StartPlayback() error {
	mp.mutex.Lock()
	defer mp.mutex.Unlock()

	if mp.playing {
		return fmt.Errorf("已经在播放中")
	}

	mp.playing = true
	mp.stats.StartTime = time.Now()

	// 启动现有轨道的播放
	if mp.remoteVideoTrack != nil {
		go mp.playVideoTrack(mp.remoteVideoTrack)
	}

	if mp.remoteAudioTrack != nil {
		go mp.playAudioTrack(mp.remoteAudioTrack)
	}

	log.Println("[PLAYER] 开始媒体播放")
	return nil
}

// StopPlayback 停止播放
func (mp *MediaPlayer) StopPlayback() {
	mp.mutex.Lock()
	defer mp.mutex.Unlock()

	if !mp.playing {
		return
	}

	mp.playing = false
	mp.cancel()

	log.Println("[PLAYER] 停止媒体播放")
}

// GetStats 获取播放统计信息
func (mp *MediaPlayer) GetStats() PlayerStats {
	mp.stats.mutex.RLock()
	defer mp.stats.mutex.RUnlock()

	// 计算FPS
	if mp.stats.VideoFrames > 0 && !mp.stats.StartTime.IsZero() {
		duration := time.Since(mp.stats.StartTime).Seconds()
		mp.stats.FPS = float64(mp.stats.VideoFrames) / duration
	}

	return mp.stats
}

// Close 关闭播放器
func (mp *MediaPlayer) Close() {
	mp.StopPlayback()

	// 关闭处理器
	if mp.h264Processor != nil {
		if err := mp.h264Processor.Close(); err != nil {
			log.Printf("[PLAYER] 关闭H.264处理器失败: %v", err)
		}
	}

	if mp.jpegProcessor != nil {
		if err := mp.jpegProcessor.Close(); err != nil {
			log.Printf("[PLAYER] 关闭JPEG处理器失败: %v", err)
		}
	}

	if mp.audioProcessor != nil {
		if err := mp.audioProcessor.Close(); err != nil {
			log.Printf("[PLAYER] 关闭音频处理器失败: %v", err)
		}
	}

	log.Println("[PLAYER] 媒体播放器已关闭")
}

// playVideoTrack 播放视频轨道
func (mp *MediaPlayer) playVideoTrack(track *webrtc.TrackRemote) {
	log.Printf("[PLAYER] 开始播放视频轨道: %s (编解码器: %s)", track.ID(), track.Codec().MimeType)

	packetCount := uint64(0)
	for {
		select {
		case <-mp.ctx.Done():
			log.Printf("[PLAYER] 视频轨道播放协程退出: %s", track.ID())
			return
		default:
			// 读取RTP包
			packet, _, err := track.ReadRTP()
			if err != nil {
				log.Printf("[PLAYER] 读取视频RTP包失败: %v", err)
				mp.updateStats(false, true, 0, 0)
				time.Sleep(10 * time.Millisecond) // 避免忙等待
				continue
			}

			packetCount++

			// 每收到第一个包和每100个包打印一次详细日志
			if packetCount == 1 || packetCount%100 == 0 {
				log.Printf("[PLAYER] 收到视频包 #%d: 序号=%d, 时间戳=%d, 大小=%d, 负载类型=%d",
					packetCount, packet.SequenceNumber, packet.Timestamp, len(packet.Payload), packet.PayloadType)
			}

			// 处理视频包
			if err := mp.processVideoPacket(packet); err != nil {
				log.Printf("[PLAYER] 处理视频包失败: %v", err)
				mp.updateStats(false, true, 0, 0)
				continue
			}

			// 更新统计信息
			mp.updateStats(true, false, uint64(len(packet.Payload)), 0)
		}
	}
}

// playAudioTrack 播放音频轨道
func (mp *MediaPlayer) playAudioTrack(track *webrtc.TrackRemote) {
	log.Printf("[PLAYER] 开始播放音频轨道: %s (编解码器: %s)", track.ID(), track.Codec().MimeType)

	packetCount := uint64(0)
	for {
		select {
		case <-mp.ctx.Done():
			log.Printf("[PLAYER] 音频轨道播放协程退出: %s", track.ID())
			return
		default:
			// 读取RTP包
			packet, _, err := track.ReadRTP()
			if err != nil {
				log.Printf("[PLAYER] 读取音频RTP包失败: %v", err)
				mp.updateStats(false, true, 0, 0)
				time.Sleep(10 * time.Millisecond) // 避免忙等待
				continue
			}

			packetCount++

			// 每收到第一个包和每500个包打印一次详细日志
			if packetCount == 1 || packetCount%500 == 0 {
				log.Printf("[PLAYER] 收到音频包 #%d: 序号=%d, 时间戳=%d, 大小=%d, 负载类型=%d",
					packetCount, packet.SequenceNumber, packet.Timestamp, len(packet.Payload), packet.PayloadType)
			}

			// 处理音频包
			if err := mp.processAudioPacket(packet); err != nil {
				log.Printf("[PLAYER] 处理音频包失败: %v", err)
				mp.updateStats(false, true, 0, 0)
				continue
			}

			// 更新统计信息
			mp.updateStats(false, false, 0, uint64(len(packet.Payload)))
		}
	}
}

// processVideoPacket 处理视频包
func (mp *MediaPlayer) processVideoPacket(packet *rtp.Packet) error {
	// 使用H.264处理器处理视频包
	if mp.h264Processor != nil {
		if err := mp.h264Processor.ProcessRTPPacket(packet); err != nil {
			return fmt.Errorf("H.264处理器处理失败: %v", err)
		}
	}

	// 每100个包打印一次日志
	if packet.SequenceNumber%100 == 0 {
		log.Printf("[PLAYER] 收到视频包: 序号=%d, 时间戳=%d, 大小=%d",
			packet.SequenceNumber, packet.Timestamp, len(packet.Payload))
	}

	return nil
}

// processAudioPacket 处理音频包
func (mp *MediaPlayer) processAudioPacket(packet *rtp.Packet) error {
	// 使用音频处理器处理音频包
	if mp.audioProcessor != nil {
		if err := mp.audioProcessor.ProcessRTPPacket(packet); err != nil {
			return fmt.Errorf("音频处理器处理失败: %v", err)
		}
	}

	// 每500个包打印一次日志
	if packet.SequenceNumber%500 == 0 {
		log.Printf("[PLAYER] 收到音频包: 序号=%d, 时间戳=%d, 大小=%d",
			packet.SequenceNumber, packet.Timestamp, len(packet.Payload))
	}

	return nil
}

// updateStats 更新统计信息
func (mp *MediaPlayer) updateStats(isVideo, isError bool, videoBytes, audioBytes uint64) {
	mp.stats.mutex.Lock()
	defer mp.stats.mutex.Unlock()

	now := time.Now()

	if isError {
		mp.stats.Errors++
		return
	}

	if isVideo {
		mp.stats.VideoFrames++
		mp.stats.VideoBytes += videoBytes
		mp.stats.LastFrameTime = now

		// 计算FPS
		if !mp.stats.StartTime.IsZero() {
			duration := now.Sub(mp.stats.StartTime).Seconds()
			if duration > 0 {
				mp.stats.FPS = float64(mp.stats.VideoFrames) / duration
			}
		}
	} else {
		mp.stats.AudioFrames++
		mp.stats.AudioBytes += audioBytes
	}
}

// GetVideoStats 获取视频统计信息
func (mp *MediaPlayer) GetVideoStats() (frames, bytes uint64, fps float64) {
	mp.stats.mutex.RLock()
	defer mp.stats.mutex.RUnlock()

	return mp.stats.VideoFrames, mp.stats.VideoBytes, mp.stats.FPS
}

// GetAudioStats 获取音频统计信息
func (mp *MediaPlayer) GetAudioStats() (frames, bytes uint64) {
	mp.stats.mutex.RLock()
	defer mp.stats.mutex.RUnlock()

	return mp.stats.AudioFrames, mp.stats.AudioBytes
}

// ProcessDataChannelMessage 处理数据通道消息
func (mp *MediaPlayer) ProcessDataChannelMessage(msg webrtc.DataChannelMessage) error {
	// 尝试用JPEG处理器处理
	if mp.jpegProcessor != nil {
		if err := mp.jpegProcessor.ProcessDataChannelMessage(msg); err == nil {
			// 成功处理为JPEG，更新统计信息
			mp.updateStats(true, false, uint64(len(msg.Data)), 0)
			return nil
		}
	}

	// 如果不是JPEG，记录未知数据
	if len(msg.Data) > 0 {
		log.Printf("[PLAYER] 未知数据通道消息: %d bytes, 前4字节: %x",
			len(msg.Data), msg.Data[:min(4, len(msg.Data))])
	}

	return nil
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// IsPlaying 检查是否正在播放
func (mp *MediaPlayer) IsPlaying() bool {
	mp.mutex.RLock()
	defer mp.mutex.RUnlock()

	return mp.playing
}
