package media

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"sync"
	"time"

	"github.com/pion/webrtc/v4"
	"go-videocall-peer/config"
)

// JPEGProcessor JPEG图像处理器
type JPEGProcessor struct {
	config *config.VideoPlaybackConfig
	mutex  sync.RWMutex

	// 文件保存
	saveFile   *os.File
	saveToFile bool

	// ffplay播放
	ffplayCmd   *exec.Cmd
	ffplayStdin *os.File
	useFFplay   bool

	// 统计信息
	framesReceived uint64
	bytesWritten   uint64
	startTime      time.Time
}

// NewJPEGProcessor 创建新的JPEG处理器
func NewJPEGProcessor(cfg *config.VideoPlaybackConfig) (*JPEGProcessor, error) {
	processor := &JPEGProcessor{
		config:     cfg,
		saveToFile: cfg.SaveToFile,
		useFFplay:  cfg.UseFFplay,
		startTime:  time.Now(),
	}

	// 初始化文件保存（保存为MJPEG格式）
	if processor.saveToFile && cfg.SavePath != "" {
		// 将.h264扩展名改为.mjpeg
		savePath := cfg.SavePath
		if len(savePath) > 5 && savePath[len(savePath)-5:] == ".h264" {
			savePath = savePath[:len(savePath)-5] + ".mjpeg"
		}
		
		file, err := os.Create(savePath)
		if err != nil {
			return nil, fmt.Errorf("创建JPEG保存文件失败: %v", err)
		}
		processor.saveFile = file
		log.Printf("[JPEG] JPEG图像将保存到: %s", savePath)
	}

	// 初始化ffplay（播放MJPEG流）
	if processor.useFFplay {
		if err := processor.startFFplay(); err != nil {
			log.Printf("[JPEG] 启动ffplay失败: %v", err)
			processor.useFFplay = false
		}
	}

	return processor, nil
}

// ProcessDataChannelMessage 处理数据通道消息
func (j *JPEGProcessor) ProcessDataChannelMessage(msg webrtc.DataChannelMessage) error {
	j.mutex.Lock()
	defer j.mutex.Unlock()

	// 检查是否是JPEG数据
	if !j.isJPEGData(msg.Data) {
		return fmt.Errorf("不是JPEG数据")
	}

	j.framesReceived++

	// 每10帧打印一次日志
	if j.framesReceived%10 == 0 {
		fps := float64(j.framesReceived) / time.Since(j.startTime).Seconds()
		log.Printf("[JPEG] 接收JPEG帧 #%d, 大小: %d bytes, FPS: %.1f", 
			j.framesReceived, len(msg.Data), fps)
	}

	// 保存到文件
	if j.saveToFile && j.saveFile != nil {
		n, err := j.saveFile.Write(msg.Data)
		if err != nil {
			return fmt.Errorf("写入JPEG文件失败: %v", err)
		}
		j.bytesWritten += uint64(n)
	}

	// 发送到ffplay
	if j.useFFplay && j.ffplayStdin != nil {
		_, err := j.ffplayStdin.Write(msg.Data)
		if err != nil {
			log.Printf("[JPEG] 写入ffplay失败: %v", err)
			j.useFFplay = false
		}
	}

	return nil
}

// isJPEGData 检查数据是否为JPEG格式
func (j *JPEGProcessor) isJPEGData(data []byte) bool {
	// JPEG文件以 FF D8 开头，以 FF D9 结尾
	if len(data) < 4 {
		return false
	}
	
	// 检查JPEG文件头
	return data[0] == 0xFF && data[1] == 0xD8
}

// startFFplay 启动ffplay进程播放MJPEG流
func (j *JPEGProcessor) startFFplay() error {
	// 创建命名管道用于ffplay输入
	pipePath := "/tmp/videocall_mjpeg_pipe"
	
	// 删除可能存在的旧管道
	os.Remove(pipePath)
	
	// 创建命名管道
	if err := exec.Command("mkfifo", pipePath).Run(); err != nil {
		return fmt.Errorf("创建命名管道失败: %v", err)
	}

	// 启动ffplay播放MJPEG流
	args := []string{
		"-f", "mjpeg",
		"-framerate", "10",
		"-i", pipePath,
		"-window_title", "ESP32 Camera - MJPEG Stream",
	}

	j.ffplayCmd = exec.Command(j.config.FFplayPath, args...)
	
	if err := j.ffplayCmd.Start(); err != nil {
		os.Remove(pipePath)
		return fmt.Errorf("启动ffplay失败: %v", err)
	}

	// 打开管道用于写入
	file, err := os.OpenFile(pipePath, os.O_WRONLY, 0)
	if err != nil {
		j.ffplayCmd.Process.Kill()
		os.Remove(pipePath)
		return fmt.Errorf("打开管道失败: %v", err)
	}
	j.ffplayStdin = file

	log.Printf("[JPEG] ffplay已启动播放MJPEG流: %s %v", j.config.FFplayPath, args)
	return nil
}

// GetStats 获取统计信息
func (j *JPEGProcessor) GetStats() (framesReceived, bytesWritten uint64, fps float64) {
	j.mutex.RLock()
	defer j.mutex.RUnlock()
	
	elapsed := time.Since(j.startTime).Seconds()
	fps = float64(j.framesReceived) / elapsed
	
	return j.framesReceived, j.bytesWritten, fps
}

// Close 关闭处理器
func (j *JPEGProcessor) Close() error {
	j.mutex.Lock()
	defer j.mutex.Unlock()

	var errors []error

	// 关闭文件
	if j.saveFile != nil {
		if err := j.saveFile.Close(); err != nil {
			errors = append(errors, fmt.Errorf("关闭保存文件失败: %v", err))
		}
		log.Printf("[JPEG] JPEG文件已保存: %d 帧, %d bytes", j.framesReceived, j.bytesWritten)
	}

	// 关闭ffplay
	if j.ffplayStdin != nil {
		j.ffplayStdin.Close()
		os.Remove("/tmp/videocall_mjpeg_pipe")
	}
	if j.ffplayCmd != nil && j.ffplayCmd.Process != nil {
		j.ffplayCmd.Process.Kill()
		j.ffplayCmd.Wait()
		log.Println("[JPEG] ffplay已关闭")
	}

	if len(errors) > 0 {
		return fmt.Errorf("关闭时发生错误: %v", errors)
	}

	return nil
}

// SaveSingleJPEG 保存单个JPEG文件（用于调试）
func (j *JPEGProcessor) SaveSingleJPEG(data []byte, filename string) error {
	if !j.isJPEGData(data) {
		return fmt.Errorf("不是有效的JPEG数据")
	}

	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	_, err = file.Write(data)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	log.Printf("[JPEG] 单个JPEG文件已保存: %s (%d bytes)", filename, len(data))
	return nil
}
