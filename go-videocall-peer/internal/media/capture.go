package media

import (
	"context"
	"fmt"
	"log"
	"math"
	"os"
	"os/exec"
	"runtime"
	"sync"
	"time"

	"go-videocall-peer/config"

	"github.com/pion/webrtc/v4"
	"github.com/pion/webrtc/v4/pkg/media"
)

// DataChannelSender 数据通道发送器接口
type DataChannelSender interface {
	SendDataChannelMessage(data []byte) error
	IsDataChannelOpen() bool
}

// MediaCapture 简化的媒体捕获器
type MediaCapture struct {
	config *config.Config
	ctx    context.Context
	cancel context.CancelFunc
	mutex  sync.RWMutex

	// WebRTC轨道
	videoTrack *webrtc.TrackLocalStaticSample
	audioTrack *webrtc.TrackLocalStaticSample

	// 数据通道发送器（用于视频）
	dataChannelSender DataChannelSender

	// AstiAV摄像头捕获器
	astiavCamera *AstiAVCamera

	// 状态
	capturing bool

	// 统计信息
	stats CaptureStats
}

// CaptureStats 捕获统计信息
type CaptureStats struct {
	VideoFrames   uint64
	AudioFrames   uint64
	VideoBytes    uint64
	AudioBytes    uint64
	DroppedFrames uint64
	Errors        uint64
	StartTime     time.Time
	LastFrameTime time.Time
	FPS           float64
	mutex         sync.RWMutex
}

// NewMediaCapture 创建新的媒体捕获器
func NewMediaCapture(cfg *config.Config) (*MediaCapture, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建WebRTC视频轨道
	videoTrack, err := webrtc.NewTrackLocalStaticSample(
		webrtc.RTPCodecCapability{
			MimeType: webrtc.MimeTypeH264,
		},
		"video",
		"capture-video",
	)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建视频轨道失败: %v", err)
	}

	// 创建WebRTC音频轨道
	audioTrack, err := webrtc.NewTrackLocalStaticSample(
		webrtc.RTPCodecCapability{
			MimeType: webrtc.MimeTypePCMA,
		},
		"audio",
		"capture-audio",
	)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建音频轨道失败: %v", err)
	}

	capture := &MediaCapture{
		config:     cfg,
		ctx:        ctx,
		cancel:     cancel,
		videoTrack: videoTrack,
		audioTrack: audioTrack,
	}

	return capture, nil
}

// Initialize 初始化媒体捕获器
func (mc *MediaCapture) Initialize() error {
	log.Println("[MEDIA] 初始化媒体捕获器")

	// 简化实现：不需要实际的硬件初始化
	// 在实际项目中，这里会初始化摄像头和麦克风

	log.Println("[MEDIA] 媒体捕获器初始化完成")
	return nil
}

// StartCapture 开始捕获
func (mc *MediaCapture) StartCapture() error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if mc.capturing {
		return fmt.Errorf("已经在捕获中")
	}

	mc.capturing = true
	mc.stats.StartTime = time.Now()

	// 启动视频捕获协程
	go mc.videoCaptureLoop()

	// 启动音频捕获协程
	go mc.audioCaptureLoop()

	log.Println("[MEDIA] 开始媒体捕获")
	return nil
}

// StopCapture 停止捕获
func (mc *MediaCapture) StopCapture() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if !mc.capturing {
		return
	}

	mc.capturing = false
	mc.cancel()

	log.Println("[MEDIA] 停止媒体捕获")
}

// GetVideoTrack 获取视频轨道
func (mc *MediaCapture) GetVideoTrack() *webrtc.TrackLocalStaticSample {
	return mc.videoTrack
}

// GetAudioTrack 获取音频轨道
func (mc *MediaCapture) GetAudioTrack() *webrtc.TrackLocalStaticSample {
	return mc.audioTrack
}

// SetDataChannelSender 设置数据通道发送器
func (mc *MediaCapture) SetDataChannelSender(sender DataChannelSender) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	mc.dataChannelSender = sender
	log.Println("[MEDIA] 数据通道发送器已设置")
}

// GetStats 获取统计信息
func (mc *MediaCapture) GetStats() CaptureStats {
	mc.stats.mutex.RLock()
	defer mc.stats.mutex.RUnlock()

	// 计算FPS
	if mc.stats.VideoFrames > 0 && !mc.stats.StartTime.IsZero() {
		duration := time.Since(mc.stats.StartTime).Seconds()
		mc.stats.FPS = float64(mc.stats.VideoFrames) / duration
	}

	return mc.stats
}

// Close 关闭捕获器
func (mc *MediaCapture) Close() {
	mc.StopCapture()

	// 停止AstiAV摄像头
	if mc.astiavCamera != nil {
		mc.astiavCamera.Stop()
		mc.astiavCamera = nil
	}

	log.Println("[MEDIA] 媒体捕获器已关闭")
}

// initAstiAVCamera 初始化AstiAV摄像头
func (mc *MediaCapture) initAstiAVCamera() error {
	if mc.astiavCamera != nil {
		return nil // 已经初始化
	}

	// 创建AstiAV摄像头配置
	config := AstiAVCameraConfig{
		Width:  1024, // 最终输出分辨率（匹配ESP32显示分辨率）
		Height: 600,
		FPS:    15,
		// DeviceID和Format会自动检测
		// 不设置输入分辨率，让摄像头使用默认支持的分辨率
	}

	// 创建AstiAV摄像头
	camera, err := NewAstiAVCamera(config)
	if err != nil {
		return fmt.Errorf("创建AstiAV摄像头失败: %v", err)
	}

	// 启用调试模式
	camera.EnableDebug()

	// 列出可用设备
	if err := camera.ListDevices(); err != nil {
		log.Printf("[MEDIA] 列出设备失败: %v", err)
	}

	mc.astiavCamera = camera
	log.Printf("[MEDIA] AstiAV摄像头已初始化")

	return nil
}

// videoCaptureLoop 视频捕获循环
func (mc *MediaCapture) videoCaptureLoop() {
	log.Println("[MEDIA] 启动视频捕获循环")

	// 生成测试视频帧
	ticker := time.NewTicker(time.Second / time.Duration(mc.config.Video.FPS))
	defer ticker.Stop()

	frameCount := uint64(0)

	for mc.capturing {
		select {
		case <-mc.ctx.Done():
			return
		case <-ticker.C:
			// 使用FFmpeg捕获真实摄像头数据
			jpegData := mc.captureRealJPEGFrame(frameCount)

			// 通过数据通道发送JPEG数据
			if mc.dataChannelSender != nil && mc.dataChannelSender.IsDataChannelOpen() {
				if err := mc.dataChannelSender.SendDataChannelMessage(jpegData); err != nil {
					log.Printf("[MEDIA] 通过数据通道发送JPEG失败: %v", err)
					mc.updateStats(false, true, 0, 0)
				} else {
					mc.updateStats(true, false, uint64(len(jpegData)), 0)

					// 每30帧打印一次详细日志
					if frameCount%30 == 0 {
						log.Printf("[MEDIA] 已通过数据通道发送JPEG帧: %d (大小: %d bytes)",
							frameCount, len(jpegData))
					}
				}
			} else if mc.dataChannelSender != nil {
				// 数据通道未打开，跳过发送（避免错误日志）
				mc.updateStats(false, true, 0, 0)
			} else {
				// 如果没有数据通道发送器，仍然发送到视频轨道（向后兼容）
				testFrame := mc.generateTestVideoFrame(frameCount)
				if err := mc.videoTrack.WriteSample(testFrame); err != nil {
					log.Printf("[MEDIA] 发送视频帧失败: %v", err)
					mc.updateStats(false, true, 0, 0)
				} else {
					mc.updateStats(true, false, uint64(len(testFrame.Data)), 0)
				}
			}

			frameCount++
		}
	}

	log.Println("[MEDIA] 视频捕获循环结束")
}

// audioCaptureLoop 音频捕获循环
func (mc *MediaCapture) audioCaptureLoop() {
	log.Println("[MEDIA] 启动音频捕获循环")

	// 生成测试音频帧
	frameSize := mc.config.Audio.FrameSize
	sampleRate := mc.config.Audio.SampleRate
	frameDuration := time.Duration(frameSize) * time.Second / time.Duration(sampleRate)

	ticker := time.NewTicker(frameDuration)
	defer ticker.Stop()

	frameCount := uint64(0)

	for mc.capturing {
		select {
		case <-mc.ctx.Done():
			return
		case <-ticker.C:
			// 生成测试PCMA音频帧
			testFrame := mc.generateTestAudioFrame(frameCount)

			// 发送到WebRTC轨道
			if err := mc.audioTrack.WriteSample(testFrame); err != nil {
				log.Printf("[MEDIA] 发送音频帧失败: %v", err)
				mc.updateStats(false, true, 0, 0)
			} else {
				mc.updateStats(false, false, 0, uint64(len(testFrame.Data)))
			}

			frameCount++
		}
	}

	log.Println("[MEDIA] 音频捕获循环结束")
}

// generateTestVideoFrame 生成测试视频帧
func (mc *MediaCapture) generateTestVideoFrame(frameCount uint64) media.Sample {
	var frameData []byte

	// 每30帧发送一次SPS/PPS（关键帧）
	if frameCount%30 == 0 {
		// 生成更完整的H.264 SPS/PPS + IDR帧
		sps := []byte{
			0x00, 0x00, 0x00, 0x01, // NAL起始码
			0x67, 0x42, 0x00, 0x1f, // SPS NAL header (profile_idc=66, level_idc=31)
			0x96, 0x54, 0x05, 0x01, // SPS数据
			0xe2, 0xc5, 0xb2, 0xc0,
		}

		pps := []byte{
			0x00, 0x00, 0x00, 0x01, // NAL起始码
			0x68, 0xce, 0x3c, 0x80, // PPS NAL header + 数据
		}

		// IDR帧头
		idr := []byte{
			0x00, 0x00, 0x00, 0x01, // NAL起始码
			0x65, 0x88, 0x84, 0x00, // IDR slice header
			0x33, 0xff, 0xfe, 0xf6, // 模拟压缩数据
			0x62, 0xee, 0xef, 0x20,
			0x00, 0x00, 0x03, 0x00,
			0x00, 0x03, 0x00, 0x00,
		}

		frameData = append(frameData, sps...)
		frameData = append(frameData, pps...)
		frameData = append(frameData, idr...)

		// log.Printf("[MEDIA] 发送关键帧 #%d (SPS+PPS+IDR), 大小: %d bytes", frameCount, len(frameData))
	} else {
		// 生成P帧
		pFrame := []byte{
			0x00, 0x00, 0x00, 0x01, // NAL起始码
			0x41, 0x9a, 0x24, 0x4d, // P slice header
			0x40, 0x32, 0x8f, 0x06, // 模拟压缩数据
			0x23, 0x08, 0x44, 0x30,
		}

		frameData = pFrame
		// log.Printf("[MEDIA] 发送P帧 #%d, 大小: %d bytes", frameCount, len(frameData))
	}

	// 计算基于开始时间的时间戳
	timestampDuration := time.Duration(frameCount) * time.Second / time.Duration(mc.config.Video.FPS)
	timestamp := mc.stats.StartTime.Add(timestampDuration)
	duration := time.Second / time.Duration(mc.config.Video.FPS)

	return media.Sample{
		Data:      frameData,
		Duration:  duration,
		Timestamp: timestamp,
	}
}

// generateTestAudioFrame 生成测试音频帧
func (mc *MediaCapture) generateTestAudioFrame(frameCount uint64) media.Sample {
	frameSize := mc.config.Audio.FrameSize
	testData := make([]byte, frameSize)

	// 生成更真实的PCMA音频数据（模拟正弦波）
	frequency := 440.0 // 440Hz A音
	sampleRate := float64(mc.config.Audio.SampleRate)

	for i := 0; i < frameSize; i++ {
		// 生成正弦波样本
		t := float64(frameCount*uint64(frameSize)+uint64(i)) / sampleRate
		sample := 0.5 * math.Sin(2*math.Pi*frequency*t)

		// 转换为16位PCM然后编码为PCMA
		pcm16 := int16(sample * 32767)
		testData[i] = linearToPCMA(pcm16)
	}

	// 计算基于开始时间的时间戳
	audioSampleRate := mc.config.Audio.SampleRate
	timestampDuration := time.Duration(frameCount*uint64(frameSize)) * time.Second / time.Duration(audioSampleRate)
	timestamp := mc.stats.StartTime.Add(timestampDuration)
	duration := time.Duration(frameSize) * time.Second / time.Duration(audioSampleRate)

	return media.Sample{
		Data:      testData,
		Duration:  duration,
		Timestamp: timestamp,
	}
}

// linearToPCMA 将16位线性PCM转换为PCMA (G.711 A-law)
func linearToPCMA(pcm int16) byte {
	// 简化的A-law编码实现
	var mask byte
	var seg byte

	if pcm < 0 {
		mask = 0x7F
		pcm = -pcm
		if pcm > 32635 {
			pcm = 32635
		}
	} else {
		mask = 0xFF
		if pcm > 32635 {
			pcm = 32635
		}
	}

	if pcm >= 256 {
		if pcm >= 2048 {
			if pcm >= 8192 {
				if pcm >= 16384 {
					seg = 7
				} else {
					seg = 6
				}
			} else {
				if pcm >= 4096 {
					seg = 5
				} else {
					seg = 4
				}
			}
		} else {
			if pcm >= 1024 {
				seg = 3
			} else {
				seg = 2
			}
		}
	} else {
		if pcm >= 128 {
			seg = 1
		} else {
			seg = 0
		}
	}

	if seg >= 8 {
		return byte(0x7F ^ mask)
	}

	aval := seg << 4
	if seg < 2 {
		aval |= byte((pcm >> 1) & 0x0F)
	} else {
		aval |= byte((pcm >> seg) & 0x0F)
	}

	return aval ^ mask
}

// updateStats 更新统计信息
func (mc *MediaCapture) updateStats(isVideo, isError bool, videoBytes, audioBytes uint64) {
	mc.stats.mutex.Lock()
	defer mc.stats.mutex.Unlock()

	now := time.Now()

	if isError {
		mc.stats.Errors++
		return
	}

	if isVideo {
		mc.stats.VideoFrames++
		mc.stats.VideoBytes += videoBytes
		mc.stats.LastFrameTime = now

		// 计算FPS
		if !mc.stats.StartTime.IsZero() {
			duration := now.Sub(mc.stats.StartTime).Seconds()
			if duration > 0 {
				mc.stats.FPS = float64(mc.stats.VideoFrames) / duration
			}
		}
	} else {
		mc.stats.AudioFrames++
		mc.stats.AudioBytes += audioBytes
	}
}

// getFrameType 获取帧类型描述
func (mc *MediaCapture) getFrameType(frameCount uint64) string {
	if frameCount%30 == 0 {
		return "关键帧(SPS+PPS+IDR)"
	}
	return "P帧"
}

// generateTestJPEGFrame 生成ESP32兼容的JPEG帧
func (mc *MediaCapture) generateTestJPEGFrame(frameCount uint64) []byte {
	// 创建一个最小的、ESP32兼容的JPEG图像
	// 基于ESP32实际能解码的格式

	// 最小的baseline JPEG头部
	jpegHeader := []byte{
		// SOI (Start of Image)
		0xFF, 0xD8,

		// APP0 (JFIF) - 简化版本
		0xFF, 0xE0, 0x00, 0x10, // APP0 marker + length
		0x4A, 0x46, 0x49, 0x46, 0x00, // "JFIF\0"
		0x01, 0x01, // version 1.1
		0x00,                   // units (no units)
		0x00, 0x01, 0x00, 0x01, // X/Y density = 1
		0x00, 0x00, // thumbnail width/height = 0

		// DQT (Define Quantization Table) - 标准亮度表
		0xFF, 0xDB, 0x00, 0x43, 0x00, // DQT marker + length + table ID
		// 标准量化表 (8x8 = 64 bytes)
		0x10, 0x0B, 0x0C, 0x0E, 0x0C, 0x0A, 0x10, 0x0E,
		0x0D, 0x0E, 0x12, 0x11, 0x10, 0x13, 0x18, 0x28,
		0x1A, 0x18, 0x16, 0x16, 0x18, 0x31, 0x23, 0x25,
		0x1D, 0x28, 0x3A, 0x33, 0x3D, 0x3C, 0x39, 0x33,
		0x38, 0x37, 0x40, 0x48, 0x5C, 0x4E, 0x40, 0x44,
		0x57, 0x45, 0x37, 0x38, 0x50, 0x6D, 0x51, 0x57,
		0x5F, 0x62, 0x67, 0x68, 0x67, 0x3E, 0x4D, 0x71,
		0x79, 0x70, 0x64, 0x78, 0x5C, 0x65, 0x67, 0x63,

		// SOF0 (Start of Frame - Baseline DCT) - 最小配置
		0xFF, 0xC0, 0x00, 0x11, // SOF0 marker + length
		0x08,       // sample precision = 8 bits
		0x00, 0x10, // height = 16 pixels
		0x00, 0x10, // width = 16 pixels
		0x01,             // number of components = 1 (grayscale)
		0x01, 0x11, 0x00, // component 1: ID=1, sampling=1x1, quantization table=0

		// DHT (Define Huffman Table) - 最小DC表
		0xFF, 0xC4, 0x00, 0x1F, 0x00, // DHT marker + length + table class/ID
		0x00, 0x01, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01,
		0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
		0x08, 0x09, 0x0A, 0x0B,

		// DHT (Define Huffman Table) - 最小AC表
		0xFF, 0xC4, 0x00, 0xB5, 0x10, // DHT marker + length + table class/ID
		0x00, 0x02, 0x01, 0x03, 0x03, 0x02, 0x04, 0x03,
		0x05, 0x05, 0x04, 0x04, 0x00, 0x00, 0x01, 0x7D,
		0x01, 0x02, 0x03, 0x00, 0x04, 0x11, 0x05, 0x12,
		0x21, 0x31, 0x41, 0x06, 0x13, 0x51, 0x61, 0x07,
		0x22, 0x71, 0x14, 0x32, 0x81, 0x91, 0xA1, 0x08,
		0x23, 0x42, 0xB1, 0xC1, 0x15, 0x52, 0xD1, 0xF0,
		0x24, 0x33, 0x62, 0x72, 0x82, 0x09, 0x0A, 0x16,
		0x17, 0x18, 0x19, 0x1A, 0x25, 0x26, 0x27, 0x28,
		0x29, 0x2A, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
		0x3A, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49,
		0x4A, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59,
		0x5A, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69,
		0x6A, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79,
		0x7A, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89,
		0x8A, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98,
		0x99, 0x9A, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7,
		0xA8, 0xA9, 0xAA, 0xB2, 0xB3, 0xB4, 0xB5, 0xB6,
		0xB7, 0xB8, 0xB9, 0xBA, 0xC2, 0xC3, 0xC4, 0xC5,
		0xC6, 0xC7, 0xC8, 0xC9, 0xCA, 0xD2, 0xD3, 0xD4,
		0xD5, 0xD6, 0xD7, 0xD8, 0xD9, 0xDA, 0xE1, 0xE2,
		0xE3, 0xE4, 0xE5, 0xE6, 0xE7, 0xE8, 0xE9, 0xEA,
		0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6, 0xF7, 0xF8,
		0xF9, 0xFA,

		// SOS (Start of Scan)
		0xFF, 0xDA, 0x00, 0x08, // SOS marker + length
		0x01,       // number of components
		0x01, 0x00, // component 1: ID=1, Huffman table=0
		0x00, 0x3F, 0x00, // spectral selection + successive approximation
	}

	// 最小的图像数据 (16x16 grayscale, 全黑)
	imageData := []byte{
		0xFF, 0xC0, // 最小的压缩数据
	}

	// EOI (End of Image)
	jpegTail := []byte{0xFF, 0xD9}

	// 组合完整的JPEG
	totalSize := len(jpegHeader) + len(imageData) + len(jpegTail)
	jpegData := make([]byte, totalSize)

	copy(jpegData, jpegHeader)
	copy(jpegData[len(jpegHeader):], imageData)
	copy(jpegData[len(jpegHeader)+len(imageData):], jpegTail)

	return jpegData
}

// captureRealJPEGFrame 使用AstiAV捕获真实摄像头JPEG帧
func (mc *MediaCapture) captureRealJPEGFrame(frameCount uint64) []byte {
	// 使用AstiAV实现真实的摄像头捕获，不偷懒！

	// 初始化AstiAV摄像头（如果还没有初始化）
	if mc.astiavCamera == nil {
		if err := mc.initAstiAVCamera(); err != nil {
			if frameCount%30 == 0 {
				log.Printf("[MEDIA] AstiAV摄像头初始化失败: %v, 使用备用方案", err)
			}
			return mc.generateHighQualityTestImage(frameCount)
		}
	}

	// 检查摄像头是否正在捕获
	if !mc.astiavCamera.IsCapturing() {
		if err := mc.astiavCamera.Start(); err != nil {
			if frameCount%30 == 0 {
				log.Printf("[MEDIA] AstiAV摄像头启动失败: %v, 使用备用方案", err)
			}
			return mc.generateHighQualityTestImage(frameCount)
		}
	}

	// 尝试从摄像头获取帧
	select {
	case jpegData := <-mc.astiavCamera.GetFrameChannel():
		// 成功获取摄像头帧
		if frameCount%30 == 0 {
			log.Printf("[MEDIA] AstiAV成功捕获摄像头帧: %d (大小: %d bytes, 分辨率: 1024x600)",
				frameCount, len(jpegData))

			// 保存样本帧用于调试
			if frameCount <= 120 {
				filename := fmt.Sprintf("debug/astiav_frame_%d.jpg", frameCount)
				if err := os.WriteFile(filename, jpegData, 0644); err == nil {
					log.Printf("[DEBUG] 已保存AstiAV摄像头帧样本: %s", filename)
				}
			}
		}
		return jpegData

	case <-time.After(100 * time.Millisecond):
		// 超时，使用备用方案
		if frameCount%60 == 0 { // 降低日志频率
			log.Printf("[MEDIA] AstiAV摄像头帧获取超时，使用备用方案")
		}
		return mc.generateHighQualityTestImage(frameCount)
	}
}

// tryAlternativeCameraCapture 尝试备用摄像头捕获方案
func (mc *MediaCapture) tryAlternativeCameraCapture(frameCount uint64) []byte {
	// 尝试多种备用方案来捕获摄像头

	// 方案1: 尝试不同的摄像头设备
	alternatives := mc.getCameraAlternatives()

	for i, alt := range alternatives {
		if frameCount%30 == 0 {
			log.Printf("[MEDIA] 尝试备用摄像头方案 %d: %s", i+1, alt.description)
		}

		// 构建命令
		args := []string{}
		args = append(args, alt.inputArgs...)
		args = append(args, "-i", alt.device)
		args = append(args,
			"-vframes", "1",
			"-f", "mjpeg",
			"-q:v", "5", // 稍微降低质量以提高兼容性
			"-s", "320x240",
			"-pix_fmt", "yuvj420p",
			"-an",
			"-loglevel", "error",
			"-")

		// 设置较短的超时
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		cmd := exec.CommandContext(ctx, "ffmpeg", args...)

		output, err := cmd.Output()
		cancel()

		if err == nil && len(output) > 10 && output[0] == 0xFF && output[1] == 0xD8 {
			if frameCount%30 == 0 {
				log.Printf("[MEDIA] 备用方案 %d 成功: %s (大小: %d bytes)",
					i+1, alt.description, len(output))
			}
			return output
		}
	}

	// 所有摄像头方案都失败，使用高质量测试图像
	if frameCount%30 == 0 {
		log.Printf("[MEDIA] 所有摄像头方案失败，使用高质量测试图像")
	}
	return mc.generateHighQualityTestImage(frameCount)
}

// CameraAlternative 摄像头备用方案
type CameraAlternative struct {
	inputArgs   []string
	device      string
	description string
}

// getCameraAlternatives 获取摄像头备用方案列表
func (mc *MediaCapture) getCameraAlternatives() []CameraAlternative {
	var alternatives []CameraAlternative

	switch runtime.GOOS {
	case "darwin": // macOS
		alternatives = []CameraAlternative{
			{[]string{"-f", "avfoundation"}, "1", "第二个摄像头"},
			{[]string{"-f", "avfoundation"}, "0:none", "摄像头0无音频"},
			{[]string{"-f", "avfoundation"}, "default", "默认设备"},
		}
	case "linux": // Linux
		alternatives = []CameraAlternative{
			{[]string{"-f", "v4l2"}, "/dev/video1", "第二个摄像头"},
			{[]string{"-f", "v4l2"}, "/dev/video2", "第三个摄像头"},
			{[]string{"-f", "v4l2", "-input_format", "mjpeg"}, "/dev/video0", "MJPEG格式"},
			{[]string{"-f", "v4l2", "-video_size", "640x480"}, "/dev/video0", "不同分辨率"},
		}
	case "windows": // Windows
		alternatives = []CameraAlternative{
			{[]string{"-f", "dshow"}, "video=USB Camera", "USB摄像头"},
			{[]string{"-f", "dshow"}, "video=Integrated Camera", "集成摄像头"},
			{[]string{"-f", "dshow"}, "video=HD WebCam", "高清摄像头"},
		}
	}

	return alternatives
}

// generateHighQualityTestImage 生成高质量测试图像
func (mc *MediaCapture) generateHighQualityTestImage(frameCount uint64) []byte {
	// 生成一个明显的、高对比度的测试图像
	// 确保ESP32能够清楚地看到图像变化

	// 计算动态参数
	colorIndex := frameCount % 6
	brightness := 50 + int(frameCount%40)

	var colorName, colorValue string
	switch colorIndex {
	case 0:
		colorName, colorValue = "红色", "red"
	case 1:
		colorName, colorValue = "绿色", "green"
	case 2:
		colorName, colorValue = "蓝色", "blue"
	case 3:
		colorName, colorValue = "黄色", "yellow"
	case 4:
		colorName, colorValue = "紫色", "purple"
	case 5:
		colorName, colorValue = "白色", "white"
	}

	// 创建高对比度的测试图案
	testPattern := fmt.Sprintf("color=%s:size=320x240:duration=1", colorValue)

	// 添加大号文字和边框
	textOverlay := fmt.Sprintf("drawtext=text='%s\\nFrame %d\\n320x240':x=50:y=80:fontsize=24:fontcolor=black:box=1:boxcolor=white@0.9",
		colorName, frameCount)

	// 添加边框
	borderOverlay := "drawbox=x=0:y=0:w=320:h=240:color=black:t=5"

	cmd := exec.Command("ffmpeg",
		"-f", "lavfi",
		"-i", testPattern,
		"-vf", fmt.Sprintf("%s,%s,hue=b=%d", borderOverlay, textOverlay, brightness),
		"-vframes", "1",
		"-f", "mjpeg",
		"-q:v", "2", // 最高质量
		"-pix_fmt", "yuvj420p",
		"-loglevel", "quiet",
		"-")

	output, err := cmd.Output()
	if err != nil || len(output) < 10 {
		// 最后的备用方案：手动生成的JPEG
		return mc.generateTestJPEGFrame(frameCount)
	}

	if frameCount%30 == 0 {
		log.Printf("[MEDIA] 生成高质量测试图像: %s, 帧 %d (大小: %d bytes)",
			colorName, frameCount, len(output))
	}

	return output
}
