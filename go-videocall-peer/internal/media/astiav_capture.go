package media

import (
	"context"
	"fmt"
	"log"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/asticode/go-astiav"
)

// AstiAVCameraConfig AstiAV摄像头配置
type AstiAVCameraConfig struct {
	Width    int
	Height   int
	FPS      int
	DeviceID string
	Format   string // "avfoundation" for macOS, "v4l2" for Linux, "dshow" for Windows
}

// AstiAVCamera AstiAV摄像头捕获器
type AstiAVCamera struct {
	config AstiAVCameraConfig
	ctx    context.Context
	cancel context.CancelFunc

	// FFmpeg components
	inputContext   *astiav.FormatContext
	videoStream    *astiav.Stream
	decoderContext *astiav.CodecContext
	encoderContext *astiav.CodecContext
	scaleContext   *astiav.SoftwareScaleContext

	// Frame buffers
	inputPacket   *astiav.Packet
	decodedFrame  *astiav.Frame
	scaledFrame   *astiav.Frame
	encodedPacket *astiav.Packet

	// State
	isCapturing bool
	mutex       sync.RWMutex
	frameCount  int64
	debugMode   bool

	// Frame channel for output
	frameChannel chan []byte
}

// NewAstiAVCamera 创建AstiAV摄像头捕获器
func NewAstiAVCamera(config AstiAVCameraConfig) (*AstiAVCamera, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 自动检测平台和设备
	if config.Format == "" {
		switch runtime.GOOS {
		case "darwin":
			config.Format = "avfoundation"
		case "linux":
			config.Format = "v4l2"
		case "windows":
			config.Format = "dshow"
		default:
			cancel()
			return nil, fmt.Errorf("unsupported platform: %s", runtime.GOOS)
		}
	}

	// 设置默认设备ID
	if config.DeviceID == "" {
		if config.Format == "v4l2" {
			config.DeviceID = "/dev/video0"
		} else {
			config.DeviceID = "0"
		}
	}

	// 设置默认分辨率和帧率 - 匹配ESP32显示分辨率
	if config.Width == 0 {
		config.Width = 1024 // 匹配ESP32显示分辨率
	}
	if config.Height == 0 {
		config.Height = 600
	}
	if config.FPS == 0 {
		config.FPS = 15 // 稍微提高帧率
	}

	camera := &AstiAVCamera{
		config:       config,
		ctx:          ctx,
		cancel:       cancel,
		frameChannel: make(chan []byte, 10), // 缓冲10帧
	}

	// 注册所有设备
	astiav.RegisterAllDevices()

	// 设置FFmpeg日志级别为ERROR，避免日志刷屏
	astiav.SetLogLevel(astiav.LogLevelError)

	log.Printf("[ASTIAV] 摄像头捕获器已创建: %dx%d@%dfps, 设备: %s (%s)",
		config.Width, config.Height, config.FPS, config.DeviceID, config.Format)

	return camera, nil
}

// Start 启动摄像头捕获
func (c *AstiAVCamera) Start() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isCapturing {
		return fmt.Errorf("camera is already capturing")
	}

	log.Printf("[ASTIAV] 启动摄像头捕获...")

	// 初始化输入
	if err := c.initInput(); err != nil {
		return fmt.Errorf("failed to init input: %v", err)
	}

	// 初始化解码器
	if err := c.initDecoder(); err != nil {
		c.cleanup()
		return fmt.Errorf("failed to init decoder: %v", err)
	}

	// 初始化编码器
	if err := c.initEncoder(); err != nil {
		c.cleanup()
		return fmt.Errorf("failed to init encoder: %v", err)
	}

	// 初始化缩放器（如果需要）
	if err := c.initScaler(); err != nil {
		c.cleanup()
		return fmt.Errorf("failed to init scaler: %v", err)
	}

	// 分配帧和包
	c.inputPacket = astiav.AllocPacket()
	c.decodedFrame = astiav.AllocFrame()
	c.scaledFrame = astiav.AllocFrame()
	c.encodedPacket = astiav.AllocPacket()

	c.isCapturing = true

	// 启动捕获循环
	go c.captureLoop()

	log.Printf("[ASTIAV] ✅ 摄像头捕获已启动")
	return nil
}

// Stop 停止摄像头捕获
func (c *AstiAVCamera) Stop() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.isCapturing {
		return
	}

	log.Printf("[ASTIAV] 停止摄像头捕获...")

	// 先设置停止标志，停止捕获循环
	c.isCapturing = false

	// 取消上下文，通知捕获循环退出
	c.cancel()

	// 等待足够长的时间让捕获循环完全退出
	// 使用time.Sleep确保等待时间足够
	time.Sleep(500 * time.Millisecond)

	// 安全地关闭帧通道
	func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("[ASTIAV] 关闭帧通道时发生panic (已忽略): %v", r)
			}
		}()

		if c.frameChannel != nil {
			close(c.frameChannel)
			c.frameChannel = nil
		}
	}()

	// 再等待一段时间确保所有goroutine都退出
	time.Sleep(200 * time.Millisecond)

	// 清理AstiAV资源
	c.cleanup()

	log.Printf("[ASTIAV] ✅ 摄像头捕获已停止")
}

// GetFrameChannel 获取帧数据通道
func (c *AstiAVCamera) GetFrameChannel() <-chan []byte {
	return c.frameChannel
}

// IsCapturing 检查是否正在捕获
func (c *AstiAVCamera) IsCapturing() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.isCapturing
}

// GetStats 获取统计信息
func (c *AstiAVCamera) GetStats() (frameCount int64, isCapturing bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.frameCount, c.isCapturing
}

// EnableDebug 启用调试模式
func (c *AstiAVCamera) EnableDebug() {
	c.debugMode = true
	log.Printf("[ASTIAV] 调试模式已启用")
}

// ListDevices 列出可用设备
func (c *AstiAVCamera) ListDevices() error {
	log.Printf("[ASTIAV] 列出可用设备 (%s):", c.config.Format)

	switch c.config.Format {
	case "avfoundation":
		log.Printf("[ASTIAV] macOS AVFoundation 设备:")
		log.Printf("[ASTIAV]   0 - 默认摄像头")
		log.Printf("[ASTIAV]   1 - 第二个摄像头（如果有）")
		log.Printf("[ASTIAV] 使用命令查看详细设备: ffmpeg -f avfoundation -list_devices true -i \"\"")

	case "v4l2":
		log.Printf("[ASTIAV] Linux V4L2 设备:")
		log.Printf("[ASTIAV]   /dev/video0 - 默认摄像头")
		log.Printf("[ASTIAV]   /dev/video1 - 第二个摄像头（如果有）")
		log.Printf("[ASTIAV] 使用命令查看详细设备: v4l2-ctl --list-devices")

	case "dshow":
		log.Printf("[ASTIAV] Windows DirectShow 设备:")
		log.Printf("[ASTIAV]   USB2.0 Camera - 常见USB摄像头")
		log.Printf("[ASTIAV]   Integrated Camera - 集成摄像头")
		log.Printf("[ASTIAV] 使用命令查看详细设备: ffmpeg -f dshow -list_devices true -i dummy")

	default:
		return fmt.Errorf("unsupported format: %s", c.config.Format)
	}

	return nil
}

// initInput 初始化输入设备
func (c *AstiAVCamera) initInput() error {
	// 分配格式上下文
	c.inputContext = astiav.AllocFormatContext()
	if c.inputContext == nil {
		return fmt.Errorf("failed to allocate input format context")
	}

	// 构建输入参数
	var inputURL string
	var inputFormat *astiav.InputFormat
	inputOptions := astiav.NewDictionary()
	defer inputOptions.Free()

	// 根据平台配置输入
	switch c.config.Format {
	case "avfoundation": // macOS
		inputURL = c.config.DeviceID + ":none" // 只要视频，不要音频
		inputFormat = astiav.FindInputFormat("avfoundation")

		// 不强制设置分辨率，让摄像头使用默认支持的分辨率
		// 只设置帧率
		if c.config.FPS > 0 {
			inputOptions.Set("framerate", fmt.Sprintf("%d", c.config.FPS), astiav.NewDictionaryFlags())
		}

		// 使用兼容的像素格式
		inputOptions.Set("pixel_format", "uyvy422", astiav.NewDictionaryFlags())

	case "v4l2": // Linux
		inputURL = c.config.DeviceID
		inputFormat = astiav.FindInputFormat("v4l2")
		if c.config.Width > 0 && c.config.Height > 0 {
			inputOptions.Set("video_size", fmt.Sprintf("%dx%d", c.config.Width, c.config.Height), astiav.NewDictionaryFlags())
		}
		if c.config.FPS > 0 {
			inputOptions.Set("framerate", fmt.Sprintf("%d", c.config.FPS), astiav.NewDictionaryFlags())
		}

	case "dshow": // Windows
		inputURL = fmt.Sprintf("video=%s", c.config.DeviceID)
		inputFormat = astiav.FindInputFormat("dshow")

	default:
		return fmt.Errorf("unsupported format: %s", c.config.Format)
	}

	if inputFormat == nil {
		return fmt.Errorf("input format '%s' not found", c.config.Format)
	}

	// 打开输入
	log.Printf("[ASTIAV] 打开输入: %s", inputURL)
	if err := c.inputContext.OpenInput(inputURL, inputFormat, inputOptions); err != nil {
		return fmt.Errorf("failed to open input: %v", err)
	}

	// 查找流信息
	if err := c.inputContext.FindStreamInfo(nil); err != nil {
		return fmt.Errorf("failed to find stream info: %v", err)
	}

	// 找到视频流
	c.videoStream = nil
	for _, stream := range c.inputContext.Streams() {
		if stream.CodecParameters().MediaType() == astiav.MediaTypeVideo {
			c.videoStream = stream
			break
		}
	}

	if c.videoStream == nil {
		return fmt.Errorf("no video stream found")
	}

	log.Printf("[ASTIAV] 找到视频流: %dx%d, %s",
		c.videoStream.CodecParameters().Width(),
		c.videoStream.CodecParameters().Height(),
		c.videoStream.CodecParameters().CodecID().String())

	return nil
}

// initDecoder 初始化解码器
func (c *AstiAVCamera) initDecoder() error {
	// 查找解码器
	decoder := astiav.FindDecoder(c.videoStream.CodecParameters().CodecID())
	if decoder == nil {
		return fmt.Errorf("decoder not found for codec %s", c.videoStream.CodecParameters().CodecID().String())
	}

	// 分配解码器上下文
	c.decoderContext = astiav.AllocCodecContext(decoder)
	if c.decoderContext == nil {
		return fmt.Errorf("failed to allocate decoder context")
	}

	// 复制参数
	if err := c.videoStream.CodecParameters().ToCodecContext(c.decoderContext); err != nil {
		return fmt.Errorf("failed to copy codec parameters: %v", err)
	}

	// 打开解码器
	if err := c.decoderContext.Open(decoder, nil); err != nil {
		return fmt.Errorf("failed to open decoder: %v", err)
	}

	log.Printf("[ASTIAV] 解码器已初始化: %s %dx%d %s",
		decoder.Name(),
		c.decoderContext.Width(), c.decoderContext.Height(),
		c.decoderContext.PixelFormat().String())

	return nil
}

// initEncoder 初始化编码器（编码为MJPEG）
func (c *AstiAVCamera) initEncoder() error {
	// 查找MJPEG编码器
	encoder := astiav.FindEncoder(astiav.CodecIDMjpeg)
	if encoder == nil {
		return fmt.Errorf("MJPEG encoder not found")
	}

	// 分配编码器上下文
	c.encoderContext = astiav.AllocCodecContext(encoder)
	if c.encoderContext == nil {
		return fmt.Errorf("failed to allocate encoder context")
	}

	// 设置编码参数
	c.encoderContext.SetWidth(c.config.Width)
	c.encoderContext.SetHeight(c.config.Height)
	c.encoderContext.SetPixelFormat(astiav.PixelFormatYuvj420P) // MJPEG使用YUVJ420P
	c.encoderContext.SetTimeBase(astiav.NewRational(1, c.config.FPS))
	c.encoderContext.SetFramerate(astiav.NewRational(c.config.FPS, 1))

	// MJPEG编码选项
	options := astiav.NewDictionary()
	defer options.Free()
	options.Set("q:v", "5", astiav.NewDictionaryFlags()) // 高质量

	// 打开编码器
	if err := c.encoderContext.Open(encoder, options); err != nil {
		return fmt.Errorf("failed to open encoder: %v", err)
	}

	log.Printf("[ASTIAV] 编码器已初始化: MJPEG %dx%d, 质量: 5",
		c.config.Width, c.config.Height)
	return nil
}

// initScaler 初始化缩放器
func (c *AstiAVCamera) initScaler() error {
	inputWidth := c.decoderContext.Width()
	inputHeight := c.decoderContext.Height()
	inputPixFmt := c.decoderContext.PixelFormat()
	outputPixFmt := c.encoderContext.PixelFormat()

	log.Printf("[ASTIAV] 缩放器检查: %dx%d %s -> %dx%d %s",
		inputWidth, inputHeight, inputPixFmt.String(),
		c.config.Width, c.config.Height, outputPixFmt.String())

	// 如果尺寸和格式都相同，不需要缩放
	if inputWidth == c.config.Width && inputHeight == c.config.Height && inputPixFmt == outputPixFmt {
		log.Printf("[ASTIAV] ✅ 无需缩放 - 格式和尺寸相同")
		c.scaleContext = nil
		return nil
	}

	// 创建缩放上下文
	var err error
	c.scaleContext, err = astiav.CreateSoftwareScaleContext(
		inputWidth, inputHeight, inputPixFmt,
		c.config.Width, c.config.Height, outputPixFmt,
		astiav.NewSoftwareScaleContextFlags(astiav.SoftwareScaleContextFlagBilinear),
	)
	if err != nil {
		return fmt.Errorf("failed to create scale context: %v", err)
	}

	log.Printf("[ASTIAV] ✅ 缩放器已初始化")
	return nil
}

// captureLoop 捕获循环
func (c *AstiAVCamera) captureLoop() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("[ASTIAV] 捕获循环崩溃: %v", r)
		}
	}()

	log.Printf("[ASTIAV] 捕获循环已启动")

	// 错误计数器，用于控制日志输出
	errorCount := 0
	lastErrorTime := time.Now()

	// 计算帧间隔
	frameInterval := time.Duration(1000/c.config.FPS) * time.Millisecond
	lastFrameTime := time.Now()

	for {
		select {
		case <-c.ctx.Done():
			log.Printf("[ASTIAV] 捕获循环退出")
			return
		default:
			// 检查是否还在捕获状态
			if !c.isCapturing {
				log.Printf("[ASTIAV] 捕获已停止，退出循环")
				return
			}

			// 控制帧率，避免过快读取
			now := time.Now()
			if now.Sub(lastFrameTime) < frameInterval {
				time.Sleep(frameInterval - now.Sub(lastFrameTime))
			}

			if err := c.captureFrame(); err != nil {
				errorCount++

				// 控制错误日志输出频率
				if time.Since(lastErrorTime) > 5*time.Second {
					if errorCount > 1 {
						log.Printf("[ASTIAV] 捕获帧失败 (过去5秒内%d次): %v", errorCount, err)
					} else {
						log.Printf("[ASTIAV] 捕获帧失败: %v", err)
					}
					errorCount = 0
					lastErrorTime = now
				}

				// 根据错误类型调整等待时间
				if strings.Contains(err.Error(), "Resource temporarily unavailable") {
					time.Sleep(50 * time.Millisecond) // 资源不可用，短暂等待
				} else {
					time.Sleep(200 * time.Millisecond) // 其他错误，较长等待
				}
				continue
			}

			// 成功捕获帧，重置错误计数
			errorCount = 0
			lastFrameTime = time.Now()
		}
	}
}

// captureFrame 捕获单帧
func (c *AstiAVCamera) captureFrame() error {
	// 检查是否还在捕获状态
	if !c.isCapturing {
		return fmt.Errorf("capture stopped")
	}

	// 检查输入上下文是否有效
	if c.inputContext == nil {
		return fmt.Errorf("input context is nil")
	}

	// 读取包
	if err := c.inputContext.ReadFrame(c.inputPacket); err != nil {
		return fmt.Errorf("failed to read frame: %v", err)
	}
	defer c.inputPacket.Unref()

	// 检查是否是视频包
	if c.inputPacket.StreamIndex() != c.videoStream.Index() {
		return nil // 跳过非视频包
	}

	// 发送包到解码器
	if err := c.decoderContext.SendPacket(c.inputPacket); err != nil {
		return fmt.Errorf("failed to send packet: %v", err)
	}

	// 接收解码后的帧
	if err := c.decoderContext.ReceiveFrame(c.decodedFrame); err != nil {
		return fmt.Errorf("failed to receive frame: %v", err)
	}
	defer c.decodedFrame.Unref()

	// 处理帧（缩放/格式转换）
	var frameToEncode *astiav.Frame
	if c.scaleContext != nil {
		// 需要缩放/转换
		if err := c.scaleContext.ScaleFrame(c.decodedFrame, c.scaledFrame); err != nil {
			return fmt.Errorf("failed to scale frame: %v", err)
		}
		frameToEncode = c.scaledFrame
	} else {
		// 直接使用解码后的帧
		frameToEncode = c.decodedFrame
	}

	// 设置帧的时间戳
	frameToEncode.SetPts(c.frameCount)

	// 发送帧到编码器
	if err := c.encoderContext.SendFrame(frameToEncode); err != nil {
		return fmt.Errorf("failed to send frame to encoder: %v", err)
	}

	// 接收编码后的包
	if err := c.encoderContext.ReceivePacket(c.encodedPacket); err != nil {
		return fmt.Errorf("failed to receive packet from encoder: %v", err)
	}
	defer c.encodedPacket.Unref()

	// 获取JPEG数据
	jpegData := c.encodedPacket.Data()
	if len(jpegData) == 0 {
		return fmt.Errorf("empty JPEG data")
	}

	// 复制数据（因为包会被释放）
	jpegCopy := make([]byte, len(jpegData))
	copy(jpegCopy, jpegData)

	// 安全地发送到通道
	defer func() {
		if r := recover(); r != nil {
			// 通道可能已关闭，忽略panic
			if c.debugMode {
				log.Printf("[ASTIAV] 帧通道发送panic (已忽略): %v", r)
			}
		}
	}()

	// 检查是否还在捕获
	if !c.isCapturing {
		return nil // 已停止，直接返回
	}

	select {
	case c.frameChannel <- jpegCopy:
		c.frameCount++
		if c.debugMode && c.frameCount%30 == 0 {
			log.Printf("[ASTIAV] 已捕获 %d 帧, 当前帧大小: %d bytes", c.frameCount, len(jpegCopy))
		}
	default:
		// 通道满了，丢弃这一帧
		if c.debugMode {
			log.Printf("[ASTIAV] 帧通道满，丢弃帧")
		}
	}

	return nil
}

// cleanup 清理资源
func (c *AstiAVCamera) cleanup() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("[ASTIAV] 清理资源时发生panic (已忽略): %v", r)
		}
	}()

	if c.inputPacket != nil {
		c.inputPacket.Free()
		c.inputPacket = nil
	}
	if c.decodedFrame != nil {
		c.decodedFrame.Free()
		c.decodedFrame = nil
	}
	if c.scaledFrame != nil {
		c.scaledFrame.Free()
		c.scaledFrame = nil
	}
	if c.encodedPacket != nil {
		c.encodedPacket.Free()
		c.encodedPacket = nil
	}
	if c.scaleContext != nil {
		c.scaleContext.Free()
		c.scaleContext = nil
	}
	if c.encoderContext != nil {
		c.encoderContext.Free()
		c.encoderContext = nil
	}
	if c.decoderContext != nil {
		c.decoderContext.Free()
		c.decoderContext = nil
	}
	if c.inputContext != nil {
		// 安全地关闭输入上下文
		func() {
			defer func() {
				if r := recover(); r != nil {
					log.Printf("[ASTIAV] 关闭输入上下文时发生panic (已忽略): %v", r)
				}
			}()
			c.inputContext.CloseInput()
			c.inputContext.Free()
		}()
		c.inputContext = nil
	}
}
