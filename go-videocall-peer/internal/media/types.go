package media

import (
	"time"
)

// Sample 媒体样本
type Sample struct {
	Data      []byte        // 媒体数据
	Duration  time.Duration // 样本持续时间
	Timestamp time.Duration // 时间戳
}

// MediaType 媒体类型
type MediaType int

const (
	MediaTypeVideo MediaType = iota
	MediaTypeAudio
)

// String 返回媒体类型的字符串表示
func (mt MediaType) String() string {
	switch mt {
	case MediaTypeVideo:
		return "Video"
	case MediaTypeAudio:
		return "Audio"
	default:
		return "Unknown"
	}
}

// VideoFormat 视频格式
type VideoFormat struct {
	Width       int    // 宽度
	Height      int    // 高度
	PixelFormat string // 像素格式
	Codec       string // 编解码器
	Bitrate     int    // 码率
	Framerate   int    // 帧率
}

// AudioFormat 音频格式
type AudioFormat struct {
	SampleRate int    // 采样率
	Channels   int    // 声道数
	SampleSize int    // 样本大小
	Codec      string // 编解码器
	Bitrate    int    // 码率
}

// MediaInfo 媒体信息
type MediaInfo struct {
	Type        MediaType    // 媒体类型
	VideoFormat *VideoFormat // 视频格式（如果是视频）
	AudioFormat *AudioFormat // 音频格式（如果是音频）
	Duration    time.Duration // 总时长
}

// FrameInfo 帧信息
type FrameInfo struct {
	Type      MediaType     // 帧类型
	Data      []byte        // 帧数据
	Timestamp time.Duration // 时间戳
	Duration  time.Duration // 持续时间
	KeyFrame  bool          // 是否为关键帧
	Size      int           // 数据大小
}

// PacketInfo 数据包信息
type PacketInfo struct {
	Type         MediaType     // 数据包类型
	Data         []byte        // 数据包数据
	Timestamp    time.Duration // 时间戳
	Duration     time.Duration // 持续时间
	SequenceNum  uint16        // 序列号
	PayloadType  uint8         // 负载类型
	SSRC         uint32        // 同步源标识符
	Marker       bool          // 标记位
}

// MediaStats 媒体统计信息
type MediaStats struct {
	Type          MediaType     // 媒体类型
	TotalFrames   uint64        // 总帧数
	TotalBytes    uint64        // 总字节数
	DroppedFrames uint64        // 丢帧数
	Errors        uint64        // 错误数
	StartTime     time.Time     // 开始时间
	LastFrameTime time.Time     // 最后一帧时间
	Bitrate       float64       // 当前码率
	Framerate     float64       // 当前帧率
}

// QualityMetrics 质量指标
type QualityMetrics struct {
	PacketLoss    float64 // 丢包率
	Jitter        float64 // 抖动
	RTT           float64 // 往返时延
	Bandwidth     float64 // 带宽
	QualityScore  float64 // 质量评分 (0-1)
}

// MediaCapabilities 媒体能力
type MediaCapabilities struct {
	SupportedVideoCodecs []string      // 支持的视频编解码器
	SupportedAudioCodecs []string      // 支持的音频编解码器
	MaxVideoResolution   VideoFormat   // 最大视频分辨率
	MaxAudioSampleRate   int           // 最大音频采样率
	SupportedFormats     []string      // 支持的格式
}

// MediaConfig 媒体配置
type MediaConfig struct {
	Video VideoFormat // 视频配置
	Audio AudioFormat // 音频配置
}

// NewSample 创建新的媒体样本
func NewSample(data []byte, duration, timestamp time.Duration) Sample {
	return Sample{
		Data:      data,
		Duration:  duration,
		Timestamp: timestamp,
	}
}

// NewVideoFormat 创建视频格式
func NewVideoFormat(width, height int, codec string, bitrate, framerate int) VideoFormat {
	return VideoFormat{
		Width:       width,
		Height:      height,
		PixelFormat: "yuv420p",
		Codec:       codec,
		Bitrate:     bitrate,
		Framerate:   framerate,
	}
}

// NewAudioFormat 创建音频格式
func NewAudioFormat(sampleRate, channels int, codec string, bitrate int) AudioFormat {
	return AudioFormat{
		SampleRate: sampleRate,
		Channels:   channels,
		SampleSize: 16, // 默认16位
		Codec:      codec,
		Bitrate:    bitrate,
	}
}

// IsValid 检查样本是否有效
func (s *Sample) IsValid() bool {
	return len(s.Data) > 0 && s.Duration > 0
}

// Size 返回样本数据大小
func (s *Sample) Size() int {
	return len(s.Data)
}

// Clone 克隆样本
func (s *Sample) Clone() Sample {
	data := make([]byte, len(s.Data))
	copy(data, s.Data)
	return Sample{
		Data:      data,
		Duration:  s.Duration,
		Timestamp: s.Timestamp,
	}
}

// IsVideo 检查是否为视频格式
func (vf *VideoFormat) IsValid() bool {
	return vf.Width > 0 && vf.Height > 0 && vf.Codec != ""
}

// IsAudio 检查是否为音频格式
func (af *AudioFormat) IsValid() bool {
	return af.SampleRate > 0 && af.Channels > 0 && af.Codec != ""
}

// GetBytesPerSecond 获取每秒字节数
func (af *AudioFormat) GetBytesPerSecond() int {
	return af.SampleRate * af.Channels * (af.SampleSize / 8)
}

// GetFrameSize 获取帧大小（字节）
func (af *AudioFormat) GetFrameSize(durationMs int) int {
	return af.GetBytesPerSecond() * durationMs / 1000
}

// GetPixelCount 获取像素数量
func (vf *VideoFormat) GetPixelCount() int {
	return vf.Width * vf.Height
}

// GetAspectRatio 获取宽高比
func (vf *VideoFormat) GetAspectRatio() float64 {
	if vf.Height == 0 {
		return 0
	}
	return float64(vf.Width) / float64(vf.Height)
}
