package cli

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"go-videocall-peer/internal/debug"
	"go-videocall-peer/internal/videocall"
)

// CLI 命令行界面
type CLI struct {
	peer    *videocall.VideoCallPeer
	reader  *bufio.Reader
	quit    chan bool
	monitor *debug.Monitor
}

// NewCLI 创建新的命令行界面
func NewCLI(peer *videocall.VideoCallPeer) *CLI {
	return &CLI{
		peer:   peer,
		reader: bufio.NewReader(os.Stdin),
		quit:   make(chan bool),
	}
}

// Start 启动命令行界面
func (cli *CLI) Start() {
	fmt.Println("=== Go VideoCall Peer ===")
	fmt.Println("与ESP32 videocall_demo设备进行双向视频通话")
	fmt.Println("输入 'help' 查看可用命令")
	fmt.Println()

	// 设置回调函数
	cli.setupCallbacks()

	// 主命令循环
	for {
		select {
		case <-cli.quit:
			return
		default:
			fmt.Print("videocall> ")
			input, err := cli.reader.ReadString('\n')
			if err != nil {
				log.Printf("读取输入失败: %v", err)
				continue
			}

			input = strings.TrimSpace(input)
			if input == "" {
				continue
			}

			if !cli.handleCommand(input) {
				return
			}
		}
	}
}

// Stop 停止命令行界面
func (cli *CLI) Stop() {
	close(cli.quit)
}

// handleCommand 处理命令
func (cli *CLI) handleCommand(input string) bool {
	parts := strings.Fields(input)
	if len(parts) == 0 {
		return true
	}

	cmd := strings.ToLower(parts[0])

	switch cmd {
	case "help", "h":
		cli.showHelp()

	case "join", "j":
		if len(parts) < 2 {
			fmt.Println("用法: join <房间ID>")
			fmt.Println("示例: join myroom123")
			return true
		}
		roomID := parts[1]
		cli.joinRoom(roomID)

	case "leave", "l":
		cli.leaveRoom()

	case "call", "c":
		cli.startCall()

	case "accept", "a":
		cli.acceptCall()

	case "reject", "r":
		cli.rejectCall()

	case "hangup", "hang":
		cli.hangupCall()

	case "status", "s":
		cli.showStatus()

	case "stats":
		cli.showStats()

	case "debug", "d":
		cli.showDebugInfo()

	case "monitor", "m":
		cli.toggleMonitor()

	case "files", "f":
		cli.showFileInfo()

	case "test":
		if len(parts) >= 2 {
			duration, err := strconv.Atoi(parts[1])
			if err != nil {
				fmt.Printf("无效的测试时长: %s\n", parts[1])
				return true
			}
			cli.runTest(time.Duration(duration) * time.Second)
		} else {
			cli.runTest(10 * time.Second)
		}

	case "quit", "exit", "q":
		fmt.Println("正在退出...")
		cli.peer.Close()
		return false

	default:
		fmt.Printf("未知命令: %s\n", cmd)
		fmt.Println("输入 'help' 查看可用命令")
	}

	return true
}

// setupCallbacks 设置回调函数
func (cli *CLI) setupCallbacks() {
	cli.peer.SetCallbacks(
		// onStateChange
		func(state videocall.CallState) {
			fmt.Printf("\n[状态变化] %s\n", state.String())
			fmt.Print("videocall> ")
		},
		// onCallReceived
		func(caller string) {
			fmt.Printf("\n[来电] 收到来自 %s 的呼叫\n", caller)
			fmt.Println("输入 'accept' 接听或 'reject' 拒绝")
			fmt.Print("videocall> ")
		},
		// onCallConnected
		func() {
			fmt.Printf("\n[通话已连接] 视频通话已建立\n")
			fmt.Println("输入 'hangup' 挂断通话")
			fmt.Print("videocall> ")
		},
		// onCallEnded
		func(reason string) {
			fmt.Printf("\n[通话结束] %s\n", reason)
			fmt.Print("videocall> ")
		},
		// onError
		func(err error) {
			fmt.Printf("\n[错误] %v\n", err)
			fmt.Print("videocall> ")
		},
	)
}

// showHelp 显示帮助信息
func (cli *CLI) showHelp() {
	fmt.Println("\n=== 可用命令 ===")
	fmt.Println("基本操作:")
	fmt.Println("  join <房间ID>     - 加入房间 (与ESP32设备使用相同房间ID)")
	fmt.Println("  leave             - 离开当前房间")
	fmt.Println("  status            - 显示当前状态")
	fmt.Println("  stats             - 显示统计信息")
	fmt.Println()
	fmt.Println("通话操作:")
	fmt.Println("  call              - 发起呼叫")
	fmt.Println("  accept            - 接听来电")
	fmt.Println("  reject            - 拒绝来电")
	fmt.Println("  hangup            - 挂断通话")
	fmt.Println()
	fmt.Println("调试功能:")
	fmt.Println("  debug             - 显示详细调试信息")
	fmt.Println("  monitor           - 切换调试监控 (自动显示统计)")
	fmt.Println("  files             - 显示保存文件状态")
	fmt.Println("  test [秒数]       - 运行媒体测试 (默认10秒)")
	fmt.Println()
	fmt.Println("其他:")
	fmt.Println("  help              - 显示此帮助信息")
	fmt.Println("  quit              - 退出程序")
	fmt.Println()
	fmt.Println("快捷键: j=join, l=leave, c=call, a=accept, r=reject, s=status, d=debug, m=monitor, f=files, h=help, q=quit")
	fmt.Println("===============\n")
}

// joinRoom 加入房间
func (cli *CLI) joinRoom(roomID string) {
	fmt.Printf("正在加入房间: %s\n", roomID)

	if err := cli.peer.JoinRoom(roomID); err != nil {
		fmt.Printf("加入房间失败: %v\n", err)
		return
	}

	fmt.Printf("成功加入房间: %s\n", roomID)
	fmt.Println("现在可以发起呼叫或等待来电")
}

// leaveRoom 离开房间
func (cli *CLI) leaveRoom() {
	fmt.Println("正在离开房间...")

	if err := cli.peer.LeaveRoom(); err != nil {
		fmt.Printf("离开房间失败: %v\n", err)
		return
	}

	fmt.Println("已离开房间")
}

// startCall 发起呼叫
func (cli *CLI) startCall() {
	state := cli.peer.GetState()
	if state != videocall.StateConnected {
		fmt.Printf("当前状态不允许发起呼叫: %s\n", state.String())
		fmt.Println("请先加入房间")
		return
	}

	fmt.Println("正在发起呼叫...")

	if err := cli.peer.StartCall(); err != nil {
		fmt.Printf("发起呼叫失败: %v\n", err)
		return
	}

	fmt.Println("呼叫已发起，等待对方响应...")
}

// acceptCall 接听来电
func (cli *CLI) acceptCall() {
	state := cli.peer.GetState()
	if state != videocall.StateIncomingCall && state != videocall.StateRinging {
		fmt.Printf("当前状态不允许接听: %s\n", state.String())
		return
	}

	fmt.Println("正在接听来电...")

	if err := cli.peer.AcceptCall(); err != nil {
		fmt.Printf("接听失败: %v\n", err)
		return
	}

	fmt.Println("来电已接听，正在建立连接...")
}

// rejectCall 拒绝来电
func (cli *CLI) rejectCall() {
	state := cli.peer.GetState()
	if state != videocall.StateIncomingCall && state != videocall.StateRinging {
		fmt.Printf("当前状态不允许拒绝: %s\n", state.String())
		return
	}

	fmt.Println("正在拒绝来电...")

	if err := cli.peer.RejectCall(); err != nil {
		fmt.Printf("拒绝失败: %v\n", err)
		return
	}

	fmt.Println("来电已拒绝")
}

// hangupCall 挂断通话
func (cli *CLI) hangupCall() {
	state := cli.peer.GetState()
	if state != videocall.StateInCall && state != videocall.StateNegotiating {
		fmt.Printf("当前状态不允许挂断: %s\n", state.String())
		return
	}

	fmt.Println("正在挂断通话...")

	if err := cli.peer.HangupCall(); err != nil {
		fmt.Printf("挂断失败: %v\n", err)
		return
	}

	fmt.Println("通话已挂断")
}

// showStatus 显示状态
func (cli *CLI) showStatus() {
	state := cli.peer.GetState()
	stats := cli.peer.GetStats()

	fmt.Println("\n=== 当前状态 ===")
	fmt.Printf("状态: %s\n", state.String())

	if room, ok := stats["room"].(string); ok && room != "" {
		fmt.Printf("房间: %s\n", room)
	}

	fmt.Println("================\n")
}

// showStats 显示统计信息
func (cli *CLI) showStats() {
	stats := cli.peer.GetStats()

	fmt.Println("\n=== 统计信息 ===")

	// 显示捕获统计
	if captureStats, ok := stats["capture"]; ok {
		fmt.Println("媒体捕获:")
		fmt.Printf("  %+v\n", captureStats)
	}

	// 显示播放统计
	if playerStats, ok := stats["player"]; ok {
		fmt.Println("媒体播放:")
		fmt.Printf("  %+v\n", playerStats)
	}

	// 显示WebRTC统计
	if webrtcStats, ok := stats["webrtc"]; ok {
		fmt.Println("WebRTC连接:")
		fmt.Printf("  %+v\n", webrtcStats)
	}

	fmt.Println("================\n")
}

// runTest 运行测试
func (cli *CLI) runTest(duration time.Duration) {
	fmt.Printf("运行媒体测试 %v...\n", duration)

	// 这里可以添加媒体测试逻辑
	// 例如测试摄像头捕获、编码器性能等

	fmt.Printf("媒体测试完成\n")
}

// showDebugInfo 显示调试信息
func (cli *CLI) showDebugInfo() {
	fmt.Println("\n=== 调试信息 ===")

	// 显示WebRTC连接状态
	if webrtcManager := cli.peer.GetWebRTCManager(); webrtcManager != nil {
		state := webrtcManager.GetConnectionState()
		fmt.Printf("WebRTC连接状态: %s\n", state.String())

		stats := webrtcManager.GetStats()
		fmt.Printf("连接统计: 发送=%d包, 接收=%d包\n", stats.PacketsSent, stats.PacketsReceived)
	}

	// 显示媒体播放器状态
	if mediaPlayer := cli.peer.GetMediaPlayer(); mediaPlayer != nil {
		videoFrames, videoBytes, fps := mediaPlayer.GetVideoStats()
		audioFrames, audioBytes := mediaPlayer.GetAudioStats()

		fmt.Printf("视频统计: %d帧, %d字节, %.1f FPS\n", videoFrames, videoBytes, fps)
		fmt.Printf("音频统计: %d帧, %d字节\n", audioFrames, audioBytes)

		if videoFrames == 0 && audioFrames == 0 {
			fmt.Println("⚠️  警告: 没有接收到任何媒体数据！")
		}
	}

	// 显示文件状态
	cli.showFileInfo()

	fmt.Println("================\n")
}

// toggleMonitor 切换监控状态
func (cli *CLI) toggleMonitor() {
	if cli.monitor == nil {
		// 启动监控
		webrtcManager := cli.peer.GetWebRTCManager()
		mediaPlayer := cli.peer.GetMediaPlayer()
		mediaCapture := cli.peer.GetMediaCapture()

		cli.monitor = debug.NewMonitor(webrtcManager, mediaPlayer, mediaCapture)
		cli.monitor.Start()
		fmt.Println("✅ 调试监控已启动")
	} else {
		// 停止监控
		cli.monitor.Stop()
		cli.monitor = nil
		fmt.Println("⏹️  调试监控已停止")
	}
}

// showFileInfo 显示文件信息
func (cli *CLI) showFileInfo() {
	fmt.Println("\n--- 文件状态 ---")

	files := map[string]string{
		"debug/received_video.h264": "视频文件",
		"debug/received_audio.pcma": "音频文件",
		"debug/monitor.log":         "监控日志",
	}

	for file, desc := range files {
		if info, err := os.Stat(file); err == nil {
			if info.Size() > 0 {
				fmt.Printf("✅ %s: %d bytes\n", desc, info.Size())
			} else {
				fmt.Printf("⚠️  %s: 0 bytes (文件存在但为空)\n", desc)
			}
		} else {
			fmt.Printf("❌ %s: 不存在\n", desc)
		}
	}

	fmt.Println("----------------")
}
