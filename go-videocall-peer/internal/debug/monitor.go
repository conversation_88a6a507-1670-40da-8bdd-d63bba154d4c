package debug

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"go-videocall-peer/internal/media"
	webrtcmgr "go-videocall-peer/internal/webrtc"
)

// Monitor 调试监控器
type Monitor struct {
	ctx    context.Context
	cancel context.CancelFunc

	webrtcManager *webrtcmgr.ConnectionManager
	mediaPlayer   *media.MediaPlayer
	mediaCapture  *media.MediaCapture

	logFile *os.File
}

// NewMonitor 创建新的调试监控器
func NewMonitor(webrtcManager *webrtcmgr.ConnectionManager, mediaPlayer *media.MediaPlayer, mediaCapture *media.MediaCapture) *Monitor {
	ctx, cancel := context.WithCancel(context.Background())

	monitor := &Monitor{
		ctx:           ctx,
		cancel:        cancel,
		webrtcManager: webrtcManager,
		mediaPlayer:   mediaPlayer,
		mediaCapture:  mediaCapture,
	}

	// 创建调试日志文件
	if logFile, err := os.Create("debug/monitor.log"); err == nil {
		monitor.logFile = logFile
	}

	return monitor
}

// Start 开始监控
func (m *Monitor) Start() {
	log.Println("[MONITOR] 开始调试监控")

	// 启动统计信息监控
	go m.monitorStats()

	// 启动连接状态监控
	go m.monitorConnection()

	// 启动文件大小监控
	go m.monitorFiles()
}

// Stop 停止监控
func (m *Monitor) Stop() {
	m.cancel()

	if m.logFile != nil {
		m.logFile.Close()
	}

	log.Println("[MONITOR] 调试监控已停止")
}

// monitorStats 监控统计信息
func (m *Monitor) monitorStats() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.printStats()
		}
	}
}

// monitorConnection 监控连接状态
func (m *Monitor) monitorConnection() {
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.printConnectionStatus()
		}
	}
}

// monitorFiles 监控文件大小
func (m *Monitor) monitorFiles() {
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.printFileSizes()
		}
	}
}

// printStats 打印统计信息
func (m *Monitor) printStats() {
	if m.mediaPlayer == nil {
		return
	}

	videoFrames, videoBytes, fps := m.mediaPlayer.GetVideoStats()
	audioFrames, audioBytes := m.mediaPlayer.GetAudioStats()

	msg := fmt.Sprintf("[MONITOR] 统计信息 - 视频: %d帧/%d字节/%.1fFPS, 音频: %d帧/%d字节",
		videoFrames, videoBytes, fps, audioFrames, audioBytes)

	log.Println(msg)
	m.writeToLog(msg)

	// 如果没有接收到任何数据，打印警告
	if videoFrames == 0 && audioFrames == 0 {
		warning := "[MONITOR] ⚠️  警告: 没有接收到任何媒体数据！"
		log.Println(warning)
		m.writeToLog(warning)
	}
}

// printConnectionStatus 打印连接状态
func (m *Monitor) printConnectionStatus() {
	if m.webrtcManager == nil {
		return
	}

	state := m.webrtcManager.GetConnectionState()
	stats := m.webrtcManager.GetStats()

	msg := fmt.Sprintf("[MONITOR] WebRTC状态: %s, 连接时间: %v",
		state.String(), time.Since(stats.ConnectedAt))

	log.Println(msg)
	m.writeToLog(msg)

	// 如果连接已建立但没有数据传输，打印警告
	if state.String() == "connected" && time.Since(stats.ConnectedAt) > 10*time.Second {
		if stats.PacketsReceived == 0 {
			warning := "[MONITOR] ⚠️  警告: WebRTC已连接但没有接收到数据包！"
			log.Println(warning)
			m.writeToLog(warning)
		}
	}
}

// printFileSizes 打印文件大小
func (m *Monitor) printFileSizes() {
	files := []string{
		"debug/received_video.h264",
		"debug/received_audio.pcma",
	}

	for _, file := range files {
		if info, err := os.Stat(file); err == nil {
			msg := fmt.Sprintf("[MONITOR] 文件大小: %s = %d bytes", file, info.Size())
			log.Println(msg)
			m.writeToLog(msg)

			// 如果文件大小为0，打印警告
			if info.Size() == 0 {
				warning := fmt.Sprintf("[MONITOR] ⚠️  警告: 文件 %s 大小为0！", file)
				log.Println(warning)
				m.writeToLog(warning)
			}
		}
	}
}

// writeToLog 写入日志文件
func (m *Monitor) writeToLog(msg string) {
	if m.logFile != nil {
		timestamp := time.Now().Format("2006-01-02 15:04:05")
		m.logFile.WriteString(fmt.Sprintf("[%s] %s\n", timestamp, msg))
		m.logFile.Sync()
	}
}

// PrintDiagnostics 打印诊断信息
func (m *Monitor) PrintDiagnostics() {
	log.Println("\n=== 调试诊断信息 ===")

	// WebRTC连接状态
	if m.webrtcManager != nil {
		state := m.webrtcManager.GetConnectionState()
		log.Printf("WebRTC连接状态: %s", state.String())

		if state.String() != "connected" {
			log.Println("❌ WebRTC连接未建立 - 这是主要问题！")
			log.Println("   建议检查:")
			log.Println("   1. ICE候选交换是否成功")
			log.Println("   2. STUN/TURN服务器配置")
			log.Println("   3. 网络防火墙设置")
		} else {
			log.Println("✅ WebRTC连接已建立")
		}
	}

	// 媒体轨道状态
	if m.mediaPlayer != nil {
		videoFrames, _, _ := m.mediaPlayer.GetVideoStats()
		audioFrames, _ := m.mediaPlayer.GetAudioStats()

		if videoFrames == 0 {
			log.Println("❌ 没有接收到视频数据")
			log.Println("   可能原因:")
			log.Println("   1. ESP32没有发送视频流")
			log.Println("   2. 视频轨道没有正确设置")
			log.Println("   3. 编解码器不匹配")
		} else {
			log.Printf("✅ 已接收到 %d 个视频帧", videoFrames)
		}

		if audioFrames == 0 {
			log.Println("❌ 没有接收到音频数据")
		} else {
			log.Printf("✅ 已接收到 %d 个音频帧", audioFrames)
		}
	}

	// 文件状态
	files := map[string]string{
		"debug/received_video.h264": "视频文件",
		"debug/received_audio.pcma": "音频文件",
	}

	for file, desc := range files {
		if info, err := os.Stat(file); err == nil {
			if info.Size() > 0 {
				log.Printf("✅ %s: %d bytes", desc, info.Size())
			} else {
				log.Printf("❌ %s: 0 bytes (文件存在但为空)", desc)
			}
		} else {
			log.Printf("❌ %s: 不存在", desc)
		}
	}

	log.Println("\n=== 建议的调试步骤 ===")
	log.Println("1. 检查WebRTC连接状态是否为'connected'")
	log.Println("2. 确认ESP32是否正在发送媒体流")
	log.Println("3. 检查编解码器配置是否匹配")
	log.Println("4. 验证轨道设置回调是否被调用")
	log.Println("5. 监控RTP包接收情况")
	log.Println("==================\n")
}

// GetRecommendations 获取调试建议
func (m *Monitor) GetRecommendations() []string {
	var recommendations []string

	// 检查WebRTC状态
	if m.webrtcManager != nil {
		state := m.webrtcManager.GetConnectionState()
		if state.String() != "connected" {
			recommendations = append(recommendations,
				"WebRTC连接未建立，检查ICE候选交换和STUN/TURN配置")
		}
	}

	// 检查媒体数据
	if m.mediaPlayer != nil {
		videoFrames, _, _ := m.mediaPlayer.GetVideoStats()
		audioFrames, _ := m.mediaPlayer.GetAudioStats()

		if videoFrames == 0 && audioFrames == 0 {
			recommendations = append(recommendations,
				"没有接收到任何媒体数据，检查ESP32是否正在发送流")
		}
	}

	// 检查文件大小
	if info, err := os.Stat("debug/received_video.h264"); err == nil && info.Size() == 0 {
		recommendations = append(recommendations,
			"视频文件为空，检查H.264处理器和RTP解析")
	}

	if len(recommendations) == 0 {
		recommendations = append(recommendations, "系统运行正常，继续监控")
	}

	return recommendations
}
