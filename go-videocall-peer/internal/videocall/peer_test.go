package videocall

import (
	"testing"
	"time"

	"go-videocall-peer/config"
)

// TestStateManager 测试状态管理器
func TestStateManager(t *testing.T) {
	sm := NewStateManager()
	
	// 测试初始状态
	if sm.GetState() != StateIdle {
		t.<PERSON><PERSON><PERSON>("期望初始状态为 StateIdle，实际为 %s", sm.GetState())
	}
	
	// 测试状态转换
	err := sm.TransitionTo(StateConnecting, "测试连接")
	if err != nil {
		t.<PERSON>rrorf("状态转换失败: %v", err)
	}
	
	if sm.GetState() != StateConnecting {
		t.<PERSON><PERSON><PERSON>("期望状态为 StateConnecting，实际为 %s", sm.GetState())
	}
	
	// 测试无效状态转换
	err = sm.TransitionTo(StateInCall, "无效转换")
	if err == nil {
		t.Error("期望无效状态转换返回错误")
	}
}

// TestVideoCallPeerCreation 测试VideoCallPeer创建
func TestVideoCallPeerCreation(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		App: config.AppConfig{
			Name:    "test",
			Version: "1.0.0",
			Debug:   true,
		},
		Signaling: config.SignalingConfig{
			ServerURL:         "https://webrtc.espressif.cn",
			ConnectTimeout:    30 * time.Second,
			MessageTimeout:    10 * time.Second,
			HeartbeatInterval: 30 * time.Second,
		},
		WebRTC: config.WebRTCConfig{
			ICEServers: []config.ICEServerConfig{
				{URLs: []string{"stun:stun.l.google.com:19302"}},
			},
			ICECandidateTimeout:   10 * time.Second,
			ConnectionTimeout:     30 * time.Second,
			EnableDataChannel:     true,
			VideoOverDataChannel:  true,
			DataChannelBufferSize: 409600,
		},
		Video: config.VideoConfig{
			DeviceID:    "0",
			InputFormat: "test",
			Width:       640,
			Height:      480,
			FPS:         15,
			Bitrate:     500000,
			Codec:       "h264",
			Profile:     "baseline",
			Level:       "3.1",
			PixelFormat: "yuv420p",
		},
		Audio: config.AudioConfig{
			DeviceID:    "",
			InputFormat: "test",
			SampleRate:  8000,
			Channels:    1,
			Bitrate:     64000,
			Codec:       "pcma",
			FrameSize:   160,
		},
		Playback: config.PlaybackConfig{
			Video: config.VideoPlaybackConfig{
				Enable:      true,
				Renderer:    "auto",
				WindowTitle: "Test Video",
			},
			Audio: config.AudioPlaybackConfig{
				Enable:     true,
				Device:     "",
				BufferSize: 1024,
			},
		},
		Call: config.CallConfig{
			RingTimeout:           30 * time.Second,
			ConnectTimeout:        60 * time.Second,
			MaxRetries:            3,
			RetryInterval:         5 * time.Second,
			QualityCheckInterval:  5 * time.Second,
			MinQualityThreshold:   0.5,
		},
		Logging: config.LoggingConfig{
			Level:      "info",
			Format:     "text",
			Output:     "console",
			FilePath:   "",
			Components: make(map[string]string),
		},
		Debug: config.DebugConfig{
			SaveFrames:     false,
			FrameSavePath:  "debug/frames",
			SaveAudio:      false,
			AudioSavePath:  "debug/audio",
			SavePackets:    false,
			PacketSavePath: "debug/packets",
			EnableStats:    true,
			StatsInterval:  5 * time.Second,
			StatsSavePath:  "debug/stats",
		},
		Performance: config.PerformanceConfig{
			MaxGoroutines:     100,
			WorkerPoolSize:    10,
			MaxMemoryMB:       512,
			GCPercent:         100,
			VideoBufferSize:   10,
			AudioBufferSize:   20,
			NetworkBufferSize: 1024,
		},
	}
	
	// 创建VideoCallPeer
	peer, err := NewVideoCallPeer(cfg)
	if err != nil {
		t.Fatalf("创建VideoCallPeer失败: %v", err)
	}
	defer peer.Close()
	
	// 测试初始状态
	if peer.GetState() != StateIdle {
		t.Errorf("期望初始状态为 StateIdle，实际为 %s", peer.GetState())
	}
	
	// 测试初始化
	err = peer.Initialize()
	if err != nil {
		t.Errorf("初始化失败: %v", err)
	}
}

// TestSignalingCommands 测试信令命令
func TestSignalingCommands(t *testing.T) {
	commands := []VideoCallCommand{
		CmdRing,
		CmdAcceptCall,
		CmdDenyCall,
		CmdCallRequest,
		CmdCallResponse,
		CmdCallHangup,
		CmdCallBusy,
		CmdCallTimeout,
		CmdHeartbeat,
		CmdDeviceInfo,
	}
	
	for _, cmd := range commands {
		if string(cmd) == "" {
			t.Errorf("命令 %v 不应为空", cmd)
		}
	}
}

// TestCallSession 测试通话会话
func TestCallSession(t *testing.T) {
	session := &CallSession{
		SessionID: "test-session",
		PeerID:    "test-peer",
		RoomID:    "test-room",
		Role:      RoleCaller,
		StartTime: time.Now(),
	}
	
	if session.SessionID != "test-session" {
		t.Errorf("期望SessionID为 'test-session'，实际为 '%s'", session.SessionID)
	}
	
	if session.Role != RoleCaller {
		t.Errorf("期望Role为 RoleCaller，实际为 %s", session.Role)
	}
}

// TestStateTransitions 测试状态转换
func TestStateTransitions(t *testing.T) {
	sm := NewStateManager()
	
	// 测试正常的状态转换序列
	transitions := []struct {
		from   CallState
		to     CallState
		reason string
		valid  bool
	}{
		{StateIdle, StateConnecting, "开始连接", true},
		{StateConnecting, StateConnected, "连接成功", true},
		{StateConnected, StateOutgoingCall, "发起呼叫", true},
		{StateOutgoingCall, StateRinging, "开始响铃", true},
		{StateRinging, StateNegotiating, "开始协商", true},
		{StateNegotiating, StateInCall, "通话建立", true},
		{StateInCall, StateHangingUp, "挂断通话", true},
		{StateHangingUp, StateConnected, "挂断完成", true},
		
		// 测试无效转换
		{StateIdle, StateInCall, "无效转换", false},
		{StateConnected, StateHangingUp, "无效转换", false},
	}
	
	for _, transition := range transitions {
		// 设置起始状态
		sm.currentState = transition.from
		
		err := sm.TransitionTo(transition.to, transition.reason)
		
		if transition.valid && err != nil {
			t.Errorf("有效转换 %s -> %s 失败: %v", transition.from, transition.to, err)
		}
		
		if !transition.valid && err == nil {
			t.Errorf("无效转换 %s -> %s 应该失败", transition.from, transition.to)
		}
		
		if transition.valid && sm.GetState() != transition.to {
			t.Errorf("转换后状态不正确，期望 %s，实际 %s", transition.to, sm.GetState())
		}
	}
}

// BenchmarkStateTransition 状态转换性能测试
func BenchmarkStateTransition(b *testing.B) {
	sm := NewStateManager()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		sm.TransitionTo(StateConnecting, "性能测试")
		sm.TransitionTo(StateConnected, "性能测试")
		sm.TransitionTo(StateIdle, "性能测试")
	}
}

// TestCallRoles 测试通话角色
func TestCallRoles(t *testing.T) {
	roles := []CallRole{RoleNone, RoleCaller, RoleCallee}
	
	for _, role := range roles {
		roleStr := role.String()
		if roleStr == "" || roleStr == "Unknown" {
			t.Errorf("角色 %v 的字符串表示不应为空或Unknown", role)
		}
	}
}

// TestStateStrings 测试状态字符串表示
func TestStateStrings(t *testing.T) {
	states := []CallState{
		StateIdle,
		StateConnecting,
		StateConnected,
		StateOutgoingCall,
		StateIncomingCall,
		StateRinging,
		StateNegotiating,
		StateInCall,
		StateHangingUp,
		StateDisconnected,
		StateError,
	}
	
	for _, state := range states {
		stateStr := state.String()
		if stateStr == "" || stateStr == "Unknown" {
			t.Errorf("状态 %v 的字符串表示不应为空或Unknown", state)
		}
	}
}
