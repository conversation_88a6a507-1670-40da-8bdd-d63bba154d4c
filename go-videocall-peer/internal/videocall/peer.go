package videocall

import (
	"context"
	"fmt"
	"log"
	"sync"

	"go-videocall-peer/config"
	"go-videocall-peer/internal/media"
	webrtcmgr "go-videocall-peer/internal/webrtc"

	"github.com/pion/webrtc/v4"
)

// VideoCallPeer 视频通话对等端
type VideoCallPeer struct {
	config *config.Config
	ctx    context.Context
	cancel context.CancelFunc
	mutex  sync.RWMutex

	// 核心组件
	stateManager    *StateManager
	signalingClient *SignalingClient
	webrtcManager   *webrtcmgr.ConnectionManager
	mediaCapture    *media.MediaCapture
	mediaPlayer     *media.MediaPlayer

	// 当前状态
	currentRoom string
	peerID      string

	// 回调函数
	onStateChange   func(CallState)
	onCallReceived  func(string)
	onCallConnected func()
	onCallEnded     func(string)
	onError         func(error)
}

// NewVideoCallPeer 创建新的视频通话对等端
func NewVideoCallPeer(cfg *config.Config) (*VideoCallPeer, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建状态管理器
	stateManager := NewStateManager()

	// 创建信令客户端
	signalingClient := NewSignalingClient(cfg.Signaling.ServerURL)

	// 创建WebRTC连接管理器
	webrtcManager, err := webrtcmgr.NewConnectionManager(cfg)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建WebRTC管理器失败: %v", err)
	}

	// 创建媒体捕获器
	mediaCapture, err := media.NewMediaCapture(cfg)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建媒体捕获器失败: %v", err)
	}

	// 创建媒体播放器
	mediaPlayer := media.NewMediaPlayer(cfg)

	peer := &VideoCallPeer{
		config:          cfg,
		ctx:             ctx,
		cancel:          cancel,
		stateManager:    stateManager,
		signalingClient: signalingClient,
		webrtcManager:   webrtcManager,
		mediaCapture:    mediaCapture,
		mediaPlayer:     mediaPlayer,
	}

	// 设置回调函数
	peer.setupCallbacks()

	return peer, nil
}

// Initialize 初始化对等端
func (vcp *VideoCallPeer) Initialize() error {
	log.Println("[PEER] 初始化视频通话对等端")

	// 初始化媒体捕获器
	if err := vcp.mediaCapture.Initialize(); err != nil {
		return fmt.Errorf("初始化媒体捕获器失败: %v", err)
	}

	// 创建WebRTC连接
	if err := vcp.webrtcManager.CreatePeerConnection(); err != nil {
		return fmt.Errorf("创建WebRTC连接失败: %v", err)
	}

	log.Println("[PEER] 视频通话对等端初始化完成")
	return nil
}

// JoinRoom 加入房间
func (vcp *VideoCallPeer) JoinRoom(roomID string) error {
	vcp.mutex.Lock()
	defer vcp.mutex.Unlock()

	if vcp.stateManager.GetState() != StateIdle {
		return fmt.Errorf("当前状态不允许加入房间: %s", vcp.stateManager.GetState())
	}

	log.Printf("[PEER] 加入房间: %s", roomID)

	// 转换状态
	if err := vcp.stateManager.TransitionTo(StateConnecting, "加入房间"); err != nil {
		return err
	}

	// 连接信令服务器
	if err := vcp.signalingClient.Connect(roomID); err != nil {
		vcp.stateManager.TransitionTo(StateError, fmt.Sprintf("信令连接失败: %v", err))
		return fmt.Errorf("连接信令服务器失败: %v", err)
	}

	vcp.currentRoom = roomID

	// 启动媒体捕获
	if err := vcp.mediaCapture.StartCapture(); err != nil {
		log.Printf("[PEER] 启动媒体捕获失败: %v", err)
	}

	// 添加本地媒体轨道到WebRTC连接
	videoTrack := vcp.mediaCapture.GetVideoTrack()
	audioTrack := vcp.mediaCapture.GetAudioTrack()
	if err := vcp.webrtcManager.AddLocalTracks(videoTrack, audioTrack); err != nil {
		log.Printf("[PEER] 添加本地轨道失败: %v", err)
	}

	// 启动媒体播放器
	if err := vcp.mediaPlayer.StartPlayback(); err != nil {
		log.Printf("[PEER] 启动媒体播放器失败: %v", err)
	}

	// 转换到已连接状态
	if err := vcp.stateManager.TransitionTo(StateConnected, "成功连接到房间"); err != nil {
		return err
	}

	log.Printf("[PEER] 成功加入房间: %s", roomID)
	return nil
}

// LeaveRoom 离开房间
func (vcp *VideoCallPeer) LeaveRoom() error {
	vcp.mutex.Lock()
	defer vcp.mutex.Unlock()

	log.Println("[PEER] 离开房间")

	// 停止媒体捕获和播放
	vcp.mediaCapture.StopCapture()
	vcp.mediaPlayer.StopPlayback()

	// 断开信令连接
	vcp.signalingClient.Disconnect()

	// 重置状态
	vcp.stateManager.TransitionTo(StateIdle, "离开房间")
	vcp.currentRoom = ""
	vcp.peerID = ""

	log.Println("[PEER] 已离开房间")
	return nil
}

// StartCall 发起呼叫
func (vcp *VideoCallPeer) StartCall() error {
	vcp.mutex.Lock()
	defer vcp.mutex.Unlock()

	if vcp.stateManager.GetState() != StateConnected {
		return fmt.Errorf("当前状态不允许发起呼叫: %s", vcp.stateManager.GetState())
	}

	log.Println("[PEER] 发起呼叫")

	// 转换状态
	if err := vcp.stateManager.TransitionTo(StateOutgoingCall, "发起呼叫"); err != nil {
		return err
	}

	// 发送RING命令
	if err := vcp.signalingClient.SendCommand(CmdRing, ""); err != nil {
		vcp.stateManager.TransitionTo(StateError, fmt.Sprintf("发送RING命令失败: %v", err))
		return fmt.Errorf("发送RING命令失败: %v", err)
	}

	// 转换到响铃状态
	if err := vcp.stateManager.TransitionTo(StateRinging, "等待对方响应"); err != nil {
		return err
	}

	log.Println("[PEER] 呼叫已发起，等待对方响应")
	return nil
}

// AcceptCall 接受呼叫
func (vcp *VideoCallPeer) AcceptCall() error {
	vcp.mutex.Lock()
	defer vcp.mutex.Unlock()

	if vcp.stateManager.GetState() != StateIncomingCall && vcp.stateManager.GetState() != StateRinging {
		return fmt.Errorf("当前状态不允许接受呼叫: %s", vcp.stateManager.GetState())
	}

	log.Println("[PEER] 接受呼叫")

	// 创建WebRTC PeerConnection
	if err := vcp.webrtcManager.CreatePeerConnection(); err != nil {
		return fmt.Errorf("创建WebRTC连接失败: %v", err)
	}

	// 设置数据通道发送器（用于视频）
	vcp.mediaCapture.SetDataChannelSender(vcp.webrtcManager)

	// 添加本地媒体轨道（只添加音频轨道，视频通过数据通道发送）
	videoTrack := vcp.mediaCapture.GetVideoTrack()
	audioTrack := vcp.mediaCapture.GetAudioTrack()
	if err := vcp.webrtcManager.AddLocalTracks(videoTrack, audioTrack); err != nil {
		return fmt.Errorf("添加本地轨道失败: %v", err)
	}

	// 发送ACCEPT_CALL命令
	if err := vcp.signalingClient.SendCommand(CmdAcceptCall, ""); err != nil {
		return fmt.Errorf("发送ACCEPT_CALL命令失败: %v", err)
	}

	// 转换到协商状态
	if err := vcp.stateManager.TransitionTo(StateNegotiating, "开始WebRTC协商"); err != nil {
		return err
	}

	log.Println("[PEER] 呼叫已接受，开始WebRTC协商")
	return nil
}

// RejectCall 拒绝呼叫
func (vcp *VideoCallPeer) RejectCall() error {
	vcp.mutex.Lock()
	defer vcp.mutex.Unlock()

	if vcp.stateManager.GetState() != StateIncomingCall && vcp.stateManager.GetState() != StateRinging {
		return fmt.Errorf("当前状态不允许拒绝呼叫: %s", vcp.stateManager.GetState())
	}

	log.Println("[PEER] 拒绝呼叫")

	// 发送DENY_CALL命令
	if err := vcp.signalingClient.SendCommand(CmdDenyCall, ""); err != nil {
		return fmt.Errorf("发送DENY_CALL命令失败: %v", err)
	}

	// 转换回连接状态
	if err := vcp.stateManager.TransitionTo(StateConnected, "拒绝呼叫"); err != nil {
		return err
	}

	log.Println("[PEER] 呼叫已拒绝")
	return nil
}

// HangupCall 挂断呼叫
func (vcp *VideoCallPeer) HangupCall() error {
	vcp.mutex.Lock()
	defer vcp.mutex.Unlock()

	currentState := vcp.stateManager.GetState()
	if currentState != StateInCall && currentState != StateNegotiating {
		return fmt.Errorf("当前状态不允许挂断: %s", currentState)
	}

	log.Println("[PEER] 挂断呼叫")

	// 转换到挂断状态
	if err := vcp.stateManager.TransitionTo(StateHangingUp, "挂断呼叫"); err != nil {
		return err
	}

	// 发送DENY_CALL命令（ESP32使用此命令表示挂断）
	if err := vcp.signalingClient.SendCommand(CmdDenyCall, ""); err != nil {
		log.Printf("[PEER] 发送挂断命令失败: %v", err)
	}

	// 转换回连接状态
	if err := vcp.stateManager.TransitionTo(StateConnected, "呼叫已挂断"); err != nil {
		return err
	}

	log.Println("[PEER] 呼叫已挂断")
	return nil
}

// GetState 获取当前状态
func (vcp *VideoCallPeer) GetState() CallState {
	return vcp.stateManager.GetState()
}

// GetStats 获取统计信息
func (vcp *VideoCallPeer) GetStats() map[string]interface{} {
	captureStats := vcp.mediaCapture.GetStats()
	playerStats := vcp.mediaPlayer.GetStats()
	webrtcStats := vcp.webrtcManager.GetStats()

	return map[string]interface{}{
		"state":   vcp.stateManager.GetState().String(),
		"room":    vcp.currentRoom,
		"capture": captureStats,
		"player":  playerStats,
		"webrtc":  webrtcStats,
	}
}

// GetWebRTCManager 获取WebRTC管理器
func (vcp *VideoCallPeer) GetWebRTCManager() *webrtcmgr.ConnectionManager {
	return vcp.webrtcManager
}

// GetMediaPlayer 获取媒体播放器
func (vcp *VideoCallPeer) GetMediaPlayer() *media.MediaPlayer {
	return vcp.mediaPlayer
}

// GetMediaCapture 获取媒体捕获器
func (vcp *VideoCallPeer) GetMediaCapture() *media.MediaCapture {
	return vcp.mediaCapture
}

// Close 关闭对等端
func (vcp *VideoCallPeer) Close() {
	log.Println("[PEER] 开始关闭视频通话对等端...")

	// 先取消上下文，停止所有goroutine
	vcp.cancel()

	// 离开房间（发送离开消息）
	log.Println("[PEER] 离开房间...")
	vcp.LeaveRoom()

	// 按顺序关闭各个组件
	log.Println("[PEER] 关闭媒体捕获器...")
	vcp.mediaCapture.Close()

	log.Println("[PEER] 关闭媒体播放器...")
	vcp.mediaPlayer.Close()

	log.Println("[PEER] 关闭WebRTC管理器...")
	vcp.webrtcManager.Close()

	// 注意：SignalingClient没有Close方法，通过context取消来停止

	log.Println("[PEER] ✅ 视频通话对等端已完全关闭")
}

// SetCallbacks 设置回调函数
func (vcp *VideoCallPeer) SetCallbacks(
	onStateChange func(CallState),
	onCallReceived func(string),
	onCallConnected func(),
	onCallEnded func(string),
	onError func(error),
) {
	vcp.mutex.Lock()
	defer vcp.mutex.Unlock()

	vcp.onStateChange = onStateChange
	vcp.onCallReceived = onCallReceived
	vcp.onCallConnected = onCallConnected
	vcp.onCallEnded = onCallEnded
	vcp.onError = onError
}

// setupCallbacks 设置内部回调函数
func (vcp *VideoCallPeer) setupCallbacks() {
	// 设置状态管理器回调
	vcp.stateManager.SetStateChangeCallback(func(oldState, newState CallState) {
		log.Printf("[PEER] 状态变化: %s -> %s", oldState, newState)
		if vcp.onStateChange != nil {
			vcp.onStateChange(newState)
		}
	})

	// 设置信令客户端回调
	vcp.signalingClient.SetCallbacks(
		// onConnected
		func() {
			log.Println("[PEER] 信令服务器已连接")
		},
		// onDisconnected
		func(err error) {
			log.Printf("[PEER] 信令服务器断开连接: %v", err)
			if err != nil && vcp.onError != nil {
				vcp.onError(fmt.Errorf("信令连接断开: %v", err))
			}
		},
		// onMessage
		func(msg RTCMessage) {
			vcp.handleRTCMessage(msg)
		},
		// onCommand
		func(cmd VideoCallCommand, data string) {
			vcp.handleCustomCommand(cmd, data)
		},
		// onError
		func(err error) {
			log.Printf("[PEER] 信令错误: %v", err)
			if vcp.onError != nil {
				vcp.onError(fmt.Errorf("信令错误: %v", err))
			}
		},
	)

	// 设置WebRTC管理器回调
	vcp.webrtcManager.SetCallbacks(
		// onConnectionStateChange
		func(state webrtc.PeerConnectionState) {
			log.Printf("[PEER] WebRTC连接状态: %s", state.String())

			switch state {
			case webrtc.PeerConnectionStateConnected:
				vcp.stateManager.TransitionTo(StateInCall, "WebRTC连接已建立")
				if vcp.onCallConnected != nil {
					vcp.onCallConnected()
				}
			case webrtc.PeerConnectionStateFailed, webrtc.PeerConnectionStateDisconnected:
				if vcp.stateManager.GetState() == StateInCall {
					vcp.stateManager.TransitionTo(StateConnected, "WebRTC连接断开")
					if vcp.onCallEnded != nil {
						vcp.onCallEnded("连接断开")
					}
				}
			}
		},
		// onICECandidate
		func(candidate *webrtc.ICECandidate) {
			if candidate != nil {
				candidateStr := candidate.ToJSON().Candidate
				if err := vcp.signalingClient.SendICECandidate(candidateStr); err != nil {
					log.Printf("[PEER] 发送ICE候选失败: %v", err)
				}
			}
		},
		// onTrack
		func(track *webrtc.TrackRemote, receiver *webrtc.RTPReceiver) {
			log.Printf("[PEER] 接收到远程轨道: %s (类型: %s)", track.ID(), track.Kind())

			if track.Kind() == webrtc.RTPCodecTypeVideo {
				vcp.mediaPlayer.SetRemoteVideoTrack(track)
			} else if track.Kind() == webrtc.RTPCodecTypeAudio {
				vcp.mediaPlayer.SetRemoteAudioTrack(track)
			}
		},
		// onDataChannel
		func(dc *webrtc.DataChannel) {
			log.Printf("[PEER] 接收到数据通道: %s", dc.Label())
			// 作为被呼叫方，接收到对方的数据通道后，设置数据通道发送器
			log.Println("[PEER] 作为被呼叫方，设置接收到的数据通道为发送器")
			vcp.mediaCapture.SetDataChannelSender(vcp.webrtcManager)
		},
		// onDataChannelMessage
		func(msg webrtc.DataChannelMessage) {
			// 避免打印二进制数据导致控制台乱码
			if len(msg.Data) > 0 {
				// 检查是否是文本数据
				if isTextData(msg.Data) {
					log.Printf("[PEER] 数据通道文本消息: %s", string(msg.Data))
				} else {
					log.Printf("[PEER] 数据通道二进制消息: %d bytes, 前4字节: %x", len(msg.Data), msg.Data[:min(4, len(msg.Data))])

					// 尝试用媒体播放器处理数据通道消息
					if vcp.mediaPlayer != nil {
						if err := vcp.mediaPlayer.ProcessDataChannelMessage(msg); err != nil {
							log.Printf("[PEER] 处理数据通道消息失败: %v", err)
						}
					}
				}
			} else {
				log.Printf("[PEER] 数据通道空消息")
			}
		},
	)
}

// handleRTCMessage 处理RTC消息
func (vcp *VideoCallPeer) handleRTCMessage(msg RTCMessage) {
	log.Printf("[PEER] 处理RTC消息: %s", msg.Type)

	switch msg.Type {
	case "offer":
		vcp.handleOffer(msg.SDP)
	case "answer":
		vcp.handleAnswer(msg.SDP)
	case "candidate":
		vcp.handleICECandidate(msg.Candidate)
	default:
		log.Printf("[PEER] 未知RTC消息类型: %s", msg.Type)
	}
}

// handleCustomCommand 处理自定义命令
func (vcp *VideoCallPeer) handleCustomCommand(cmd VideoCallCommand, data string) {
	log.Printf("[PEER] 处理自定义命令: %s, 数据: %s", cmd, data)

	switch cmd {
	case CmdRing:
		// 收到呼叫
		if vcp.stateManager.GetState() == StateConnected {
			vcp.stateManager.TransitionTo(StateIncomingCall, "收到呼叫")
			if vcp.onCallReceived != nil {
				vcp.onCallReceived("来电")
			}
		}

	case CmdAcceptCall:
		// 对方接受呼叫
		if vcp.stateManager.GetState() == StateRinging {
			vcp.stateManager.TransitionTo(StateNegotiating, "对方接受呼叫")
			// 作为发起方，创建offer
			go vcp.createAndSendOffer()
		}

	case CmdDenyCall:
		// 对方拒绝呼叫或挂断
		currentState := vcp.stateManager.GetState()
		if currentState == StateRinging || currentState == StateInCall || currentState == StateNegotiating {
			vcp.stateManager.TransitionTo(StateConnected, "对方拒绝/挂断呼叫")
			if vcp.onCallEnded != nil {
				vcp.onCallEnded("对方拒绝/挂断")
			}
		}

	default:
		log.Printf("[PEER] 未处理的自定义命令: %s", cmd)
	}
}

// createAndSendOffer 创建并发送Offer
func (vcp *VideoCallPeer) createAndSendOffer() {
	log.Println("[PEER] 创建并发送Offer")

	// 创建WebRTC PeerConnection（如果还没有创建）
	if vcp.webrtcManager.GetConnectionState() == webrtc.PeerConnectionStateNew {
		if err := vcp.webrtcManager.CreatePeerConnection(); err != nil {
			log.Printf("[PEER] 创建WebRTC连接失败: %v", err)
			vcp.stateManager.TransitionTo(StateError, fmt.Sprintf("创建WebRTC连接失败: %v", err))
			return
		}

		// 设置数据通道发送器（用于视频）
		vcp.mediaCapture.SetDataChannelSender(vcp.webrtcManager)

		// 只添加音频轨道，视频通过数据通道发送
		audioTrack := vcp.mediaCapture.GetAudioTrack()
		if err := vcp.webrtcManager.AddLocalTracks(nil, audioTrack); err != nil {
			log.Printf("[PEER] 添加本地轨道失败: %v", err)
			vcp.stateManager.TransitionTo(StateError, fmt.Sprintf("添加本地轨道失败: %v", err))
			return
		}
	}

	offer, err := vcp.webrtcManager.CreateOffer()
	if err != nil {
		log.Printf("[PEER] 创建Offer失败: %v", err)
		vcp.stateManager.TransitionTo(StateError, fmt.Sprintf("创建Offer失败: %v", err))
		return
	}

	if err := vcp.signalingClient.SendSDP("offer", offer.SDP); err != nil {
		log.Printf("[PEER] 发送Offer失败: %v", err)
		vcp.stateManager.TransitionTo(StateError, fmt.Sprintf("发送Offer失败: %v", err))
		return
	}

	log.Println("[PEER] Offer已发送")
}

// handleOffer 处理接收到的Offer
func (vcp *VideoCallPeer) handleOffer(sdp string) {
	log.Println("[PEER] 处理接收到的Offer")

	// 创建WebRTC PeerConnection（如果还没有创建）
	if vcp.webrtcManager.GetConnectionState() == webrtc.PeerConnectionStateNew {
		if err := vcp.webrtcManager.CreatePeerConnection(); err != nil {
			log.Printf("[PEER] 创建WebRTC连接失败: %v", err)
			return
		}

		// 设置数据通道发送器（用于视频）
		vcp.mediaCapture.SetDataChannelSender(vcp.webrtcManager)

		// 只添加音频轨道，视频通过数据通道发送
		audioTrack := vcp.mediaCapture.GetAudioTrack()
		if err := vcp.webrtcManager.AddLocalTracks(nil, audioTrack); err != nil {
			log.Printf("[PEER] 添加本地轨道失败: %v", err)
			return
		}
	}

	offer := webrtc.SessionDescription{
		Type: webrtc.SDPTypeOffer,
		SDP:  sdp,
	}

	if err := vcp.webrtcManager.SetRemoteDescription(offer); err != nil {
		log.Printf("[PEER] 设置远程Offer失败: %v", err)
		return
	}

	// 创建并发送Answer
	answer, err := vcp.webrtcManager.CreateAnswer()
	if err != nil {
		log.Printf("[PEER] 创建Answer失败: %v", err)
		return
	}

	if err := vcp.signalingClient.SendSDP("answer", answer.SDP); err != nil {
		log.Printf("[PEER] 发送Answer失败: %v", err)
		return
	}

	log.Println("[PEER] Answer已发送")
}

// handleAnswer 处理接收到的Answer
func (vcp *VideoCallPeer) handleAnswer(sdp string) {
	log.Println("[PEER] 处理接收到的Answer")

	answer := webrtc.SessionDescription{
		Type: webrtc.SDPTypeAnswer,
		SDP:  sdp,
	}

	if err := vcp.webrtcManager.SetRemoteDescription(answer); err != nil {
		log.Printf("[PEER] 设置远程Answer失败: %v", err)
		return
	}

	log.Println("[PEER] Answer已处理")
}

// handleICECandidate 处理ICE候选
func (vcp *VideoCallPeer) handleICECandidate(candidate string) {
	log.Printf("[PEER] 处理ICE候选: %s", candidate)

	iceCandidate := webrtc.ICECandidateInit{
		Candidate: candidate,
	}

	if err := vcp.webrtcManager.AddICECandidate(iceCandidate); err != nil {
		log.Printf("[PEER] 添加ICE候选失败: %v", err)
		return
	}

	log.Println("[PEER] ICE候选已添加")
}

// isTextData 检查数据是否为文本数据
func isTextData(data []byte) bool {
	if len(data) == 0 {
		return true
	}

	// 检查前100个字节是否都是可打印字符
	checkLen := min(100, len(data))
	for i := 0; i < checkLen; i++ {
		b := data[i]
		// 允许可打印ASCII字符、换行符、制表符
		if !((b >= 32 && b <= 126) || b == '\n' || b == '\r' || b == '\t') {
			return false
		}
	}
	return true
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
