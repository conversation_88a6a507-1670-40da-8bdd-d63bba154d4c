package videocall

import (
	"fmt"
	"sync"
	"time"
)

// CallState 通话状态枚举
type CallState int

const (
	StateIdle CallState = iota        // 空闲状态
	StateConnecting                   // 连接信令服务器中
	StateConnected                    // 已连接到信令服务器
	StateOutgoingCall                 // 发起呼叫中
	StateIncomingCall                 // 接收到呼叫
	StateRinging                      // 响铃中
	StateNegotiating                  // WebRTC协商中
	StateInCall                       // 通话中
	StateHangingUp                    // 挂断中
	StateDisconnected                 // 已断开连接
	StateError                        // 错误状态
)

// String 返回状态的字符串表示
func (s CallState) String() string {
	switch s {
	case StateIdle:
		return "Idle"
	case StateConnecting:
		return "Connecting"
	case StateConnected:
		return "Connected"
	case StateOutgoingCall:
		return "OutgoingCall"
	case StateIncomingCall:
		return "IncomingCall"
	case StateRinging:
		return "Ringing"
	case StateNegotiating:
		return "Negotiating"
	case StateInCall:
		return "InCall"
	case StateHangingUp:
		return "HangingUp"
	case StateDisconnected:
		return "Disconnected"
	case StateError:
		return "Error"
	default:
		return "Unknown"
	}
}

// CallRole 通话角色
type CallRole int

const (
	RoleNone CallRole = iota
	RoleCaller                        // 主叫方
	RoleCallee                        // 被叫方
)

// String 返回角色的字符串表示
func (r CallRole) String() string {
	switch r {
	case RoleNone:
		return "None"
	case RoleCaller:
		return "Caller"
	case RoleCallee:
		return "Callee"
	default:
		return "Unknown"
	}
}

// CallSession 通话会话信息
type CallSession struct {
	SessionID    string    `json:"session_id"`
	PeerID       string    `json:"peer_id"`
	RoomID       string    `json:"room_id"`
	Role         CallRole  `json:"role"`
	StartTime    time.Time `json:"start_time"`
	ConnectTime  time.Time `json:"connect_time,omitempty"`
	EndTime      time.Time `json:"end_time,omitempty"`
	Duration     time.Duration `json:"duration,omitempty"`
	Quality      float64   `json:"quality,omitempty"`
	DisconnectReason string `json:"disconnect_reason,omitempty"`
}

// StateManager 状态管理器
type StateManager struct {
	currentState CallState
	previousState CallState
	role         CallRole
	session      *CallSession
	stateHistory []StateTransition
	mutex        sync.RWMutex
	
	// 状态变化回调
	onStateChange func(oldState, newState CallState)
	
	// 超时管理
	timeouts map[CallState]*time.Timer
	timeoutMutex sync.Mutex
}

// StateTransition 状态转换记录
type StateTransition struct {
	From      CallState `json:"from"`
	To        CallState `json:"to"`
	Timestamp time.Time `json:"timestamp"`
	Reason    string    `json:"reason,omitempty"`
}

// NewStateManager 创建新的状态管理器
func NewStateManager() *StateManager {
	return &StateManager{
		currentState: StateIdle,
		previousState: StateIdle,
		role:         RoleNone,
		stateHistory: make([]StateTransition, 0),
		timeouts:     make(map[CallState]*time.Timer),
	}
}

// GetState 获取当前状态
func (sm *StateManager) GetState() CallState {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.currentState
}

// GetRole 获取当前角色
func (sm *StateManager) GetRole() CallRole {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.role
}

// GetSession 获取当前会话
func (sm *StateManager) GetSession() *CallSession {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	if sm.session == nil {
		return nil
	}
	// 返回副本以避免并发修改
	sessionCopy := *sm.session
	return &sessionCopy
}

// SetStateChangeCallback 设置状态变化回调
func (sm *StateManager) SetStateChangeCallback(callback func(oldState, newState CallState)) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	sm.onStateChange = callback
}

// TransitionTo 转换到新状态
func (sm *StateManager) TransitionTo(newState CallState, reason string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	// 检查状态转换是否有效
	if !sm.isValidTransition(sm.currentState, newState) {
		return fmt.Errorf("无效的状态转换: %s -> %s", sm.currentState, newState)
	}
	
	oldState := sm.currentState
	sm.previousState = sm.currentState
	sm.currentState = newState
	
	// 记录状态转换
	transition := StateTransition{
		From:      oldState,
		To:        newState,
		Timestamp: time.Now(),
		Reason:    reason,
	}
	sm.stateHistory = append(sm.stateHistory, transition)
	
	// 更新会话信息
	sm.updateSession(newState)
	
	// 清除旧状态的超时
	sm.clearTimeout(oldState)
	
	// 设置新状态的超时
	sm.setStateTimeout(newState)
	
	// 调用状态变化回调
	if sm.onStateChange != nil {
		go sm.onStateChange(oldState, newState)
	}
	
	return nil
}

// StartCall 开始新的通话会话
func (sm *StateManager) StartCall(sessionID, peerID, roomID string, role CallRole) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	if sm.currentState != StateConnected {
		return fmt.Errorf("只能在Connected状态下开始通话")
	}
	
	sm.role = role
	sm.session = &CallSession{
		SessionID: sessionID,
		PeerID:    peerID,
		RoomID:    roomID,
		Role:      role,
		StartTime: time.Now(),
	}
	
	return nil
}

// EndCall 结束通话会话
func (sm *StateManager) EndCall(reason string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	if sm.session != nil {
		sm.session.EndTime = time.Now()
		sm.session.Duration = sm.session.EndTime.Sub(sm.session.StartTime)
		sm.session.DisconnectReason = reason
	}
	
	sm.role = RoleNone
}

// isValidTransition 检查状态转换是否有效
func (sm *StateManager) isValidTransition(from, to CallState) bool {
	validTransitions := map[CallState][]CallState{
		StateIdle: {StateConnecting, StateError},
		StateConnecting: {StateConnected, StateDisconnected, StateError},
		StateConnected: {StateOutgoingCall, StateIncomingCall, StateDisconnected, StateError},
		StateOutgoingCall: {StateRinging, StateDisconnected, StateError},
		StateIncomingCall: {StateRinging, StateNegotiating, StateDisconnected, StateError},
		StateRinging: {StateNegotiating, StateDisconnected, StateError},
		StateNegotiating: {StateInCall, StateDisconnected, StateError},
		StateInCall: {StateHangingUp, StateDisconnected, StateError},
		StateHangingUp: {StateConnected, StateDisconnected, StateError},
		StateDisconnected: {StateIdle, StateConnecting, StateError},
		StateError: {StateIdle, StateDisconnected},
	}
	
	allowedStates, exists := validTransitions[from]
	if !exists {
		return false
	}
	
	for _, allowedState := range allowedStates {
		if allowedState == to {
			return true
		}
	}
	
	return false
}

// updateSession 更新会话信息
func (sm *StateManager) updateSession(newState CallState) {
	if sm.session == nil {
		return
	}
	
	switch newState {
	case StateInCall:
		if sm.session.ConnectTime.IsZero() {
			sm.session.ConnectTime = time.Now()
		}
	}
}

// setStateTimeout 设置状态超时
func (sm *StateManager) setStateTimeout(state CallState) {
	sm.timeoutMutex.Lock()
	defer sm.timeoutMutex.Unlock()
	
	var timeout time.Duration
	var timeoutAction func()
	
	switch state {
	case StateOutgoingCall:
		timeout = 30 * time.Second
		timeoutAction = func() {
			sm.TransitionTo(StateDisconnected, "呼叫超时")
		}
	case StateIncomingCall:
		timeout = 30 * time.Second
		timeoutAction = func() {
			sm.TransitionTo(StateDisconnected, "来电超时")
		}
	case StateRinging:
		timeout = 30 * time.Second
		timeoutAction = func() {
			sm.TransitionTo(StateDisconnected, "响铃超时")
		}
	case StateNegotiating:
		timeout = 60 * time.Second
		timeoutAction = func() {
			sm.TransitionTo(StateError, "协商超时")
		}
	default:
		return // 其他状态不设置超时
	}
	
	if timeout > 0 && timeoutAction != nil {
		timer := time.AfterFunc(timeout, timeoutAction)
		sm.timeouts[state] = timer
	}
}

// clearTimeout 清除状态超时
func (sm *StateManager) clearTimeout(state CallState) {
	sm.timeoutMutex.Lock()
	defer sm.timeoutMutex.Unlock()
	
	if timer, exists := sm.timeouts[state]; exists {
		timer.Stop()
		delete(sm.timeouts, state)
	}
}

// GetStateHistory 获取状态历史
func (sm *StateManager) GetStateHistory() []StateTransition {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	// 返回副本
	history := make([]StateTransition, len(sm.stateHistory))
	copy(history, sm.stateHistory)
	return history
}

// Reset 重置状态管理器
func (sm *StateManager) Reset() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	// 清除所有超时
	sm.timeoutMutex.Lock()
	for state, timer := range sm.timeouts {
		timer.Stop()
		delete(sm.timeouts, state)
	}
	sm.timeoutMutex.Unlock()
	
	sm.currentState = StateIdle
	sm.previousState = StateIdle
	sm.role = RoleNone
	sm.session = nil
	sm.stateHistory = make([]StateTransition, 0)
}
