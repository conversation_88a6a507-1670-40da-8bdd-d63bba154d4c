package videocall

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"github.com/gorilla/websocket"
)

// VideoCallCommand ESP32 videocall_demo兼容的命令
type VideoCallCommand string

const (
	// ESP32 videocall_demo命令
	CmdRing       VideoCallCommand = "RING"
	CmdAcceptCall VideoCallCommand = "ACCEPT_CALL"
	CmdDenyCall   VideoCallCommand = "DENY_CALL"

	// 扩展命令
	CmdCallRequest  VideoCallCommand = "CALL_REQUEST"
	CmdCallResponse VideoCallCommand = "CALL_RESPONSE"
	CmdCallHangup   VideoCallCommand = "CALL_HANGUP"
	CmdCallBusy     VideoCallCommand = "CALL_BUSY"
	CmdCallTimeout  VideoCallCommand = "CALL_TIMEOUT"
	CmdHeartbeat    VideoCallCommand = "HEARTBEAT"
	CmdDeviceInfo   VideoCallCommand = "DEVICE_INFO"
)

// AppRTCResponse AppRTC房间信息响应
type AppRTCResponse struct {
	Result string `json:"result"`
	Params struct {
		ClientID     string   `json:"client_id"`
		IsInitiator  string   `json:"is_initiator"`
		WssURL       string   `json:"wss_url"`
		WssPostURL   string   `json:"wss_post_url"`
		RoomID       string   `json:"room_id"`
		RoomLink     string   `json:"room_link"`
		IceServerURL string   `json:"ice_server_url"`
		Messages     []string `json:"messages"`
	} `json:"params"`
}

// ICEServersResponse ICE服务器响应
type ICEServersResponse struct {
	IceServers []struct {
		URLs       []string `json:"urls"`
		Username   string   `json:"username"`
		Credential string   `json:"credential"`
	} `json:"iceServers"`
}

// WSMessage WebSocket消息格式
type WSMessage struct {
	Cmd      string `json:"cmd"`
	Msg      string `json:"msg,omitempty"`
	RoomID   string `json:"roomid,omitempty"`
	ClientID string `json:"clientid,omitempty"`
}

// RTCMessage WebRTC消息格式
type RTCMessage struct {
	Type      string `json:"type"`
	SDP       string `json:"sdp,omitempty"`
	Candidate string `json:"candidate,omitempty"`
	Data      string `json:"data,omitempty"`
}

// SignalingClient 信令客户端
type SignalingClient struct {
	serverURL string
	roomInfo  AppRTCResponse
	wsConn    *websocket.Conn
	ctx       context.Context
	cancel    context.CancelFunc
	mutex     sync.RWMutex

	// 回调函数
	onConnected    func()
	onDisconnected func(error)
	onMessage      func(RTCMessage)
	onCommand      func(VideoCallCommand, string)
	onError        func(error)

	// 状态
	connected bool
}

// NewSignalingClient 创建新的信令客户端
func NewSignalingClient(serverURL string) *SignalingClient {
	ctx, cancel := context.WithCancel(context.Background())

	return &SignalingClient{
		serverURL: serverURL,
		ctx:       ctx,
		cancel:    cancel,
	}
}

// SetCallbacks 设置回调函数
func (sc *SignalingClient) SetCallbacks(
	onConnected func(),
	onDisconnected func(error),
	onMessage func(RTCMessage),
	onCommand func(VideoCallCommand, string),
	onError func(error),
) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	sc.onConnected = onConnected
	sc.onDisconnected = onDisconnected
	sc.onMessage = onMessage
	sc.onCommand = onCommand
	sc.onError = onError
}

// Connect 连接到房间
func (sc *SignalingClient) Connect(roomID string) error {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	if sc.connected {
		return fmt.Errorf("已经连接到信令服务器")
	}

	log.Printf("[SIGNALING] 连接到房间: %s", roomID)

	// 1. 获取房间信息
	if err := sc.getRoomInfo(roomID); err != nil {
		return fmt.Errorf("获取房间信息失败: %v", err)
	}

	// 2. 建立WebSocket连接
	if err := sc.connectWebSocket(); err != nil {
		return fmt.Errorf("WebSocket连接失败: %v", err)
	}

	// 3. 启动消息监听
	go sc.listenMessages()

	sc.connected = true

	if sc.onConnected != nil {
		go sc.onConnected()
	}

	log.Printf("[SIGNALING] 成功连接到房间: %s, ClientID: %s, IsInitiator: %s",
		roomID, sc.roomInfo.Params.ClientID, sc.roomInfo.Params.IsInitiator)

	return nil
}

// Disconnect 断开连接
func (sc *SignalingClient) Disconnect() {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	if !sc.connected {
		return
	}

	log.Println("[SIGNALING] 断开信令连接")

	sc.connected = false
	sc.cancel()

	if sc.wsConn != nil {
		sc.wsConn.Close()
		sc.wsConn = nil
	}

	if sc.onDisconnected != nil {
		go sc.onDisconnected(nil)
	}
}

// SendCommand 发送自定义命令
func (sc *SignalingClient) SendCommand(cmd VideoCallCommand, data string) error {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()

	if !sc.connected {
		return fmt.Errorf("未连接到信令服务器")
	}

	rtcMsg := RTCMessage{
		Type: "customized",
		Data: string(cmd),
	}

	if data != "" {
		rtcMsg.Data = fmt.Sprintf("%s:%s", cmd, data)
	}

	msgData, _ := json.Marshal(rtcMsg)

	wsMsg := WSMessage{
		Cmd: "send",
		Msg: string(msgData),
	}

	return sc.sendWSMessage(wsMsg)
}

// SendSDP 发送SDP消息
func (sc *SignalingClient) SendSDP(sdpType, sdp string) error {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()

	if !sc.connected {
		return fmt.Errorf("未连接到信令服务器")
	}

	rtcMsg := RTCMessage{
		Type: sdpType,
		SDP:  sdp,
	}
	msgData, _ := json.Marshal(rtcMsg)

	// 通过HTTP POST发送SDP消息
	messageURL := fmt.Sprintf("%s/message/%s/%s",
		sc.getBaseURL(), sc.roomInfo.Params.RoomID, sc.roomInfo.Params.ClientID)

	resp, err := http.Post(messageURL, "application/json", strings.NewReader(string(msgData)))
	if err != nil {
		return fmt.Errorf("发送SDP失败: %v", err)
	}
	defer resp.Body.Close()

	log.Printf("[SIGNALING] 发送SDP %s", sdpType)
	return nil
}

// SendICECandidate 发送ICE候选
func (sc *SignalingClient) SendICECandidate(candidate string) error {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()

	if !sc.connected {
		return fmt.Errorf("未连接到信令服务器")
	}

	rtcMsg := RTCMessage{
		Type:      "candidate",
		Candidate: candidate,
	}
	msgData, _ := json.Marshal(rtcMsg)

	// 通过HTTP POST发送ICE候选
	messageURL := fmt.Sprintf("%s/message/%s/%s",
		sc.getBaseURL(), sc.roomInfo.Params.RoomID, sc.roomInfo.Params.ClientID)

	resp, err := http.Post(messageURL, "application/json", strings.NewReader(string(msgData)))
	if err != nil {
		return fmt.Errorf("发送ICE候选失败: %v", err)
	}
	defer resp.Body.Close()

	log.Printf("[SIGNALING] 发送ICE候选")
	return nil
}

// GetRoomInfo 获取房间信息
func (sc *SignalingClient) GetRoomInfo() AppRTCResponse {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()
	return sc.roomInfo
}

// IsConnected 检查是否已连接
func (sc *SignalingClient) IsConnected() bool {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()
	return sc.connected
}

// getRoomInfo 获取房间信息
func (sc *SignalingClient) getRoomInfo(roomID string) error {
	joinURL := fmt.Sprintf("%s/join/%s", sc.serverURL, roomID)

	resp, err := http.Post(joinURL, "application/json", nil)
	if err != nil {
		return fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	if err := json.Unmarshal(body, &sc.roomInfo); err != nil {
		return fmt.Errorf("解析房间信息失败: %v", err)
	}

	return nil
}

// connectWebSocket 建立WebSocket连接
func (sc *SignalingClient) connectWebSocket() error {
	u, err := url.Parse(sc.roomInfo.Params.WssURL)
	if err != nil {
		return fmt.Errorf("无效的WebSocket URL: %v", err)
	}

	// 设置Origin头
	header := http.Header{}
	baseURL := fmt.Sprintf("%s://%s", u.Scheme[:len(u.Scheme)-1], u.Host)
	header.Set("Origin", baseURL)

	sc.wsConn, _, err = websocket.DefaultDialer.Dial(sc.roomInfo.Params.WssURL, header)
	if err != nil {
		return fmt.Errorf("WebSocket连接失败: %v", err)
	}

	// 注册到房间
	registerMsg := WSMessage{
		Cmd:      "register",
		RoomID:   sc.roomInfo.Params.RoomID,
		ClientID: sc.roomInfo.Params.ClientID,
	}

	return sc.sendWSMessage(registerMsg)
}

// sendWSMessage 发送WebSocket消息
func (sc *SignalingClient) sendWSMessage(msg WSMessage) error {
	if sc.wsConn == nil {
		return fmt.Errorf("WebSocket未连接")
	}

	data, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	log.Printf("[SIGNALING] 发送WS消息: %s", string(data))
	return sc.wsConn.WriteMessage(websocket.TextMessage, data)
}

// listenMessages 监听WebSocket消息
func (sc *SignalingClient) listenMessages() {
	defer func() {
		if sc.wsConn != nil {
			sc.wsConn.Close()
			sc.wsConn = nil
		}
	}()

	for {
		select {
		case <-sc.ctx.Done():
			return
		default:
			_, message, err := sc.wsConn.ReadMessage()
			if err != nil {
				log.Printf("[SIGNALING] WebSocket读取错误: %v", err)
				if sc.onError != nil {
					go sc.onError(err)
				}
				return
			}

			sc.handleMessage(message)
		}
	}
}

// handleMessage 处理接收到的消息
func (sc *SignalingClient) handleMessage(data []byte) {
	log.Printf("[SIGNALING] 收到消息: %s", string(data))

	var wsMsg WSMessage
	if err := json.Unmarshal(data, &wsMsg); err != nil {
		log.Printf("[SIGNALING] 解析WebSocket消息失败: %v", err)
		return
	}

	// 处理包含RTC消息的WebSocket消息
	if wsMsg.Msg != "" {
		var rtcMsg RTCMessage
		if err := json.Unmarshal([]byte(wsMsg.Msg), &rtcMsg); err != nil {
			log.Printf("[SIGNALING] 解析RTC消息失败: %v", err)
			return
		}

		// 处理不同类型的RTC消息
		switch rtcMsg.Type {
		case "offer", "answer":
			if sc.onMessage != nil {
				go sc.onMessage(rtcMsg)
			}
		case "candidate":
			if sc.onMessage != nil {
				go sc.onMessage(rtcMsg)
			}
		case "customized":
			sc.handleCustomCommand(rtcMsg.Data)
		default:
			log.Printf("[SIGNALING] 未知RTC消息类型: %s", rtcMsg.Type)
		}
	}

	// 处理其他WebSocket命令
	switch wsMsg.Cmd {
	case "new_client":
		log.Println("[SIGNALING] 新客户端加入房间")
	default:
		if wsMsg.Cmd != "" {
			log.Printf("[SIGNALING] 未知WebSocket命令: %s", wsMsg.Cmd)
		}
	}
}

// handleCustomCommand 处理自定义命令
func (sc *SignalingClient) handleCustomCommand(data string) {
	// 解析命令和数据
	parts := strings.SplitN(data, ":", 2)
	cmd := VideoCallCommand(parts[0])
	var cmdData string
	if len(parts) > 1 {
		cmdData = parts[1]
	}

	log.Printf("[SIGNALING] 收到自定义命令: %s, 数据: %s", cmd, cmdData)

	if sc.onCommand != nil {
		go sc.onCommand(cmd, cmdData)
	}
}

// getBaseURL 获取基础URL
func (sc *SignalingClient) getBaseURL() string {
	if idx := strings.Index(sc.serverURL, "/join"); idx != -1 {
		return sc.serverURL[:idx]
	}
	return sc.serverURL
}
