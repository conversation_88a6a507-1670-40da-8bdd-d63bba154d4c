# Go VideoCall Peer Makefile

# 项目信息
PROJECT_NAME := go-videocall-peer
BINARY_NAME := videocall-peer
VERSION := 1.0.0
BUILD_TIME := $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Go相关变量
GO := go
GOFLAGS := -v
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# 目录
BUILD_DIR := build
CONFIG_DIR := config
DOCS_DIR := docs
DEBUG_DIR := debug

# 默认目标
.PHONY: all
all: clean deps build

# 安装依赖
.PHONY: deps
deps:
	@echo "正在安装依赖..."
	$(GO) mod download
	$(GO) mod tidy

# 构建项目
.PHONY: build
build:
	@echo "正在构建 $(PROJECT_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GO) build $(GOFLAGS) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) .
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 构建所有平台
.PHONY: build-all
build-all: clean deps
	@echo "正在构建所有平台..."
	@mkdir -p $(BUILD_DIR)
	
	# Linux AMD64
	GOOS=linux GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 .
	
	# Linux ARM64
	GOOS=linux GOARCH=arm64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-arm64 .
	
	# macOS AMD64
	GOOS=darwin GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 .
	
	# macOS ARM64 (Apple Silicon)
	GOOS=darwin GOARCH=arm64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 .
	
	# Windows AMD64
	GOOS=windows GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe .
	
	@echo "所有平台构建完成"

# 运行项目
.PHONY: run
run: build
	@echo "正在运行 $(PROJECT_NAME)..."
	./$(BUILD_DIR)/$(BINARY_NAME)

# 调试模式运行
.PHONY: run-debug
run-debug: build
	@echo "正在以调试模式运行 $(PROJECT_NAME)..."
	@mkdir -p $(DEBUG_DIR)
	./$(BUILD_DIR)/$(BINARY_NAME) -debug

# 使用自定义配置运行
.PHONY: run-config
run-config: build
	@echo "正在使用自定义配置运行 $(PROJECT_NAME)..."
	./$(BUILD_DIR)/$(BINARY_NAME) -config $(CONFIG_DIR)/custom.yaml

# 运行测试
.PHONY: test
test:
	@echo "正在运行测试..."
	$(GO) test $(GOFLAGS) ./...

# 运行测试并生成覆盖率报告
.PHONY: test-coverage
test-coverage:
	@echo "正在运行测试并生成覆盖率报告..."
	$(GO) test -coverprofile=coverage.out ./...
	$(GO) tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

# 代码格式化
.PHONY: fmt
fmt:
	@echo "正在格式化代码..."
	$(GO) fmt ./...

# 代码检查
.PHONY: vet
vet:
	@echo "正在进行代码检查..."
	$(GO) vet ./...

# 运行golangci-lint
.PHONY: lint
lint:
	@echo "正在运行 golangci-lint..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint 未安装，请运行: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

# 清理构建文件
.PHONY: clean
clean:
	@echo "正在清理构建文件..."
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html
	@rm -rf $(DEBUG_DIR)

# 创建发布包
.PHONY: release
release: clean build-all
	@echo "正在创建发布包..."
	@mkdir -p $(BUILD_DIR)/release
	
	# 复制配置文件和文档
	@cp -r $(CONFIG_DIR) $(BUILD_DIR)/release/
	@cp -r $(DOCS_DIR) $(BUILD_DIR)/release/
	@cp README.md $(BUILD_DIR)/release/
	
	# 创建各平台的压缩包
	@cd $(BUILD_DIR) && \
	for binary in $(BINARY_NAME)-*; do \
		if [ -f "$$binary" ]; then \
			platform=$$(echo $$binary | sed 's/$(BINARY_NAME)-//'); \
			mkdir -p "release/$$platform"; \
			cp "$$binary" "release/$$platform/$(BINARY_NAME)"; \
			cp -r release/$(CONFIG_DIR) "release/$$platform/"; \
			cp -r release/$(DOCS_DIR) "release/$$platform/"; \
			cp release/README.md "release/$$platform/"; \
			tar -czf "$(PROJECT_NAME)-$$platform.tar.gz" -C release "$$platform"; \
			echo "已创建: $(BUILD_DIR)/$(PROJECT_NAME)-$$platform.tar.gz"; \
		fi \
	done
	
	@echo "发布包创建完成"

# 安装到系统
.PHONY: install
install: build
	@echo "正在安装到系统..."
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	@echo "已安装到 /usr/local/bin/$(BINARY_NAME)"

# 从系统卸载
.PHONY: uninstall
uninstall:
	@echo "正在从系统卸载..."
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)
	@echo "已从系统卸载"

# 显示帮助信息
.PHONY: help
help:
	@echo "$(PROJECT_NAME) Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all          - 清理、安装依赖并构建项目"
	@echo "  deps         - 安装Go依赖"
	@echo "  build        - 构建项目"
	@echo "  build-all    - 构建所有平台的二进制文件"
	@echo "  run          - 构建并运行项目"
	@echo "  run-debug    - 以调试模式运行项目"
	@echo "  run-config   - 使用自定义配置运行项目"
	@echo "  test         - 运行测试"
	@echo "  test-coverage- 运行测试并生成覆盖率报告"
	@echo "  fmt          - 格式化代码"
	@echo "  vet          - 代码检查"
	@echo "  lint         - 运行golangci-lint"
	@echo "  clean        - 清理构建文件"
	@echo "  release      - 创建发布包"
	@echo "  install      - 安装到系统"
	@echo "  uninstall    - 从系统卸载"
	@echo "  help         - 显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make build           # 构建项目"
	@echo "  make run             # 运行项目"
	@echo "  make run-debug       # 调试模式运行"
	@echo "  make test            # 运行测试"
	@echo "  make release         # 创建发布包"

# 检查Go环境
.PHONY: check-env
check-env:
	@echo "检查Go环境..."
	@echo "Go版本: $$($(GO) version)"
	@echo "GOPATH: $$($(GO) env GOPATH)"
	@echo "GOROOT: $$($(GO) env GOROOT)"
	@echo "项目路径: $$(pwd)"

# 显示项目信息
.PHONY: info
info:
	@echo "项目信息:"
	@echo "  名称: $(PROJECT_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Git提交: $(GIT_COMMIT)"
	@echo "  二进制文件: $(BINARY_NAME)"
