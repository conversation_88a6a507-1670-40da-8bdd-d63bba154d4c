#!/bin/bash

# 测试WebRTC连接修复

echo "=== WebRTC连接修复测试 ==="
echo

# 清理之前的调试文件
rm -f debug/received_video.h264 debug/received_audio.pcma debug/monitor.log

echo "✅ 已清理调试文件"
echo

echo "启动程序进行测试..."
echo "请按以下步骤操作："
echo
echo "1. 程序启动后，输入: join a0005"
echo "2. 启用监控: monitor"
echo "3. 在ESP32上发起呼叫 (输入 b)"
echo "4. 在程序中接受呼叫: accept"
echo "5. 观察以下关键日志:"
echo "   - [WEBRTC] 创建PeerConnection成功"
echo "   - [WEBRTC] 添加音频轨道/视频轨道"
echo "   - [WEBRTC] 连接状态变化: new -> connecting -> connected"
echo "   - [WEBRTC] 生成ICE候选"
echo "   - [PLAYER] 设置远程视频轨道"
echo "   - [PLAYER] 收到视频包"
echo "   - [H264] 处理SPS/PPS/IDR帧"
echo
echo "6. 使用调试命令检查状态:"
echo "   - debug  (检查WebRTC连接状态)"
echo "   - files  (检查文件大小)"
echo
echo "预期结果:"
echo "✅ WebRTC状态应该变为 'connected'"
echo "✅ 应该接收到视频和音频包"
echo "✅ 文件大小应该持续增长"
echo "✅ ffplay应该能播放视频"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 检查结果
echo "检查测试结果:"

if [ -f "debug/received_video.h264" ]; then
    size=$(stat -f%z "debug/received_video.h264" 2>/dev/null || stat -c%s "debug/received_video.h264" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 视频文件: ${size} bytes (成功接收数据)"
        
        # 尝试用ffprobe检查文件
        if command -v ffprobe >/dev/null 2>&1; then
            echo "📹 视频文件信息:"
            ffprobe -v quiet -show_format -show_streams debug/received_video.h264 2>/dev/null || echo "   (无法解析，可能是部分数据)"
        fi
        
        echo "🎬 播放命令:"
        echo "   ffplay -f h264 -framerate 15 debug/received_video.h264"
    else
        echo "❌ 视频文件: 0 bytes (未接收到数据)"
    fi
else
    echo "❌ 视频文件: 不存在"
fi

if [ -f "debug/received_audio.pcma" ]; then
    size=$(stat -f%z "debug/received_audio.pcma" 2>/dev/null || stat -c%s "debug/received_audio.pcma" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 音频文件: ${size} bytes (成功接收数据)"
        echo "🎵 播放命令:"
        echo "   ffplay -f alaw -ar 8000 -ac 1 debug/received_audio.pcma"
    else
        echo "❌ 音频文件: 0 bytes (未接收到数据)"
    fi
else
    echo "❌ 音频文件: 不存在"
fi

if [ -f "debug/monitor.log" ]; then
    echo "📊 监控日志已生成: debug/monitor.log"
    echo "   最后几行:"
    tail -5 debug/monitor.log 2>/dev/null || echo "   (无法读取)"
fi

echo
echo "如果仍然有问题，请检查:"
echo "1. ESP32是否正确发送媒体流"
echo "2. 网络连接是否正常"
echo "3. STUN/TURN服务器是否可达"
echo "4. 防火墙设置是否正确"
