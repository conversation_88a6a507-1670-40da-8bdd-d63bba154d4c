# JPEG数据通道支持

## 问题分析

从日志分析发现，ESP32设备的实际行为与我们的预期不同：

### ESP32实际行为：
- ✅ **音频轨道**：通过WebRTC音频轨道发送PCMA音频（8kHz, 160字节/包）
- ✅ **视频数据**：通过WebRTC数据通道发送JPEG图像（~20KB/帧, 10FPS）
- ❌ **视频轨道**：没有使用WebRTC视频轨道发送H.264流

### 我们程序的原始配置：
- ✅ **音频处理**：配置正确，能接收PCMA音频
- ❌ **视频处理**：只配置了H.264视频轨道处理，无法处理JPEG数据通道

## 解决方案

添加了完整的JPEG数据通道处理支持：

### 1. JPEG处理器 (`jpeg_processor.go`)
- **JPEG检测**：识别JPEG文件头 (`FF D8`)
- **MJPEG保存**：将连续JPEG帧保存为MJPEG格式
- **实时播放**：使用ffplay实时显示JPEG流
- **统计信息**：帧数、字节数、FPS计算

### 2. 媒体播放器集成
- **数据通道处理**：`ProcessDataChannelMessage()` 方法
- **自动识别**：区分JPEG和其他二进制数据
- **统计更新**：将JPEG帧计入视频统计

### 3. 对等端集成
- **回调处理**：数据通道消息自动转发给媒体播放器
- **错误处理**：优雅处理未知数据格式
- **日志优化**：避免二进制数据乱码输出

## 技术细节

### JPEG数据格式
```
前4字节: FF D8 FF E0  (JPEG文件头)
大小: ~20KB/帧
频率: 10FPS
来源: ESP32摄像头
```

### MJPEG文件格式
```
JPEG帧1 + JPEG帧2 + JPEG帧3 + ...
每帧独立，可单独解码
兼容ffplay、VLC等播放器
```

### ffplay实时播放
```bash
# 使用命名管道实现实时播放
mkfifo /tmp/videocall_mjpeg_pipe
ffplay -f mjpeg -framerate 10 -i /tmp/videocall_mjpeg_pipe
```

## 使用方法

### 1. 启动程序
```bash
./videocall-peer
```

### 2. 建立连接
```
join a0005
monitor
accept  # 当ESP32发起呼叫时
```

### 3. 观察输出
```
[JPEG] JPEG图像将保存到: debug/received_video.mjpeg
[JPEG] ffplay已启动播放MJPEG流
[JPEG] 接收JPEG帧 #10, 大小: 20500 bytes, FPS: 10.0
[PLAYER] 数据通道二进制消息被成功处理为JPEG
```

### 4. 检查结果
```
debug    # 查看视频统计
files    # 查看文件大小
```

### 5. 播放保存的视频
```bash
# 播放MJPEG文件
ffplay -f mjpeg debug/received_video.mjpeg
vlc debug/received_video.mjpeg

# 播放音频文件
ffplay -f alaw -ar 8000 -ac 1 debug/received_audio.pcma
```

## 配置文件更新

在 `config/default.yaml` 中：
```yaml
playback:
  video:
    enable: true
    save_to_file: true
    save_path: "debug/received_video.h264"  # 自动改为 .mjpeg
    use_ffplay: true
    ffplay_path: "ffplay"
    ffplay_args: ["-f", "mjpeg", "-framerate", "10", "-"]
```

## 文件输出

### 生成的文件
- `debug/received_video.mjpeg` - MJPEG视频文件
- `debug/received_audio.pcma` - PCMA音频文件
- `debug/monitor.log` - 调试监控日志
- `debug/first_frame.jpg` - 第一帧JPEG图像（测试脚本生成）

### 文件大小估算
- **MJPEG文件**：20KB/帧 × 10FPS × 时长(秒) = 200KB/秒
- **音频文件**：160字节/包 × 50包/秒 = 8KB/秒

## 调试功能

### 统计信息
```
debug 命令输出：
视频统计: 100帧, 2048000字节, 10.0 FPS
音频统计: 500帧, 80000字节
```

### 文件监控
```
files 命令输出：
✅ 视频文件: 2048000 bytes
✅ 音频文件: 80000 bytes
✅ 监控日志: 1024 bytes
```

### 实时监控
```
monitor 命令启动后每5秒显示：
[MONITOR] 统计信息 - 视频: 50帧/1024000字节/10.0FPS, 音频: 250帧/40000字节
```

## 兼容性

### 支持的播放器
- **ffplay**：完美支持MJPEG格式
- **VLC**：支持MJPEG文件播放
- **QuickTime**：支持MJPEG格式
- **Chrome/Firefox**：可直接播放MJPEG文件

### 系统要求
- **FFmpeg**：用于实时播放和格式转换
- **命名管道**：Linux/macOS支持，Windows需要调整

## 性能优化

### 内存使用
- 流式处理，不缓存完整视频
- 每帧独立处理，内存占用稳定
- 自动垃圾回收，长时间运行稳定

### CPU使用
- 无需解码，直接保存JPEG数据
- 最小化数据拷贝
- 异步处理，不阻塞主线程

## 故障排除

### 常见问题

1. **ffplay无法启动**
   - 检查ffplay是否安装
   - 验证ffplay_path配置
   - 查看错误日志

2. **MJPEG文件无法播放**
   - 确认文件大小 > 0
   - 检查JPEG文件头
   - 尝试不同播放器

3. **实时播放卡顿**
   - 检查系统性能
   - 调整ffplay参数
   - 监控网络延迟

### 调试命令
```bash
# 检查JPEG文件头
hexdump -C debug/received_video.mjpeg | head -5

# 检查文件大小变化
watch -n 1 'ls -la debug/'

# 测试ffplay
ffplay -f mjpeg debug/received_video.mjpeg
```

## 总结

通过添加JPEG数据通道支持，程序现在能够：
- ✅ 正确接收ESP32发送的JPEG视频流
- ✅ 保存为标准MJPEG格式
- ✅ 实时播放视频内容
- ✅ 提供详细的调试信息
- ✅ 兼容多种播放器和工具

这解决了视频文件一直为空的问题，现在可以正常接收和播放ESP32摄像头的视频流了！
