package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go-videocall-peer/config"
	"go-videocall-peer/internal/cli"
	"go-videocall-peer/internal/videocall"
)

var (
	configPath = flag.String("config", "config/default.yaml", "配置文件路径")
	debug      = flag.Bool("debug", false, "启用调试模式")
	version    = flag.Bool("version", false, "显示版本信息")
)

const (
	AppName    = "Go VideoCall Peer"
	AppVersion = "1.0.0"
	AppDesc    = "与ESP32 videocall_demo设备进行双向视频通话"
)

func main() {
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Printf("%s v%s\n", AppName, AppVersion)
		fmt.Printf("%s\n", AppDesc)
		return
	}

	// 显示启动信息
	fmt.Printf("=== %s v%s ===\n", AppName, AppVersion)
	fmt.Printf("%s\n\n", AppDesc)

	// 加载配置
	cfg, err := config.Load(*configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 设置调试模式
	if *debug {
		cfg.App.Debug = true
		cfg.Logging.Level = "debug"
	}

	// 创建调试目录
	if cfg.App.Debug {
		if err := cfg.CreateDebugDirs(); err != nil {
			log.Printf("创建调试目录失败: %v", err)
		}
	}

	// 设置日志级别
	setupLogging(cfg)

	log.Printf("启动 %s v%s", AppName, AppVersion)
	log.Printf("配置文件: %s", *configPath)
	log.Printf("调试模式: %v", cfg.App.Debug)

	// 创建视频通话对等端
	peer, err := videocall.NewVideoCallPeer(cfg)
	if err != nil {
		log.Fatalf("创建视频通话对等端失败: %v", err)
	}
	defer peer.Close()

	// 初始化对等端
	if err := peer.Initialize(); err != nil {
		log.Fatalf("初始化视频通话对等端失败: %v", err)
	}

	log.Println("视频通话对等端初始化完成")

	// 创建命令行界面
	cliInterface := cli.NewCLI(peer)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动命令行界面
	go func() {
		cliInterface.Start()
		// CLI退出时发送信号
		sigChan <- syscall.SIGTERM
	}()

	// 等待退出信号
	sig := <-sigChan
	log.Printf("收到信号: %v", sig)

	// 优雅关闭
	log.Println("正在关闭应用程序...")

	// 停止CLI界面
	cliInterface.Stop()

	// 关闭视频通话对等端（包括AstiAV摄像头）
	log.Println("正在关闭视频通话对等端...")
	peer.Close()

	// 给一点时间让资源完全释放
	log.Println("等待资源释放...")
	time.Sleep(2 * time.Second)

	log.Println("应用程序已安全关闭")
}

// setupLogging 设置日志
func setupLogging(cfg *config.Config) {
	// 设置日志格式
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// 根据配置设置日志输出
	switch cfg.Logging.Output {
	case "file":
		if cfg.Logging.FilePath != "" {
			file, err := os.OpenFile(cfg.Logging.FilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err != nil {
				log.Printf("打开日志文件失败: %v", err)
			} else {
				log.SetOutput(file)
			}
		}
	case "console":
		fallthrough
	default:
		log.SetOutput(os.Stdout)
	}

	// 根据日志级别设置输出
	switch cfg.Logging.Level {
	case "debug":
		log.SetFlags(log.LstdFlags | log.Lshortfile | log.Lmicroseconds)
	case "info":
		log.SetFlags(log.LstdFlags)
	case "warn", "error":
		log.SetFlags(log.LstdFlags | log.Lshortfile)
	}
}

// 使用说明
func init() {
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "%s v%s\n", AppName, AppVersion)
		fmt.Fprintf(os.Stderr, "%s\n\n", AppDesc)
		fmt.Fprintf(os.Stderr, "用法: %s [选项]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\n示例:\n")
		fmt.Fprintf(os.Stderr, "  %s                           # 使用默认配置启动\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -debug                    # 启用调试模式\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -config custom.yaml       # 使用自定义配置文件\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "\n基本使用流程:\n")
		fmt.Fprintf(os.Stderr, "  1. 启动程序\n")
		fmt.Fprintf(os.Stderr, "  2. 输入 'join <房间ID>' 加入房间\n")
		fmt.Fprintf(os.Stderr, "  3. 输入 'call' 发起呼叫或等待来电\n")
		fmt.Fprintf(os.Stderr, "  4. 输入 'accept' 接听或 'reject' 拒绝\n")
		fmt.Fprintf(os.Stderr, "  5. 输入 'hangup' 挂断通话\n")
		fmt.Fprintf(os.Stderr, "  6. 输入 'quit' 退出程序\n")
		fmt.Fprintf(os.Stderr, "\n与ESP32设备通话:\n")
		fmt.Fprintf(os.Stderr, "  - 确保ESP32设备运行videocall_demo固件\n")
		fmt.Fprintf(os.Stderr, "  - 使用相同的房间ID加入房间\n")
		fmt.Fprintf(os.Stderr, "  - 任一设备都可以发起呼叫\n")
		fmt.Fprintf(os.Stderr, "\n更多信息请查看 README.md\n")
	}
}
