#!/bin/bash

# 最终修复测试脚本
# 验证1024x600分辨率JPEG视频传输

echo "=== 最终修复测试 ==="

# 确保debug目录存在
mkdir -p debug

echo "1. 当前修复状态："
echo "   ✅ 分辨率: 1024x600 (匹配ESP32P4 LCD)"
echo "   ✅ JPEG解码器缓冲区: 增加32KB额外空间"
echo "   ✅ 音视频同步: 禁用 (AV_RENDER_SYNC_NONE)"
echo "   ✅ 第一帧暂停: 禁用 (pause_on_first_frame = false)"
echo "   ✅ LCD错误处理: 忽略不支持的功能"
echo ""

echo "2. ESP32日志分析："
echo "   ✅ Video resolution 1024x600 - 分辨率识别正确"
echo "   ✅ LCD_RENDER: Render started 1024x600 - LCD渲染启动"
echo "   ✅ LCD_RENDER: fps: 6/7 - 有帧率输出"
echo "   ✅ 没有解码失败错误 - JPEG解码成功"
echo ""

echo "3. 测试优化的JPEG生成..."

# 生成测试JPEG图像，使用与ESP32相同的参数
ffmpeg -f lavfi -i "testsrc2=size=1024x600:rate=1:duration=1" \
       -vf "drawtext=text='Test Frame - 1024x600':x=50:y=50:fontsize=48:fontcolor=white:box=1:boxcolor=black@0.5" \
       -vframes 1 -f mjpeg -q:v 2 -pix_fmt yuvj420p -huffman optimal \
       -loglevel quiet "debug/test_optimized_1024x600.jpg"

if [ -f "debug/test_optimized_1024x600.jpg" ]; then
    size=$(wc -c < "debug/test_optimized_1024x600.jpg")
    echo "   ✅ 优化JPEG生成成功: debug/test_optimized_1024x600.jpg (${size} bytes)"
    
    # 验证图像分辨率
    if command -v identify >/dev/null 2>&1; then
        resolution=$(identify "debug/test_optimized_1024x600.jpg" | grep -o '[0-9]*x[0-9]*')
        echo "   ✅ 分辨率验证: $resolution"
    fi
else
    echo "   ✗ 优化JPEG生成失败"
fi

echo ""
echo "4. 预期效果："
echo "   - ESP32P4应该能成功解码1024x600 JPEG图像"
echo "   - LCD屏幕应该全屏显示彩色视频内容"
echo "   - 不再出现黑屏问题"
echo ""

echo "5. 如果仍然看不到图像，可能的原因："
echo "   - LCD背光未开启"
echo "   - 颜色格式转换问题"
echo "   - 图像内容太暗或对比度不够"
echo ""

echo "6. 建议的调试步骤："
echo "   a) 检查ESP32日志中的fps输出是否持续"
echo "   b) 尝试发送更高对比度的测试图案"
echo "   c) 检查LCD背光和显示设置"
echo ""

echo "=== 测试完成 ==="
echo "现在可以运行: ./videocall-peer --room aa0003"
