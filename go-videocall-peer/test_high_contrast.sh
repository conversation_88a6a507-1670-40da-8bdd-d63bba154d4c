#!/bin/bash

# 测试高对比度图像生成
# 用于解决ESP32P4 LCD黑屏问题

echo "=== 高对比度测试图像生成 ==="

# 确保debug目录存在
mkdir -p debug

echo "1. 生成极高对比度测试图像..."

# 生成纯白色背景
echo "   生成纯白色背景..."
ffmpeg -f lavfi -i "color=white:size=1024x600:duration=1" \
       -vf "drawtext=text='WHITE BACKGROUND - 1024x600':x=200:y=250:fontsize=72:fontcolor=black:box=1:boxcolor=red@0.8" \
       -vframes 1 -f mjpeg -q:v 2 -pix_fmt yuvj420p -huffman optimal \
       -loglevel quiet "debug/test_white_1024x600.jpg"

# 生成纯红色背景
echo "   生成纯红色背景..."
ffmpeg -f lavfi -i "color=red:size=1024x600:duration=1" \
       -vf "drawtext=text='RED BACKGROUND - 1024x600':x=200:y=250:fontsize=72:fontcolor=white:box=1:boxcolor=black@0.8" \
       -vframes 1 -f mjpeg -q:v 2 -pix_fmt yuvj420p -huffman optimal \
       -loglevel quiet "debug/test_red_1024x600.jpg"

# 生成纯蓝色背景
echo "   生成纯蓝色背景..."
ffmpeg -f lavfi -i "color=blue:size=1024x600:duration=1" \
       -vf "drawtext=text='BLUE BACKGROUND - 1024x600':x=200:y=250:fontsize=72:fontcolor=white:box=1:boxcolor=black@0.8" \
       -vframes 1 -f mjpeg -q:v 2 -pix_fmt yuvj420p -huffman optimal \
       -loglevel quiet "debug/test_blue_1024x600.jpg"

# 生成黑白条纹
echo "   生成黑白条纹..."
ffmpeg -f lavfi -i "testsrc=size=1024x600:rate=1:duration=1" \
       -vf "drawtext=text='BLACK WHITE STRIPES - 1024x600':x=150:y=250:fontsize=72:fontcolor=red:box=1:boxcolor=white@0.8" \
       -vframes 1 -f mjpeg -q:v 2 -pix_fmt yuvj420p -huffman optimal \
       -loglevel quiet "debug/test_stripes_1024x600.jpg"

echo ""
echo "2. 验证生成的图像..."

for file in debug/test_*_1024x600.jpg; do
    if [ -f "$file" ]; then
        size=$(wc -c < "$file")
        echo "   ✅ $(basename "$file"): ${size} bytes"
        
        # 验证图像分辨率
        if command -v identify >/dev/null 2>&1; then
            resolution=$(identify "$file" | grep -o '[0-9]*x[0-9]*')
            echo "      分辨率: $resolution"
        fi
    else
        echo "   ✗ $(basename "$file"): 生成失败"
    fi
done

echo ""
echo "3. 测试建议："
echo "   - 现在运行Go程序: ./videocall-peer --room aa0003"
echo "   - Go程序将循环发送这4种高对比度图案："
echo "     * 黑白条纹 (最容易看见)"
echo "     * 纯白色背景 (测试背光)"
echo "     * 纯红色背景 (高对比度)"
echo "     * 纯蓝色背景 (高对比度)"
echo ""
echo "4. 如果仍然看不到图像："
echo "   - 检查ESP32日志中的fps输出是否持续"
echo "   - 可能是硬件背光问题"
echo "   - 可能需要手动配置LCD背光引脚"
echo ""

echo "=== 测试完成 ==="
