#!/bin/bash

# 1024x600分辨率测试脚本

echo "=== 1024x600分辨率测试 ==="
echo

echo "🔧 分辨率配置更新:"
echo "- 输出分辨率: 1024x600 (匹配ESP32显示分辨率)"
echo "- 输入分辨率: 摄像头默认 (自动检测)"
echo "- 缩放算法: 双线性插值"
echo "- 编码格式: MJPEG"
echo "- 像素格式: YUVJ420P"
echo

echo "📊 分辨率对比:"
echo "- 之前: 320x240 (76,800 像素)"
echo "- 现在: 1024x600 (614,400 像素)"
echo "- 提升: 8倍像素数量"
echo "- 文件大小预期: 约50-150KB (取决于内容复杂度)"
echo

echo "🎯 ESP32兼容性:"
echo "- ESP32显示分辨率: 1024x600"
echo "- 完美匹配，无需ESP32端缩放"
echo "- 更清晰的图像质量"
echo "- 更好的用户体验"
echo

echo "⚠️  注意事项:"
echo "- 文件大小会增加 (约8倍)"
echo "- 网络传输时间会增加"
echo "- 编码时间会稍微增加"
echo "- 但图像质量显著提升"
echo

echo "🚀 启动程序测试1024x600分辨率..."
echo

echo "预期效果:"
echo "✅ 日志显示: 1024x600@15fps"
echo "✅ 成功捕获: 分辨率: 1024x600"
echo "✅ 文件大小: 50-150KB (比之前的15KB大)"
echo "✅ ESP32显示: 更清晰的图像"
echo

# 清理调试文件
rm -f debug/*.jpg debug/*.mjpeg debug/*.pcma debug/*.h264 debug/*.log

echo "✅ 已清理调试文件"
echo

echo "测试步骤:"
echo "1. 程序启动后，输入: join d0002"
echo "2. 启用监控: monitor"
echo "3. 在ESP32上: join d0002"
echo "4. 在ESP32上: b"
echo "5. 在程序中: accept"
echo

echo "关键日志检查:"
echo "- [ASTIAV] 摄像头捕获器已创建: 1024x600@15fps"
echo "- [ASTIAV] 编码器已初始化: MJPEG 1024x600"
echo "- [ASTIAV] 缩放器检查: XXXxYYY -> 1024x600"
echo "- [MEDIA] AstiAV成功捕获摄像头帧: 30 (大小: XXXXX bytes, 分辨率: 1024x600)"
echo

# 启动程序
./videocall-peer

echo
echo "=== 测试完成 ==="

# 检查结果
echo "检查1024x600分辨率结果:"

if ls debug/astiav_frame_*.jpg 1> /dev/null 2>&1; then
    echo "🎉 发现1024x600分辨率的AstiAV摄像头帧:"
    total_size=0
    frame_count=0
    
    for file in debug/astiav_frame_*.jpg; do
        if [ -f "$file" ]; then
            size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
            echo "  📸 $file (${size} bytes)"
            total_size=$((total_size + size))
            frame_count=$((frame_count + 1))
            
            # 使用file命令检查图像信息
            if command -v file >/dev/null 2>&1; then
                file_info=$(file "$file" 2>/dev/null)
                if echo "$file_info" | grep -q "1024 x 600"; then
                    echo "    ✅ 确认分辨率: 1024x600"
                else
                    echo "    ⚠️  分辨率信息: $file_info"
                fi
            fi
        fi
    done
    
    if [ $frame_count -gt 0 ]; then
        avg_size=$((total_size / frame_count))
        echo
        echo "📊 统计信息:"
        echo "  - 帧数: $frame_count"
        echo "  - 总大小: $total_size bytes"
        echo "  - 平均大小: $avg_size bytes/帧"
        echo "  - 质量评估: $(if [ $avg_size -gt 50000 ]; then echo "高质量 (>50KB)"; elif [ $avg_size -gt 20000 ]; then echo "中等质量 (20-50KB)"; else echo "较低质量 (<20KB)"; fi)"
    fi
    
    echo
    echo "🖼️  查看图像命令:"
    echo "   open debug/astiav_frame_*.jpg"
    
else
    echo "❌ 没有找到AstiAV摄像头帧，可能使用了备用方案"
fi

# 检查接收的数据
if [ -f "debug/received_video.mjpeg" ]; then
    size=$(stat -f%z "debug/received_video.mjpeg" 2>/dev/null || stat -c%s "debug/received_video.mjpeg" 2>/dev/null)
    if [ "$size" -gt 0 ]; then
        echo "✅ 接收MJPEG文件: ${size} bytes (ESP32 -> Go)"
        echo "🎬 播放命令:"
        echo "   ffplay -f mjpeg debug/received_video.mjpeg"
    fi
fi

echo
echo "分辨率升级总结:"
echo "✅ 输出分辨率: 320x240 → 1024x600"
echo "✅ 像素数量: 76,800 → 614,400 (8倍提升)"
echo "✅ ESP32兼容: 完美匹配显示分辨率"
echo "✅ 图像质量: 显著提升"
echo
echo "如果文件大小合理 (50-150KB) 且ESP32能正常显示，"
echo "说明1024x600分辨率配置成功！"
