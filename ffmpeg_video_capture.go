package main

import (
	"bufio"
	"context"
	"fmt"
	"image"
	"image/jpeg"
	"io"
	"log"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pion/webrtc/v4"
	"github.com/pion/webrtc/v4/pkg/media"
)

// FFmpeg视频捕获器
type FFmpegVideoCapture struct {
	cmd           *exec.Cmd
	stdout        io.ReadCloser
	cancel        context.CancelFunc
	ctx           context.Context
	width         int
	height        int
	fps           int
	deviceID      string
	isRunning     bool
	mutex         sync.RWMutex
	frameCallback func([]byte)
}

// FFmpeg配置
type FFmpegConfig struct {
	Width    int
	Height   int
	FPS      int
	DeviceID string
	Format   string // "avfoundation" for macOS, "v4l2" for Linux, "dshow" for Windows
}

// 创建FFmpeg视频捕获器
func NewFFmpegVideoCapture(config FFmpegConfig) *FFmpegVideoCapture {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &FFmpegVideoCapture{
		ctx:      ctx,
		cancel:   cancel,
		width:    config.Width,
		height:   config.Height,
		fps:      config.FPS,
		deviceID: config.DeviceID,
	}
}

// 检测可用的摄像头设备
func (f *FFmpegVideoCapture) ListDevices() error {
	log.Println("[INFO] 📹 Detecting available video devices with FFmpeg...")
	
	// 根据操作系统选择不同的命令
	var cmd *exec.Cmd
	
	// 检测操作系统
	if _, err := exec.LookPath("ffmpeg"); err != nil {
		return fmt.Errorf("FFmpeg not found. Please install FFmpeg first")
	}
	
	// macOS
	cmd = exec.Command("ffmpeg", "-f", "avfoundation", "-list_devices", "true", "-i", "")
	
	log.Println("[DEBUG] Running: ffmpeg -f avfoundation -list_devices true -i \"\"")
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("[DEBUG] FFmpeg output: %s", string(output))
		
		// 尝试Linux v4l2
		log.Println("[INFO] Trying Linux v4l2...")
		cmd = exec.Command("ffmpeg", "-f", "v4l2", "-list_devices", "true", "-i", "")
		output, err = cmd.CombinedOutput()
		if err != nil {
			log.Printf("[DEBUG] v4l2 output: %s", string(output))
			return fmt.Errorf("failed to list devices: %v", err)
		}
	}
	
	log.Printf("[INFO] Available devices:\n%s", string(output))
	return nil
}

// 启动视频捕获
func (f *FFmpegVideoCapture) Start() error {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	
	if f.isRunning {
		return fmt.Errorf("capture already running")
	}
	
	log.Printf("[INFO] 🎬 Starting FFmpeg video capture: %dx%d@%dfps", f.width, f.height, f.fps)
	
	// 构建FFmpeg命令
	args := f.buildFFmpegArgs()
	
	log.Printf("[DEBUG] FFmpeg command: ffmpeg %s", strings.Join(args, " "))
	
	f.cmd = exec.CommandContext(f.ctx, "ffmpeg", args...)
	
	// 获取stdout用于读取视频数据
	stdout, err := f.cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %v", err)
	}
	f.stdout = stdout
	
	// 启动FFmpeg进程
	if err := f.cmd.Start(); err != nil {
		return fmt.Errorf("failed to start FFmpeg: %v", err)
	}
	
	f.isRunning = true
	log.Println("[INFO] ✅ FFmpeg video capture started")
	
	// 启动帧读取goroutine
	go f.readFrames()
	
	return nil
}

// 构建FFmpeg参数
func (f *FFmpegVideoCapture) buildFFmpegArgs() []string {
	args := []string{
		"-f", "avfoundation", // macOS输入格式
		"-framerate", strconv.Itoa(f.fps),
		"-video_size", fmt.Sprintf("%dx%d", f.width, f.height),
		"-i", f.deviceID, // 设备ID，如"0"表示默认摄像头
		"-pix_fmt", "yuv420p", // 像素格式
		"-c:v", "rawvideo", // 输出原始视频
		"-f", "rawvideo", // 输出格式
		"-", // 输出到stdout
	}
	
	// 如果设备ID为空，使用默认设备
	if f.deviceID == "" {
		args[5] = "0"
	}
	
	return args
}

// 读取视频帧
func (f *FFmpegVideoCapture) readFrames() {
	defer func() {
		f.mutex.Lock()
		f.isRunning = false
		f.mutex.Unlock()
		log.Println("[INFO] 📹 FFmpeg frame reading stopped")
	}()
	
	// 计算每帧的字节数 (YUV420p格式)
	frameSize := f.width * f.height * 3 / 2
	buffer := make([]byte, frameSize)
	frameCount := 0
	
	log.Printf("[DEBUG] Reading frames, expected frame size: %d bytes", frameSize)
	
	for {
		select {
		case <-f.ctx.Done():
			return
		default:
			// 读取一帧数据
			n, err := io.ReadFull(f.stdout, buffer)
			if err != nil {
				if err != io.EOF {
					log.Printf("[ERROR] Failed to read frame: %v", err)
				}
				return
			}
			
			if n != frameSize {
				log.Printf("[WARN] Incomplete frame read: %d/%d bytes", n, frameSize)
				continue
			}
			
			frameCount++
			if frameCount%30 == 0 { // 每30帧打印一次
				log.Printf("[DEBUG] 📹 Captured frame %d (%d bytes)", frameCount, n)
			}
			
			// 调用回调函数处理帧数据
			if f.frameCallback != nil {
				f.frameCallback(buffer[:n])
			}
			
			// 保存测试帧
			if frameCount%150 == 0 { // 每5秒保存一帧 (30fps)
				f.saveTestFrame(buffer[:n], frameCount)
			}
		}
	}
}

// 保存测试帧为JPEG
func (f *FFmpegVideoCapture) saveTestFrame(yuvData []byte, frameNumber int) {
	// 创建调试目录
	os.MkdirAll("ffmpeg_debug", 0755)
	
	// 将YUV420p转换为RGB (简化版本)
	img := f.yuv420pToRGB(yuvData)
	
	filename := fmt.Sprintf("ffmpeg_debug/frame_%06d.jpg", frameNumber)
	file, err := os.Create(filename)
	if err != nil {
		log.Printf("[WARN] Failed to create frame file: %v", err)
		return
	}
	defer file.Close()
	
	if err := jpeg.Encode(file, img, &jpeg.Options{Quality: 90}); err != nil {
		log.Printf("[WARN] Failed to encode frame: %v", err)
		return
	}
	
	log.Printf("[DEBUG] 💾 Saved real camera frame %d to %s", frameNumber, filename)
}

// YUV420p转RGB (简化实现)
func (f *FFmpegVideoCapture) yuv420pToRGB(yuvData []byte) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, f.width, f.height))
	
	// 简化的YUV到RGB转换
	ySize := f.width * f.height
	uSize := ySize / 4
	
	for y := 0; y < f.height; y++ {
		for x := 0; x < f.width; x++ {
			yIndex := y*f.width + x
			uIndex := ySize + (y/2)*(f.width/2) + (x/2)
			vIndex := ySize + uSize + (y/2)*(f.width/2) + (x/2)
			
			if yIndex >= len(yuvData) || uIndex >= len(yuvData) || vIndex >= len(yuvData) {
				continue
			}
			
			Y := float64(yuvData[yIndex])
			U := float64(yuvData[uIndex]) - 128
			V := float64(yuvData[vIndex]) - 128
			
			// YUV到RGB转换公式
			R := Y + 1.402*V
			G := Y - 0.344*U - 0.714*V
			B := Y + 1.772*U
			
			// 限制范围
			if R < 0 { R = 0 }
			if R > 255 { R = 255 }
			if G < 0 { G = 0 }
			if G > 255 { G = 255 }
			if B < 0 { B = 0 }
			if B > 255 { B = 255 }
			
			img.Set(x, y, image.RGBA{uint8(R), uint8(G), uint8(B), 255})
		}
	}
	
	return img
}

// 设置帧回调函数
func (f *FFmpegVideoCapture) SetFrameCallback(callback func([]byte)) {
	f.frameCallback = callback
}

// 停止捕获
func (f *FFmpegVideoCapture) Stop() {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	
	if !f.isRunning {
		return
	}
	
	log.Println("[INFO] 🛑 Stopping FFmpeg video capture...")
	
	f.cancel()
	
	if f.cmd != nil && f.cmd.Process != nil {
		f.cmd.Process.Kill()
		f.cmd.Wait()
	}
	
	if f.stdout != nil {
		f.stdout.Close()
	}
	
	f.isRunning = false
	log.Println("[INFO] ✅ FFmpeg video capture stopped")
}

// 检查是否正在运行
func (f *FFmpegVideoCapture) IsRunning() bool {
	f.mutex.RLock()
	defer f.mutex.RUnlock()
	return f.isRunning
}
