#!/bin/bash

echo "🎯 Role-Based Behavior Test"
echo "=========================="

# 编译项目
echo "📦 Building project..."
go build -o door-station-esp32 main.go
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"

echo ""
echo "🔧 Key Changes Applied:"
echo "✅ 移除强制设置initiator=true"
echo "✅ 让AppRTC服务器决定角色分配"
echo "✅ 根据实际角色决定offer/answer行为"
echo ""

echo "📋 角色行为逻辑："
echo "如果 IsInitiator=true:"
echo "  → Go程序发送offer，等待浏览器answer"
echo "如果 IsInitiator=false:"
echo "  → Go程序等待浏览器offer，然后发送answer"
echo ""

echo "🔍 AppRTC角色分配规则："
echo "- 第一个加入房间的客户端: IsInitiator=true"
echo "- 后续加入房间的客户端: IsInitiator=false"
echo "- 浏览器通常先加入，所以Go程序通常是non-initiator"
echo ""

echo "🚀 测试步骤："
echo "1. 先打开浏览器页面（让浏览器成为initiator）:"
echo "   https://webrtc.espressif.com/role_test_room"
echo ""
echo "2. 然后启动Go程序（成为non-initiator）:"
echo "   ./door-station-esp32 https://webrtc.espressif.com role_test_room"
echo ""
echo "3. 在Go程序中连接:"
echo "   connect role_test_room"
echo ""
echo "4. 观察日志，应该看到:"
echo "   [INFO] Got room info: ClientID=xxx, IsInitiator=false"
echo "   [INFO] 🎯 As non-initiator, waiting for browser's offer..."
echo "   [INFO] 📋 Received SDP Offer via WebSocket"
echo "   [INFO] 📋 Answer sent successfully"
echo ""

echo "🎯 成功标志:"
echo "- ✅ Go程序被分配为non-initiator"
echo "- ✅ 浏览器发送offer"
echo "- ✅ Go程序接收offer并回复answer"
echo "- ✅ WebRTC连接建立"
echo "- ✅ 浏览器显示视频流"
echo ""

echo "🌐 浏览器测试URL:"
echo "   https://webrtc.espressif.com/role_test_room"
echo ""

echo "💡 关键洞察:"
echo "AppRTC的角色分配基于加入顺序，不是设备类型"
echo "我们之前强制设置initiator=true可能违反了协议"
echo ""

echo "✅ Role-based behavior test ready!"
echo "现在让AppRTC自然分配角色，应该能正常工作！"
